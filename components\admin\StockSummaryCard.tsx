'use client'

import React from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { 
  Package, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Clock,
  BarChart3
} from 'lucide-react'

// Types
interface Product {
  id: string
  name: string
  stockQuantity: number
  minStockThreshold: number
  maxStockCapacity: number
  stockStatus: 'IN_STOCK' | 'LOW_STOCK' | 'OUT_OF_STOCK'
  trackStock: boolean
}

interface StockIndicators {
  isLowStock: boolean
  isOutOfStock: boolean
  isOverCapacity: boolean
  stockPercentage: number | null
  daysUntilReorder: number | null
}

interface StockStatistics {
  totalMovements: number
  netChange: number
  movementsByType: Array<{
    type: string
    totalQuantity: number
    count: number
  }>
}

interface StockSummaryCardProps {
  product: Product
  stockIndicators?: StockIndicators
  statistics?: StockStatistics
  onStockMovement?: () => void
  onViewHistory?: () => void
  className?: string
}

const StockSummaryCard: React.FC<StockSummaryCardProps> = ({
  product,
  stockIndicators,
  statistics,
  onStockMovement,
  onViewHistory,
  className = ''
}) => {
  // Get stock status info
  const getStockStatusInfo = () => {
    if (!product.trackStock) {
      return {
        icon: Package,
        color: 'text-gray-500',
        bgColor: 'bg-gray-100',
        label: 'Takip Edilmiyor',
        variant: 'secondary' as const
      }
    }

    switch (product.stockStatus) {
      case 'OUT_OF_STOCK':
        return {
          icon: XCircle,
          color: 'text-red-600',
          bgColor: 'bg-red-100',
          label: 'Stokta Yok',
          variant: 'destructive' as const
        }
      case 'LOW_STOCK':
        return {
          icon: AlertTriangle,
          color: 'text-orange-600',
          bgColor: 'bg-orange-100',
          label: 'Düşük Stok',
          variant: 'warning' as const
        }
      case 'IN_STOCK':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          label: 'Stokta Var',
          variant: 'success' as const
        }
      default:
        return {
          icon: Package,
          color: 'text-gray-500',
          bgColor: 'bg-gray-100',
          label: 'Bilinmiyor',
          variant: 'secondary' as const
        }
    }
  }

  // Calculate stock percentage
  const getStockPercentage = (): number => {
    if (!product.maxStockCapacity || product.maxStockCapacity <= 0) {
      return 0
    }
    return Math.min(100, Math.max(0, (product.stockQuantity / product.maxStockCapacity) * 100))
  }

  // Get recent movement summary
  const getMovementSummary = () => {
    if (!statistics || statistics.totalMovements === 0) {
      return null
    }

    const inMovements = statistics.movementsByType.find(m => m.type === 'in')
    const outMovements = statistics.movementsByType.find(m => m.type === 'out')

    return {
      totalIn: inMovements?.totalQuantity || 0,
      totalOut: Math.abs(outMovements?.totalQuantity || 0),
      netChange: statistics.netChange
    }
  }

  const statusInfo = getStockStatusInfo()
  const StatusIcon = statusInfo.icon
  const stockPercentage = getStockPercentage()
  const movementSummary = getMovementSummary()

  return (
    <Card className={`${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium truncate" title={product.name}>
            {product.name}
          </CardTitle>
          <div className={`p-2 rounded-lg ${statusInfo.bgColor}`}>
            <StatusIcon className={`h-5 w-5 ${statusInfo.color}`} />
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={statusInfo.variant} className="text-xs">
            {statusInfo.label}
          </Badge>
          {stockIndicators?.daysUntilReorder && stockIndicators.daysUntilReorder > 0 && (
            <Badge variant="outline" className="text-xs flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {stockIndicators.daysUntilReorder} gün
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Stock Levels */}
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">Mevcut Stok</span>
            <span className={`text-lg font-bold ${statusInfo.color}`}>
              {product.stockQuantity || 0}
            </span>
          </div>

          {/* Stock Progress Bar */}
          {product.maxStockCapacity && product.maxStockCapacity > 0 && (
            <div className="space-y-2">
              <div className="flex justify-between text-xs text-gray-500">
                <span>0</span>
                <span>{product.maxStockCapacity}</span>
              </div>
              <Progress 
                value={stockPercentage} 
                className="h-2"
                // Custom color based on stock level
                style={{
                  '--progress-background': stockPercentage <= 20 ? '#ef4444' : 
                                         stockPercentage <= 50 ? '#f97316' : '#22c55e'
                } as React.CSSProperties}
              />
              <div className="text-center text-xs text-gray-500">
                {stockPercentage.toFixed(1)}% dolu
              </div>
            </div>
          )}

          {/* Stock Thresholds */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Min. Eşik:</span>
              <span className="font-medium">{product.minStockThreshold || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Maks. Kapasite:</span>
              <span className="font-medium">{product.maxStockCapacity || '-'}</span>
            </div>
          </div>
        </div>

        {/* Recent Movement Summary */}
        {movementSummary && (
          <div className="border-t pt-3">
            <div className="flex items-center gap-2 mb-2">
              <BarChart3 className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Son 30 Gün</span>
            </div>
            
            <div className="grid grid-cols-3 gap-3 text-sm">
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 text-green-600">
                  <TrendingUp className="h-3 w-3" />
                  <span className="font-medium">+{movementSummary.totalIn}</span>
                </div>
                <div className="text-xs text-gray-500">Giriş</div>
              </div>
              
              <div className="text-center">
                <div className="flex items-center justify-center gap-1 text-red-600">
                  <TrendingDown className="h-3 w-3" />
                  <span className="font-medium">-{movementSummary.totalOut}</span>
                </div>
                <div className="text-xs text-gray-500">Çıkış</div>
              </div>
              
              <div className="text-center">
                <div className={`font-medium ${movementSummary.netChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {movementSummary.netChange >= 0 ? '+' : ''}{movementSummary.netChange}
                </div>
                <div className="text-xs text-gray-500">Net</div>
              </div>
            </div>
          </div>
        )}

        {/* Stock Alerts */}
        {stockIndicators && (
          <div className="space-y-2">
            {stockIndicators.isOutOfStock && (
              <div className="flex items-center gap-2 p-2 bg-red-50 rounded-lg text-red-700 text-sm">
                <XCircle className="h-4 w-4" />
                <span>Stok tükendi!</span>
              </div>
            )}
            
            {stockIndicators.isLowStock && !stockIndicators.isOutOfStock && (
              <div className="flex items-center gap-2 p-2 bg-orange-50 rounded-lg text-orange-700 text-sm">
                <AlertTriangle className="h-4 w-4" />
                <span>Stok seviyesi düşük</span>
              </div>
            )}
            
            {stockIndicators.isOverCapacity && (
              <div className="flex items-center gap-2 p-2 bg-yellow-50 rounded-lg text-yellow-700 text-sm">
                <AlertTriangle className="h-4 w-4" />
                <span>Kapasite aşıldı!</span>
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        {product.trackStock && (
          <div className="flex gap-2 pt-2 border-t">
            <Button
              variant="outline"
              size="sm"
              onClick={onStockMovement}
              className="flex-1"
            >
              <Package className="h-4 w-4 mr-2" />
              Stok Hareketi
            </Button>
            
            {onViewHistory && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onViewHistory}
                className="flex-1"
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                Geçmiş
              </Button>
            )}
          </div>
        )}

        {/* Track Stock Disabled Warning */}
        {!product.trackStock && (
          <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg text-gray-600 text-sm">
            <Package className="h-4 w-4" />
            <span>Bu ürün için stok takibi devre dışı</span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default StockSummaryCard
