import type { ProductImage } from '@/types'

export interface ImageUploadResult {
  id: string
  originalName: string
  filename: string
  url: string
  alt: string
  title: string
  sortOrder: number
  isMain: boolean
  size: number
  width: number
  height: number
  format: string
  variants: ImageVariant[]
}

export interface ImageVariant {
  size: 'original' | 'thumbnail' | 'medium'
  filename: string
  url: string
  width: number
  height: number
  fileSize: number
}

// Upload images to server
export async function uploadImages(files: File[]): Promise<ImageUploadResult[]> {
  const formData = new FormData()
  
  files.forEach(file => {
    formData.append('files', file)
  })

  const response = await fetch('/api/upload', {
    method: 'POST',
    body: formData,
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Upload failed')
  }

  const result = await response.json()
  return result.data
}

// Delete image from server
export async function deleteImage(filename: string): Promise<void> {
  const response = await fetch(`/api/upload?filename=${encodeURIComponent(filename)}`, {
    method: 'DELETE',
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Delete failed')
  }
}

// Convert uploaded image to ProductImage format
export function convertToProductImage(uploadResult: ImageUploadResult, productId?: string): ProductImage {
  return {
    id: uploadResult.id,
    productId: productId,
    url: uploadResult.url,
    alt: uploadResult.alt,
    title: uploadResult.title,
    sortOrder: uploadResult.sortOrder,
    isMain: uploadResult.isMain,
    size: uploadResult.size,
    width: uploadResult.width,
    height: uploadResult.height,
    format: uploadResult.format,
  }
}

// Validate image files before upload
export function validateImageFiles(files: File[]): { valid: boolean; errors: string[] } {
  const errors: string[] = []
  const maxSize = 5 * 1024 * 1024 // 5MB
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']

  if (files.length === 0) {
    errors.push('No files selected')
    return { valid: false, errors }
  }

  if (files.length > 10) {
    errors.push('Maximum 10 files allowed')
  }

  files.forEach((file, index) => {
    if (!allowedTypes.includes(file.type)) {
      errors.push(`File ${index + 1}: Invalid file type. Allowed: JPEG, PNG, WebP`)
    }

    if (file.size > maxSize) {
      errors.push(`File ${index + 1}: File too large. Maximum size: 5MB`)
    }

    if (file.name.length > 100) {
      errors.push(`File ${index + 1}: Filename too long`)
    }
  })

  return { valid: errors.length === 0, errors }
}

// Get image URL with size variant
export function getImageUrl(image: ProductImage, size: 'original' | 'thumbnail' | 'medium' = 'original'): string {
  if (size === 'original') {
    return image.url
  }

  // Generate variant URL based on filename pattern
  const baseName = image.url.replace(/\/uploads\/products\//, '').replace(/-original\.jpg$/, '')
  return `/uploads/products/${baseName}-${size}.jpg`
}

// Create image placeholder
export function createImagePlaceholder(width: number = 400, height: number = 400): ProductImage {
  return {
    id: 'placeholder',
    url: `/placeholder.svg?height=${height}&width=${width}`,
    alt: 'Placeholder image',
    title: 'Placeholder',
    sortOrder: 0,
    isMain: false,
    size: 0,
    width,
    height,
    format: 'svg',
  }
}

// Sort images by sortOrder and isMain
export function sortImages(images: ProductImage[]): ProductImage[] {
  return [...images].sort((a, b) => {
    // Main image first
    if (a.isMain && !b.isMain) return -1
    if (!a.isMain && b.isMain) return 1
    
    // Then by sortOrder
    return a.sortOrder - b.sortOrder
  })
}

// Update image sort order
export function updateImageSortOrder(images: ProductImage[], dragIndex: number, hoverIndex: number): ProductImage[] {
  const updatedImages = [...images]
  const draggedImage = updatedImages[dragIndex]
  
  // Remove dragged image
  updatedImages.splice(dragIndex, 1)
  
  // Insert at new position
  updatedImages.splice(hoverIndex, 0, draggedImage)
  
  // Update sort orders
  return updatedImages.map((image, index) => ({
    ...image,
    sortOrder: index,
  }))
}

// Set main image
export function setMainImage(images: ProductImage[], imageId: string): ProductImage[] {
  return images.map(image => ({
    ...image,
    isMain: image.id === imageId,
  }))
}
