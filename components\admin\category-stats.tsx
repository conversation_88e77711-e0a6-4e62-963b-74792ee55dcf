"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Package, FolderTree, Eye, EyeOff, AlertTriangle } from "lucide-react"
import { formatNumber } from "@/lib/utils"
import type { Category } from "@/types"

interface CategoryStatsProps {
  categories: Category[]
}

export function CategoryStats({ categories }: CategoryStatsProps) {
  const stats = {
    total: categories.length,
    active: categories.filter((cat) => cat.isActive).length,
    inactive: categories.filter((cat) => !cat.isActive).length,
    mainCategories: categories.filter((cat) => !cat.parentId).length,
    subCategories: categories.filter((cat) => cat.parentId).length,
    totalProducts: categories.reduce((sum, cat) => sum + cat.productCount, 0),
    emptyCategories: categories.filter((cat) => cat.productCount === 0).length,
  }

  const statCards = [
    {
      title: "Toplam Kategori",
      value: stats.total,
      icon: FolderTree,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      title: "Akt<PERSON>",
      value: stats.active,
      icon: Eye,
      color: "text-green-600",
      bgColor: "bg-green-50",
    },
    {
      title: "Pasif Kategoriler",
      value: stats.inactive,
      icon: EyeOff,
      color: "text-gray-600",
      bgColor: "bg-gray-50",
    },
    {
      title: "Ana Kategoriler",
      value: stats.mainCategories,
      icon: FolderTree,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
    },
    {
      title: "Alt Kategoriler",
      value: stats.subCategories,
      icon: FolderTree,
      color: "text-indigo-600",
      bgColor: "bg-indigo-50",
    },
    {
      title: "Toplam Ürün",
      value: stats.totalProducts,
      icon: Package,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
    },
  ]

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
      {statCards.map((stat) => {
        const Icon = stat.icon
        return (
          <Card key={stat.title}>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                  <Icon className={`h-5 w-5 ${stat.color}`} />
                </div>
                <div>
                  <div className="text-2xl font-bold text-gray-900">{formatNumber(stat.value)}</div>
                  <div className="text-xs text-gray-600">{stat.title}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        )
      })}

      {/* Warning for empty categories */}
      {stats.emptyCategories > 0 && (
        <Card className="md:col-span-3 lg:col-span-6">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-yellow-50">
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
              </div>
              <div className="flex-1">
                <div className="text-sm font-medium text-gray-900">Ürünsüz Kategoriler</div>
                <div className="text-xs text-gray-600">{stats.emptyCategories} kategoride henüz ürün bulunmuyor</div>
              </div>
              <Badge variant="warning">{stats.emptyCategories} kategori</Badge>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
