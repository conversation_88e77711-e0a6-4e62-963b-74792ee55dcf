const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

// Simple product data that works with current schema
const sampleProducts = [
  {
    name: "3M H-700 Güvenlik Bareti",
    slug: "3m-h700-guvenlik-bareti",
    description: "3M H-700 serisi güvenlik bareti, yüks<PERSON> darbe dayanımı ve konfor sağlar. İnşaat, endüstri ve genel iş güvenliği için idealdir.",
    shortDescription: "Yüksek kaliteli güvenlik bareti",
    brand: "3M",
    model: "H-700",
    basePrice: 65.00,
    baseCostPrice: 45.00,
    taxRate: 20,
    currency: "TRY",
    trackStock: true,
    reservedStock: 0,
    isActive: true,
    isFeatured: true,
    isNew: false,
    isOnSale: false,
    seoTitle: "3M H-700 Güvenlik Bareti - İş Güvenliği",
    seoDescription: "3M H-700 güvenlik bareti ile başınız<PERSON> koruyun. <PERSON><PERSON><PERSON><PERSON> kalite, uygun fiyat ve hızlı teslimat.",
    weight: 0.35,
    dimensions: JSON.stringify({ length: 28, width: 22, height: 15 })
  },
  {
    name: "MSA V-Gard Endüstriyel Kask",
    slug: "msa-v-gard-endustriyel-kask",
    description: "MSA V-Gard endüstriyel güvenlik kaskı, üstün koruma ve dayanıklılık sunar. Ayarlanabilir askı sistemi ile mükemmel uyum.",
    shortDescription: "Endüstriyel güvenlik kaskı",
    brand: "MSA",
    model: "V-Gard",
    basePrice: 95.00,
    baseCostPrice: 65.00,
    taxRate: 20,
    currency: "TRY",
    trackStock: true,
    reservedStock: 0,
    isActive: true,
    isFeatured: false,
    isNew: true,
    isOnSale: false,
    seoTitle: "MSA V-Gard Endüstriyel Kask - İş Güvenliği",
    seoDescription: "MSA V-Gard endüstriyel kask ile maksimum koruma. Profesyonel kalite ve güvenilir performans.",
    weight: 0.42,
    dimensions: JSON.stringify({ length: 30, width: 24, height: 16 })
  },
  {
    name: "Honeywell Fibre-Metal SuperEight",
    slug: "honeywell-fibre-metal-supereight",
    description: "Honeywell Fibre-Metal SuperEight güvenlik bareti, hafif yapısı ve yüksek dayanımı ile öne çıkar. Uzun süreli kulım için ideal.",
    shortDescription: "Hafif ve dayanıklı güvenlik bareti",
    brand: "Honeywell",
    model: "SuperEight",
    basePrice: 120.00,
    baseCostPrice: 85.00,
    taxRate: 20,
    currency: "TRY",
    trackStock: true,
    reservedStock: 0,
    isActive: true,
    isFeatured: false,
    isNew: false,
    isOnSale: true,
    seoTitle: "Honeywell Fibre-Metal SuperEight - İş Güvenliği",
    seoDescription: "Honeywell SuperEight güvenlik bareti. Hafif, dayanıklı ve konforlu tasarım.",
    weight: 0.28,
    dimensions: JSON.stringify({ length: 27, width: 21, height: 14 })
  },
  {
    name: "Uvex Pheos B-WR Güvenlik Kaskı",
    slug: "uvex-pheos-b-wr-guvenlik-kaski",
    description: "Uvex Pheos B-WR güvenlik kaskı, modern tasarım ve üstün koruma özelliklerini bir araya getirir. Havalandırma sistemi ile konfor sağlar.",
    shortDescription: "Modern tasarımlı güvenlik kaskı",
    brand: "Uvex",
    model: "Pheos B-WR",
    basePrice: 75.00,
    baseCostPrice: 52.00,
    taxRate: 20,
    currency: "TRY",
    trackStock: true,
    reservedStock: 0,
    isActive: true,
    isFeatured: true,
    isNew: false,
    isOnSale: false,
    seoTitle: "Uvex Pheos B-WR Güvenlik Kaskı - İş Güvenliği",
    seoDescription: "Uvex Pheos güvenlik kaskı ile modern koruma. Havalandırma sistemi ve ergonomik tasarım.",
    weight: 0.38,
    dimensions: JSON.stringify({ length: 29, width: 23, height: 15 })
  }
]

async function seedBasicProducts() {
  console.log('🌱 Starting basic product seeding...')
  
  try {
    // Get first category for testing
    const category = await prisma.category.findFirst({
      where: { isActive: true }
    })
    
    if (!category) {
      console.log('❌ No categories found. Creating a test category...')
      
      const testCategory = await prisma.category.create({
        data: {
          name: "Baş Koruma",
          slug: "bas-koruma",
          description: "Güvenlik baretleri ve kaskları",
          icon: "",
          parentId: null,
          productCount: 0,
          isActive: true,
          sortOrder: 1,
          seoTitle: "Baş Koruma - İş Güvenliği Ekipmanları",
          seoDescription: "Güvenlik baretleri ve kaskları. Kaliteli ve güvenilir baş koruma ürünleri.",
          cacheKey: "cat_bas_koruma_tr",
          viewCount: 0,
          popularityScore: 0,
          searchKeywords: "baş koruma",
          isSearchable: true,
          categoryImage: null,
          colorCode: "#3B82F6",
          iconType: "SVG",
          metaKeywords: "baş koruma",
          ogTitle: "Baş Koruma - İş Güvenliği",
          ogDescription: "Güvenlik baretleri ve kaskları",
          ogImage: null,
          conversionRate: null,
          avgOrderValue: null,
          minOrderAmount: null,
          commissionRate: null,
          taxRate: null,
          isPromoted: false,
          isFeatured: false,
          adminNotes: null,
          approvalStatus: "ONAYLANDI",
          version: 1,
          changeLog: null,
          createdBy: null,
          mobileIcon: null,
          mobileTemplate: "VARSAYILAN"
        }
      })
      
      console.log(`✅ Created test category: ${testCategory.name}`)
      category = testCategory
    }
    
    console.log(`📦 Using category: ${category.name}`)
    
    // Create products
    let createdCount = 0
    
    for (const productData of sampleProducts) {
      try {
        const product = await prisma.product.create({
          data: {
            ...productData,
            categoryId: category.id,
            publishedAt: new Date()
          }
        })
        
        console.log(`  ✅ Created product: ${product.name}`)
        createdCount++
        
      } catch (error) {
        console.error(`  ❌ Error creating product ${productData.name}:`, error.message)
      }
    }
    
    // Update category product count
    await prisma.category.update({
      where: { id: category.id },
      data: { productCount: createdCount }
    })
    
    console.log(`\n🎉 Basic seeding completed! Created ${createdCount} products.`)
    console.log(`📊 Products are now available in the admin panel.`)
    
  } catch (error) {
    console.error('❌ Error during seeding:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the seeding
seedBasicProducts()
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
