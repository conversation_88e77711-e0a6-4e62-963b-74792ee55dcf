"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { CartSheet } from "@/components/cart/cart-sheet"
import { CartButton } from "@/components/cart/cart-button"
import { Menu, Search, User, Heart } from "lucide-react"

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const navigation = [
    { name: "Ana Sayfa", href: "/" },
    { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", href: "/urunler" },
    { name: "<PERSON><PERSON><PERSON>", href: "/kategoriler" },
    { name: "<PERSON>k<PERSON><PERSON>m<PERSON><PERSON>", href: "/hakkimizda" },
    { name: "<PERSON>let<PERSON>şim", href: "/iletisim" },
  ]

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">İG</span>
            </div>
            <span className="font-bold text-xl text-gray-900">İş Güvenliği</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-sm font-medium text-gray-700 hover:text-blue-600 transition-colors"
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Actions */}
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="icon" className="hidden sm:flex">
              <Search className="w-5 h-5" />
            </Button>

            <Button variant="ghost" size="icon" className="hidden sm:flex">
              <Heart className="w-5 h-5" />
            </Button>

            <Button variant="ghost" size="icon" className="hidden sm:flex">
              <User className="w-5 h-5" />
            </Button>

            <CartSheet>
              <CartButton />
            </CartSheet>

            {/* Mobile Menu */}
            <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="md:hidden">
                  <Menu className="w-5 h-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-80">
                <div className="flex flex-col space-y-4 mt-8">
                  {navigation.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      className="text-lg font-medium text-gray-700 hover:text-blue-600 transition-colors"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      {item.name}
                    </Link>
                  ))}

                  <div className="pt-4 border-t space-y-2">
                    <Button variant="outline" className="w-full justify-start bg-transparent">
                      <Search className="w-4 h-4 mr-2" />
                      Ara
                    </Button>
                    <Button variant="outline" className="w-full justify-start bg-transparent">
                      <Heart className="w-4 h-4 mr-2" />
                      Favoriler
                    </Button>
                    <Button variant="outline" className="w-full justify-start bg-transparent">
                      <User className="w-4 h-4 mr-2" />
                      Hesabım
                    </Button>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </header>
  )
}
