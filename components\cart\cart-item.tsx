"use client"

import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { useCartStore } from "@/lib/stores/cart-store"
import { formatCurrency } from "@/lib/utils"
import { Minus, Plus, Trash2 } from "lucide-react"
import type { CartItem as CartItemType } from "@/lib/stores/cart-store"

interface CartItemProps {
  item: CartItemType
}

export function CartItem({ item }: CartItemProps) {
  const { updateQuantity, removeItem } = useCartStore()

  // Handle both legacy and new cart item formats
  const product = item.product || {
    id: item.id,
    name: item.name,
    price: item.price,
    slug: item.slug,
    images: [{ url: item.image }],
    stock: 999, // Default stock for legacy items
    brand: null
  }
  const quantity = item.quantity

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity <= 0) {
      removeItem(item.id)
    } else {
      updateQuantity(item.id, newQuantity)
    }
  }

  const handleRemove = () => {
    removeItem(item.id)
  }

  return (
    <div className="flex gap-3 py-3">
      {/* Product Image */}
      <div className="relative w-16 h-16 flex-shrink-0">
        <Link href={`/urun/${product.slug}`}>
          <Image
            src={product.images?.[0]?.url || product.images?.[0] || "/placeholder.svg?height=64&width=64"}
            alt={product.name}
            fill
            className="object-cover rounded-md"
            sizes="64px"
          />
        </Link>
      </div>

      {/* Product Info */}
      <div className="flex-1 min-w-0">
        <Link href={`/urun/${product.slug}`}>
          <h4 className="font-medium text-sm text-gray-900 line-clamp-2 hover:text-blue-600 transition-colors">
            {product.name}
          </h4>
        </Link>

        {product.brand && <p className="text-xs text-gray-500 mt-1">{product.brand}</p>}

        <div className="flex items-center justify-between mt-2">
          {/* Price */}
          <div className="flex items-center gap-2">
            <span className="font-medium text-sm">{formatCurrency(product.price)}</span>
            {product.originalPrice && product.originalPrice > product.price && (
              <span className="text-xs text-gray-500 line-through">{formatCurrency(product.originalPrice)}</span>
            )}
          </div>

          {/* Remove Button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={handleRemove}
            className="h-6 w-6 text-gray-400 hover:text-red-600"
          >
            <Trash2 className="w-3 h-3" />
          </Button>
        </div>

        {/* Quantity Controls */}
        <div className="flex items-center justify-between mt-3">
          <div className="flex items-center gap-1">
            <Button
              variant="outline"
              size="icon"
              onClick={() => handleQuantityChange(quantity - 1)}
              disabled={quantity <= 1}
              className="h-7 w-7"
            >
              <Minus className="w-3 h-3" />
            </Button>

            <span className="w-8 text-center text-sm font-medium">{quantity}</span>

            <Button
              variant="outline"
              size="icon"
              onClick={() => handleQuantityChange(quantity + 1)}
              disabled={product.stock && quantity >= product.stock}
              className="h-7 w-7"
            >
              <Plus className="w-3 h-3" />
            </Button>
          </div>

          {/* Total Price */}
          <span className="font-medium text-sm">{formatCurrency(product.price * quantity)}</span>
        </div>

        {/* Stock Warning */}
        {product.stock && quantity >= product.stock && (
          <p className="text-xs text-orange-600 mt-1">Maksimum stok: {product.stock} adet</p>
        )}
      </div>
    </div>
  )
}
