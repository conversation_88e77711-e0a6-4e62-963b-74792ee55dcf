import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { UnifiedStockService } from '@/lib/services/UnifiedStockService'

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing Stock Capacity Management...')

    // Get a test product
    const testProduct = await prisma.product.findFirst({
      where: { trackStock: true },
      select: {
        id: true,
        name: true,
        stockQuantity: true,
        maxStockCapacity: true,
        minStockThreshold: true
      }
    })

    if (!testProduct) {
      return NextResponse.json({
        success: false,
        error: 'No test product found'
      }, { status: 404 })
    }

    console.log('📦 Test Product:', {
      name: testProduct.name,
      currentStock: testProduct.stockQuantity,
      maxCapacity: testProduct.maxStockCapacity
    })

    const testResults = []

    // Test 1: Valid stock addition within capacity
    console.log('\n📋 Test 1: Valid stock addition within capacity')
    try {
      const currentStock = testProduct.stockQuantity || 0
      const maxCapacity = testProduct.maxStockCapacity || 100
      const addQuantity = Math.min(5, maxCapacity - currentStock)

      if (addQuantity > 0) {
        const validation = UnifiedStockService.validateStockOperation(
          currentStock,
          addQuantity,
          'add',
          maxCapacity
        )

        if (validation.isValid) {
          console.log('✅ Valid addition test passed')
          testResults.push({
            test: 'Valid Stock Addition',
            status: 'PASSED',
            details: {
              currentStock,
              addQuantity,
              newStock: validation.newStock,
              maxCapacity
            }
          })
        } else {
          throw new Error(`Validation failed: ${validation.error}`)
        }
      } else {
        console.log('⚠️ Product at max capacity, skipping addition test')
        testResults.push({
          test: 'Valid Stock Addition',
          status: 'SKIPPED',
          reason: 'Product at max capacity'
        })
      }
    } catch (error: any) {
      console.log('❌ Valid addition test failed:', error.message)
      testResults.push({
        test: 'Valid Stock Addition',
        status: 'FAILED',
        error: error.message
      })
    }

    // Test 2: Invalid stock addition exceeding capacity
    console.log('\n📋 Test 2: Invalid stock addition exceeding capacity')
    try {
      const currentStock = testProduct.stockQuantity || 0
      const maxCapacity = testProduct.maxStockCapacity || 100
      const excessQuantity = maxCapacity - currentStock + 10 // Exceed by 10

      const validation = UnifiedStockService.validateStockOperation(
        currentStock,
        excessQuantity,
        'add',
        maxCapacity
      )

      if (!validation.isValid) {
        console.log('✅ Capacity validation working correctly')
        testResults.push({
          test: 'Capacity Validation',
          status: 'PASSED',
          details: {
            currentStock,
            excessQuantity,
            maxCapacity,
            error: validation.error,
            availableCapacity: validation.availableCapacity
          }
        })
      } else {
        throw new Error('Validation should have failed but passed')
      }
    } catch (error: any) {
      console.log('❌ Capacity validation test failed:', error.message)
      testResults.push({
        test: 'Capacity Validation',
        status: 'FAILED',
        error: error.message
      })
    }

    // Test 3: Available capacity calculation
    console.log('\n📋 Test 3: Available capacity calculation')
    try {
      const currentStock = testProduct.stockQuantity || 0
      const maxCapacity = testProduct.maxStockCapacity || 100
      
      const availableCapacity = UnifiedStockService.getAvailableCapacity(currentStock, maxCapacity)
      const expectedCapacity = Math.max(0, maxCapacity - currentStock)

      if (availableCapacity === expectedCapacity) {
        console.log('✅ Available capacity calculation correct')
        testResults.push({
          test: 'Available Capacity Calculation',
          status: 'PASSED',
          details: {
            currentStock,
            maxCapacity,
            availableCapacity,
            expectedCapacity
          }
        })
      } else {
        throw new Error(`Expected ${expectedCapacity}, got ${availableCapacity}`)
      }
    } catch (error: any) {
      console.log('❌ Available capacity test failed:', error.message)
      testResults.push({
        test: 'Available Capacity Calculation',
        status: 'FAILED',
        error: error.message
      })
    }

    // Test 4: API endpoint capacity validation
    console.log('\n📋 Test 4: API endpoint capacity validation')
    try {
      const currentStock = testProduct.stockQuantity || 0
      const maxCapacity = testProduct.maxStockCapacity || 100
      const excessQuantity = maxCapacity - currentStock + 5

      if (excessQuantity > 0) {
        const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3001'}/api/stock-movements`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            productId: testProduct.id,
            movementType: 'in',
            quantity: excessQuantity,
            reason: 'test',
            notes: 'Capacity test',
            createdBy: 'test'
          })
        })

        const result = await response.json()

        if (response.status === 400 && result.error === 'Kapasite Aşımı') {
          console.log('✅ API capacity validation working')
          testResults.push({
            test: 'API Capacity Validation',
            status: 'PASSED',
            details: {
              expectedError: 'Kapasite Aşımı',
              actualError: result.error,
              validation: result.validation
            }
          })
        } else {
          throw new Error(`Expected 400 with capacity error, got ${response.status}: ${result.error}`)
        }
      } else {
        testResults.push({
          test: 'API Capacity Validation',
          status: 'SKIPPED',
          reason: 'No excess quantity to test'
        })
      }
    } catch (error: any) {
      console.log('❌ API capacity validation test failed:', error.message)
      testResults.push({
        test: 'API Capacity Validation',
        status: 'FAILED',
        error: error.message
      })
    }

    const passedTests = testResults.filter(t => t.status === 'PASSED').length
    const totalTests = testResults.filter(t => t.status !== 'SKIPPED').length

    console.log('\n🎯 Stock Capacity Test Summary:')
    console.log(`Passed: ${passedTests}/${totalTests}`)

    return NextResponse.json({
      success: passedTests === totalTests,
      message: `Stock capacity tests completed: ${passedTests}/${totalTests} passed`,
      testProduct: {
        id: testProduct.id,
        name: testProduct.name,
        currentStock: testProduct.stockQuantity,
        maxCapacity: testProduct.maxStockCapacity,
        availableCapacity: UnifiedStockService.getAvailableCapacity(
          testProduct.stockQuantity || 0, 
          testProduct.maxStockCapacity
        )
      },
      summary: {
        total: testResults.length,
        passed: passedTests,
        failed: testResults.filter(t => t.status === 'FAILED').length,
        skipped: testResults.filter(t => t.status === 'SKIPPED').length
      },
      results: testResults
    })

  } catch (error: any) {
    console.error('❌ Stock capacity test failed:', error)
    return NextResponse.json({
      success: false,
      error: 'Stock capacity test execution failed',
      details: error.message
    }, { status: 500 })
  }
}
