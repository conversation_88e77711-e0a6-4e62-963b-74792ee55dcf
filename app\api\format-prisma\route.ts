import { NextRequest, NextResponse } from 'next/server'
import { exec } from 'child_process'
import { promisify } from 'util'

const execAsync = promisify(exec)

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 Formatting Prisma schema...')

    // Execute prisma format command
    const { stdout, stderr } = await execAsync('npx prisma format', {
      cwd: process.cwd(),
      timeout: 30000 // 30 seconds timeout
    })

    console.log('✅ Prisma format completed successfully!')
    console.log('STDOUT:', stdout)
    if (stderr) {
      console.log('STDERR:', stderr)
    }

    // Now try to generate
    console.log('🔧 Now generating Prisma client...')
    const { stdout: genStdout, stderr: genStderr } = await execAsync('npx prisma generate', {
      cwd: process.cwd(),
      timeout: 60000 // 60 seconds timeout
    })

    console.log('✅ Prisma generate completed successfully!')
    console.log('GENERATE STDOUT:', genStdout)
    if (genStderr) {
      console.log('GENERATE STDERR:', genStderr)
    }

    // Test that the new fields are now accessible
    console.log('🔍 Testing stock field access after generation...')
    const { prisma } = await import('@/lib/prisma')
    
    const testProduct = await prisma.product.findFirst({
      select: {
        id: true,
        name: true,
        stockQuantity: true,
        minStockThreshold: true,
        maxStockCapacity: true,
        trackStock: true,
        basePrice: true
      }
    })

    console.log('✅ Stock fields are now accessible via Prisma client!')
    console.log('Sample product:', testProduct)

    return NextResponse.json({
      success: true,
      message: 'Prisma schema formatted and client regenerated successfully',
      formatOutput: { stdout, stderr },
      generateOutput: { stdout: genStdout, stderr: genStderr },
      testProduct: testProduct,
      nextSteps: [
        'Prisma schema has been formatted',
        'Prisma client has been regenerated',
        'New stock fields are now available in the client',
        'Stock fields tested successfully',
        'Admin panel should now work correctly'
      ]
    })

  } catch (error: any) {
    console.error('❌ Prisma format/generate failed:', error)
    return NextResponse.json({
      success: false,
      error: error.message,
      details: 'Failed to format schema or regenerate Prisma client',
      stdout: error.stdout || '',
      stderr: error.stderr || ''
    }, { status: 500 })
  }
}
