const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function verifyData() {
  try {
    console.log('🔍 Verifying database data...')
    
    // Check categories
    const categoryCount = await prisma.category.count()
    console.log(`📂 Categories: ${categoryCount}`)
    
    if (categoryCount > 0) {
      const categories = await prisma.category.findMany({
        select: { id: true, name: true, productCount: true, isActive: true },
        orderBy: { sortOrder: 'asc' }
      })
      
      console.log('\n📋 Categories:')
      categories.forEach(cat => {
        console.log(`  - ${cat.name} (${cat.productCount} products, active: ${cat.isActive})`)
      })
    }
    
    // Check products
    const productCount = await prisma.product.count()
    console.log(`\n📦 Products: ${productCount}`)
    
    if (productCount > 0) {
      const products = await prisma.product.findMany({
        select: { 
          id: true, 
          name: true, 
          price: true, 
          isActive: true,
          category: { select: { name: true } }
        },
        take: 5
      })
      
      console.log('\n📋 Sample Products:')
      products.forEach(prod => {
        console.log(`  - ${prod.name} (${prod.price} TL, category: ${prod.category.name}, active: ${prod.isActive})`)
      })
    }
    
    console.log('\n✅ Data verification completed!')
    
  } catch (error) {
    console.error('❌ Error verifying data:', error)
  } finally {
    await prisma.$disconnect()
  }
}

verifyData()
