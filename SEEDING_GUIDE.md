# 🌱 Database Seeding Guide

This guide explains how to populate your database with comprehensive test data for the enhanced product management system.

## 📋 What Gets Seeded

### 🏷️ Categories (8 categories)
- **Baş Koruma** - Güvenlik baretleri ve kaskları
- **Göz Koruma** - Güvenlik gözlükleri ve yüz koruyucuları  
- **<PERSON>lak Koruma** - Kulaklıklar ve kulak tıkaçları
- **Solunum Koruma** - Maskeler ve solunum cihazları
- **El Koruma** - İş eldivenleri ve el koruyucuları
- **Ayak Koruma** - Güvenlik ayakkabıları ve botları
- **Vücut Koruma** - İş kıyafetleri ve koruyucu tulumlar
- **Yüksekte Çalışma** - Emniyet kemerleri ve yüksekte çalışma ekipmanları

### 📦 Products (32 products total, 4 per category)
Each product includes:
- **Enhanced Pricing Structure**:
  - `costPrice` (Alış fiyatı) - 60-80% of base price
  - `basePrice` (Normal satış fiyatı) - Main selling price
  - `zakatAmount` (Zekat tutarı) - 2-8 TL for products over 100 TL
  - `taxRate` - 20% VAT rate

- **Stock Management**:
  - `stockQuantity` - Current stock (0-150 units)
  - `minStockThreshold` - Low stock alert level (5-20 units)
  - `maxStockCapacity` - Maximum storage capacity (25-200 units)
  - `stockLocation` - Warehouse location (Depo A/B/C, Ana Depo)
  - `reorderPoint` - Automatic reorder trigger
  - `reorderQuantity` - Quantity to reorder

- **Product Status Flags**:
  - `isActive` - All products active
  - `isFeatured` - 30% chance of being featured
  - `isNew` - 20% chance of being new
  - `isOnSale` - 25% chance of being on sale

- **SEO Optimization**:
  - `seoTitle` - Optimized page titles
  - `seoDescription` - Meta descriptions
  - `metaKeywords` - Category-specific keywords

### 💰 Sample Discounts (30% of products)
- **Yaz İndirimi** - 15% discount (June-August)
- **Erken Kuş İndirimi** - 25% discount (All year, limited to 100 uses)
- **50 TL İndirim** - Fixed 50 TL discount (July only, 50 uses)

### 🔐 Admin User
- **Email**: <EMAIL>
- **Password**: admin123
- **Role**: SUPER_ADMIN

## 🚀 How to Run the Seeder

### Option 1: Run Complete Seeder (Recommended)
```bash
# Install dependencies if not already done
npm install

# Run the comprehensive seeder
npm run seed:all
```

### Option 2: Run Individual Seeders
```bash
# Seed only products (requires existing categories)
npx tsx prisma/seed-products.ts

# Run original seeder (categories + basic products)
npx prisma db seed
```

### Option 3: Reset and Reseed
```bash
# Reset database and reseed everything
npx prisma db reset
npm run seed:all
```

## 📊 Expected Results

After seeding, you should have:
- ✅ **8 Categories** with proper SEO and metadata
- ✅ **32 Products** with realistic pricing and stock data
- ✅ **~10 Discounts** for testing the automatic pricing system
- ✅ **1 Admin User** for accessing the admin panel
- ✅ **5 Stock Scenarios** (High Stock, Normal, Low, Critical, Out of Stock)

## 🧪 Testing the New Features

### 1. **Automatic Pricing System**
- Open ProductFormModal
- Enter a `basePrice` (e.g., 200 TL)
- See real-time calculation of current price with active discounts
- Test profit margin calculations

### 2. **Stock Management**
- View products with different stock levels
- Check stock status indicators (In Stock/Low Stock/Out of Stock)
- Test stock threshold alerts

### 3. **Enhanced Product Data**
- Browse products with realistic Turkish names and descriptions
- Check SEO fields and metadata
- Test category-specific product filtering

## 🔧 Customization

### Adding More Products
Edit `prisma/seed-products.ts` and add products to the `productTemplates` object:

```typescript
"1": { // Category ID
  products: [
    {
      name: "Your Product Name",
      brand: "Brand Name", 
      model: "Model Number",
      description: "Detailed description...",
      shortDescription: "Short description",
      priceRange: { min: 50, max: 100 }
    }
  ]
}
```

### Modifying Stock Scenarios
Edit the `stockScenarios` array in `prisma/seed-products.ts`:

```typescript
const stockScenarios = [
  { 
    name: "Custom Scenario", 
    stockQuantity: 75, 
    minStockThreshold: 15, 
    maxStockCapacity: 100 
  }
]
```

### Adding New Discounts
Edit the `sampleDiscounts` array:

```typescript
{
  name: "Black Friday",
  type: "PERCENTAGE",
  value: 40,
  isActive: true,
  startDate: new Date('2024-11-29').toISOString(),
  endDate: new Date('2024-12-02').toISOString(),
  usageLimit: 200
}
```

## 🐛 Troubleshooting

### Common Issues

1. **"No categories found"**
   - Run `npm run seed:all` instead of individual seeders
   - Or run category seeding first

2. **Database connection errors**
   - Check your `DATABASE_URL` in `.env`
   - Ensure PostgreSQL is running

3. **Permission errors**
   - Make sure the script is executable: `chmod +x scripts/seed-all.ts`

4. **TypeScript errors**
   - Install tsx: `npm install -g tsx`
   - Or use: `npx tsx scripts/seed-all.ts`

### Verification Commands

```bash
# Check seeded data
npx prisma studio

# Count records
npx prisma db execute --stdin <<< "
SELECT 
  (SELECT COUNT(*) FROM categories) as categories,
  (SELECT COUNT(*) FROM products) as products,
  (SELECT COUNT(*) FROM product_discounts) as discounts;
"
```

## 🎯 Next Steps

After seeding:
1. Start the development server: `npm run dev`
2. Login to admin panel with `<EMAIL> / admin123`
3. Test the ProductFormModal with the new pricing system
4. Verify stock management features
5. Check discount calculations in the pricing preview

The seeded data provides a comprehensive foundation for testing all the enhanced features of the product management system! 🎉
