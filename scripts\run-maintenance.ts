#!/usr/bin/env tsx

/**
 * Maintenance Task Runner
 * 
 * This script can be run manually or scheduled as a cron job
 * 
 * Usage:
 * npm run maintenance:cleanup
 * npm run maintenance:all
 * npx tsx scripts/run-maintenance.ts cleanup
 */

import { 
  cleanupExpiredReservations,
  updateStockStatuses,
  generateLowStockAlerts,
  optimizeDatabase,
  healthCheck,
  runAllMaintenanceTasks
} from '../lib/jobs/cart-maintenance'

const TASKS = {
  cleanup: cleanupExpiredReservations,
  stock: updateStockStatuses,
  alerts: generateLowStockAlerts,
  optimize: optimizeDatabase,
  health: healthCheck,
  all: runAllMaintenanceTasks
} as const

type TaskName = keyof typeof TASKS

async function runTask(taskName: TaskName) {
  console.log(`🚀 Running task: ${taskName}`)
  console.log(`⏰ Started at: ${new Date().toISOString()}`)
  
  const startTime = Date.now()
  
  try {
    const result = await TASKS[taskName]()
    const duration = Date.now() - startTime
    
    console.log(`✅ Task completed in ${duration}ms`)
    console.log('📊 Result:', JSON.stringify(result, null, 2))
    
    process.exit(0)
  } catch (error) {
    const duration = Date.now() - startTime
    
    console.error(`❌ Task failed after ${duration}ms`)
    console.error('Error:', error)
    
    process.exit(1)
  }
}

function showUsage() {
  console.log('🔧 Cart & Stock Maintenance Task Runner')
  console.log('')
  console.log('Usage: npx tsx scripts/run-maintenance.ts <task>')
  console.log('')
  console.log('Available tasks:')
  console.log('  cleanup  - Clean up expired reservations and carts')
  console.log('  stock    - Update product stock statuses')
  console.log('  alerts   - Generate low stock alerts')
  console.log('  optimize - Optimize database performance')
  console.log('  health   - Run system health check')
  console.log('  all      - Run all maintenance tasks')
  console.log('')
  console.log('Examples:')
  console.log('  npx tsx scripts/run-maintenance.ts cleanup')
  console.log('  npx tsx scripts/run-maintenance.ts all')
}

async function main() {
  const taskName = process.argv[2] as TaskName
  
  if (!taskName) {
    showUsage()
    process.exit(1)
  }
  
  if (!TASKS[taskName]) {
    console.error(`❌ Unknown task: ${taskName}`)
    console.error('')
    showUsage()
    process.exit(1)
  }
  
  await runTask(taskName)
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Maintenance script failed:', error)
    process.exit(1)
  })
}

export { main as runMaintenance }
