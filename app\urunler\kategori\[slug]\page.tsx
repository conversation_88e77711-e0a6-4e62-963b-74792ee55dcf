import { Suspense } from "react"
import type { Metada<PERSON> } from "next"
import { notFound } from "next/navigation"
import ProductGrid from "@/components/products/product-grid"
import ProductFilters from "@/components/products/product-filters"
import ProductSort from "@/components/products/product-sort"
import ProductSearch from "@/components/products/product-search"
import { Skeleton } from "@/components/ui/skeleton"
import { getCategoryBySlug } from "@/lib/api/categories"
import { CategoryBanner } from "@/components/category-banner"
import { getCategoryImageUrl } from "@/lib/category-image-utils"
import { Header } from "@/components/header"
import { Footer } from "@/components/footer"

interface CategoryProductsPageProps {
  params: { slug: string }
  searchParams: {
    search?: string
    brand?: string
    minPrice?: string
    maxPrice?: string
    inStock?: string
    sort?: string
    page?: string
  }
}

export async function generateMetadata({ params }: CategoryProductsPageProps): Promise<Metadata> {
  try {
    const category = await getCategoryBySlug(params.slug)

    return {
      title: category.seoTitle || `${category.name} | İş Güvenliği Mağazası`,
      description: category.seoDescription || category.description,
      keywords: category.metaKeywords || `${category.name}, iş güvenliği, kişisel koruyucu donanım`,
      openGraph: {
        title: category.ogTitle || category.name,
        description: category.ogDescription || category.description,
        images: category.ogImage ? [
          {
            url: category.ogImage,
            width: 1200,
            height: 630,
            alt: category.name,
          }
        ] : [],
        type: "website",
        url: `/urunler/kategori/${category.slug}`,
      },
      twitter: {
        card: "summary_large_image",
        title: category.ogTitle || category.name,
        description: category.ogDescription || category.description,
        images: category.ogImage ? [category.ogImage] : [],
      },
      alternates: {
        canonical: `/urunler/kategori/${category.slug}`,
      },
    }
  } catch {
    return {
      title: "Kategori Bulunamadı | İş Güvenliği Mağazası",
      description: "Aradığınız kategori bulunamadı.",
    }
  }
}

export default async function CategoryProductsPage({ params, searchParams }: CategoryProductsPageProps) {
  let category

  try {
    category = await getCategoryBySlug(params.slug)
  } catch (error) {
    console.error("Category not found:", error)
    notFound()
  }

  // Pass category ID to search params for product filtering
  const searchParamsWithCategory = {
    ...searchParams,
    category: category.id,
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main className="bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          {/* Breadcrumb */}
          <nav className="mb-6">
            <ol className="flex items-center space-x-2 text-sm text-gray-600">
              <li>
                <a href="/" className="hover:text-blue-600">
                  Ana Sayfa
                </a>
              </li>
              <li>/</li>
              <li>
                <a href="/urunler" className="hover:text-blue-600">
                  Ürünler
                </a>
              </li>
              <li>/</li>
              <li className="text-gray-900 font-medium">{category.name}</li>
            </ol>
          </nav>

          {/* Kategori Banner */}
          <CategoryBanner category={category} />

          {/* Kategori Başlığı (Banner yoksa göster) */}
          {!category.bannerImage && (
            <div className="mb-8">
              <div className="flex items-center gap-3 mb-2">
                {category.icon && <span className="text-2xl">{category.icon}</span>}
                <h1 className="text-3xl font-bold text-gray-900">{category.name}</h1>
              </div>
              <p className="text-gray-600">{category.description}</p>
              {category.productCount > 0 && (
                <p className="text-sm text-gray-500 mt-2">{category.productCount} ürün bulundu</p>
              )}
            </div>
          )}

          {/* Alt Kategoriler */}
          {category.children && category.children.length > 0 && (
            <div className="mb-8">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Alt Kategoriler</h2>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {category.children.map((child) => (
                  <a
                    key={child.id}
                    href={`/urunler/kategori/${child.slug}`}
                    className="bg-white rounded-lg border p-4 text-center hover:shadow-md transition-shadow group"
                  >
                    {/* Alt kategori resmi varsa göster */}
                    {child.bannerImage ? (
                      <div className="w-12 h-12 mx-auto mb-2 rounded-lg overflow-hidden">
                        <img
                          src={getCategoryImageUrl(child.bannerImage, 'thumbnail')}
                          alt={child.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ) : child.icon ? (
                      <span className="text-xl mb-2 block">{child.icon}</span>
                    ) : (
                      <div className="w-12 h-12 mx-auto mb-2 bg-orange-100 rounded-lg flex items-center justify-center">
                        <span className="text-orange-600 text-lg font-medium">
                          {child.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    )}
                    <span className="text-sm font-medium text-gray-900 group-hover:text-orange-600 transition-colors">
                      {child.name}
                    </span>
                  </a>
                ))}
              </div>
            </div>
          )}

          {/* Arama */}
          <div className="mb-6">
            <Suspense fallback={<Skeleton className="h-12 w-full max-w-md" />}>
              <ProductSearch initialValue={searchParams.search} />
            </Suspense>
          </div>

          <div className="flex flex-col lg:flex-row gap-8">
            {/* Filtreler */}
            <aside className="lg:w-64 flex-shrink-0">
              <div className="bg-white rounded-lg shadow-sm border p-6 sticky top-4">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Filtreler</h2>
                <Suspense
                  fallback={
                    <div className="space-y-4">
                      <Skeleton className="h-8 w-full" />
                      <Skeleton className="h-32 w-full" />
                      <Skeleton className="h-24 w-full" />
                    </div>
                  }
                >
                  <ProductFilters searchParams={searchParamsWithCategory} />
                </Suspense>
              </div>
            </aside>

            {/* Ana İçerik */}
            <main className="flex-1">
              {/* Sıralama */}
              <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <div className="text-sm text-gray-600">{category.name} kategorisindeki ürünler</div>
                  <Suspense fallback={<Skeleton className="h-10 w-48" />}>
                    <ProductSort currentSort={searchParams.sort} />
                  </Suspense>
                </div>
              </div>

              {/* Ürün Grid */}
              <Suspense
                fallback={
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {Array.from({ length: 12 }).map((_, i) => (
                      <div key={i} className="bg-white rounded-lg shadow-sm border overflow-hidden">
                        <Skeleton className="h-48 w-full" />
                        <div className="p-4 space-y-2">
                          <Skeleton className="h-4 w-3/4" />
                          <Skeleton className="h-4 w-1/2" />
                          <Skeleton className="h-6 w-1/3" />
                        </div>
                      </div>
                    ))}
                  </div>
                }
              >
                <ProductGrid searchParams={searchParamsWithCategory} />
              </Suspense>
            </main>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  )
}
