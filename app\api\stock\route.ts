import { NextRequest, NextResponse } from 'next/server'
import { UnifiedStockService } from '@/lib/services/UnifiedStockService'
import { z } from 'zod'

// Validation schemas
const GetStockSchema = z.object({
  productIds: z.string().transform(str => str.split(',').filter(Boolean))
})

const UpdateStockSettingsSchema = z.object({
  productId: z.string().min(1),
  minStockThreshold: z.number().int().min(0).optional(),
  maxStockCapacity: z.number().int().min(0).optional(),
  trackStock: z.boolean().optional(),
  stockLocation: z.string().optional(),
  reorderPoint: z.number().int().min(0).optional(),
  reorderQuantity: z.number().int().min(0).optional()
})

// GET /api/stock - Get stock information for products
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const productId = searchParams.get('productId')
    const productIds = searchParams.get('productIds')

    if (productId) {
      // Single product stock
      const stockInfo = await UnifiedStockService.getProductStock(productId)
      
      if (!stockInfo) {
        return NextResponse.json({
          success: false,
          error: 'Product not found'
        }, { status: 404 })
      }

      return NextResponse.json({
        success: true,
        data: stockInfo
      })
    } else if (productIds) {
      // Multiple products stock
      const query = GetStockSchema.parse({ productIds })
      const stockInfoMap = await UnifiedStockService.getMultipleProductsStock(query.productIds)

      return NextResponse.json({
        success: true,
        data: stockInfoMap
      })
    } else {
      return NextResponse.json({
        success: false,
        error: 'productId or productIds parameter is required'
      }, { status: 400 })
    }

  } catch (error: any) {
    console.error('❌ Stock GET error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Validation error',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      error: 'Failed to fetch stock information',
      details: error.message
    }, { status: 500 })
  }
}

// PATCH /api/stock - Update stock settings (not quantity)
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = UpdateStockSettingsSchema.parse(body)

    const updatedStock = await UnifiedStockService.updateStockSettings(
      validatedData.productId,
      validatedData
    )

    if (!updatedStock) {
      return NextResponse.json({
        success: false,
        error: 'Product not found'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      message: 'Stock settings updated successfully',
      data: updatedStock
    })

  } catch (error: any) {
    console.error('❌ Stock PATCH error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Validation error',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      error: 'Failed to update stock settings',
      details: error.message
    }, { status: 500 })
  }
}
