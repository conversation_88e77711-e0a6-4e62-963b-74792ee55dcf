import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { createSuccessResponse, createErrorResponse } from "@/lib/api-utils"
import type { CategoryBanner } from "@/types"

// GET /api/category-banners - Tüm banner'ları getir (admin için)
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const categoryId = searchParams.get('categoryId')
    const isActive = searchParams.get('isActive')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    const where: any = {}
    
    if (categoryId) {
      where.categoryId = categoryId
    }
    
    if (isActive !== null) {
      where.isActive = isActive === 'true'
    }

    // Aktif banner'lar i<PERSON>in tarih kontrol<PERSON>
    if (isActive === 'true') {
      const now = new Date()
      where.OR = [
        { startDate: null, endDate: null },
        { startDate: { lte: now }, endDate: null },
        { startDate: null, endDate: { gte: now } },
        { startDate: { lte: now }, endDate: { gte: now } }
      ]
    }

    const [banners, total] = await Promise.all([
      prisma.categoryBanner.findMany({
        where,
        include: {
          category: {
            select: {
              id: true,
              name: true,
              slug: true
            }
          }
        },
        orderBy: [
          { displayOrder: 'asc' },
          { createdAt: 'desc' }
        ],
        skip,
        take: limit
      }),
      prisma.categoryBanner.count({ where })
    ])

    const pagination = {
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
      hasNext: page < Math.ceil(total / limit),
      hasPrev: page > 1
    }

    return NextResponse.json(createSuccessResponse({
      data: banners,
      pagination
    }))
  } catch (error) {
    console.error("CategoryBanners GET error:", error)
    return NextResponse.json(
      createErrorResponse("Banner'lar yüklenirken hata oluştu"),
      { status: 500 }
    )
  }
}

// POST /api/category-banners - Yeni banner oluştur
export async function POST(request: Request) {
  try {
    const body = await request.json()
    
    // Validation
    if (!body.categoryId || !body.imageUrl) {
      return NextResponse.json(
        createErrorResponse("Kategori ID ve resim URL'si gerekli"),
        { status: 400 }
      )
    }

    // Kategori var mı kontrol et
    const category = await prisma.category.findUnique({
      where: { id: body.categoryId }
    })

    if (!category) {
      return NextResponse.json(
        createErrorResponse("Kategori bulunamadı"),
        { status: 404 }
      )
    }

    // Aynı kategoride aynı sırada banner var mı kontrol et
    if (body.displayOrder !== undefined) {
      const existingBanner = await prisma.categoryBanner.findFirst({
        where: {
          categoryId: body.categoryId,
          displayOrder: body.displayOrder,
          isActive: true
        }
      })

      if (existingBanner) {
        // Mevcut banner'ların sırasını kaydır
        await prisma.categoryBanner.updateMany({
          where: {
            categoryId: body.categoryId,
            displayOrder: { gte: body.displayOrder }
          },
          data: {
            displayOrder: { increment: 1 }
          }
        })
      }
    }

    const banner = await prisma.categoryBanner.create({
      data: {
        categoryId: body.categoryId,
        imageUrl: body.imageUrl,
        imageAlt: body.imageAlt,
        mobileImageUrl: body.mobileImageUrl,
        title: body.title,
        subtitle: body.subtitle,
        description: body.description,
        ctaText: body.ctaText,
        ctaUrl: body.ctaUrl,
        displayOrder: body.displayOrder || 0,
        isActive: body.isActive !== undefined ? body.isActive : true,
        startDate: body.startDate ? new Date(body.startDate) : null,
        endDate: body.endDate ? new Date(body.endDate) : null,
        displayDuration: body.displayDuration || 5,
        transitionType: body.transitionType || 'FADE',
        backgroundColor: body.backgroundColor,
        textColor: body.textColor,
        priority: body.priority || 1,
        targetAudience: body.targetAudience,
        deviceType: body.deviceType || ['DESKTOP', 'MOBILE', 'TABLET'],
        geoLocation: body.geoLocation,
        seasonalTags: body.seasonalTags,
        conversionGoal: body.conversionGoal,
        budgetAllocation: body.budgetAllocation,
        createdBy: body.createdBy
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        }
      }
    })

    return NextResponse.json(createSuccessResponse({
      data: banner,
      message: "Banner başarıyla oluşturuldu"
    }), { status: 201 })
  } catch (error) {
    console.error("CategoryBanner POST error:", error)
    return NextResponse.json(
      createErrorResponse("Banner oluşturulurken hata oluştu"),
      { status: 500 }
    )
  }
}
