const { PrismaClient } = require('@prisma/client')
const fs = require('fs')
const path = require('path')

const prisma = new PrismaClient()

async function applyMigration() {
  console.log('🔧 Applying stock management migration...')
  
  try {
    // First, check if fields already exist
    console.log('🔍 Checking current schema...')
    
    try {
      const testResult = await prisma.$queryRaw`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'Product' 
        AND column_name IN ('stockQuantity', 'minStockThreshold', 'maxStockCapacity', 'stockStatus')
      `
      
      console.log('📋 Existing stock fields:', testResult.map(r => r.column_name))
      
      if (testResult.length >= 4) {
        console.log('✅ All stock management fields already exist')
        return true
      }
    } catch (error) {
      console.log('⚠️  Could not check existing fields, proceeding with migration...')
    }
    
    // Apply the migration SQL step by step
    console.log('📝 Adding stock management fields...')
    
    // Add columns
    await prisma.$executeRaw`ALTER TABLE "Product" ADD COLUMN IF NOT EXISTS "stockQuantity" INTEGER DEFAULT 0`
    console.log('✅ Added stockQuantity column')
    
    await prisma.$executeRaw`ALTER TABLE "Product" ADD COLUMN IF NOT EXISTS "minStockThreshold" INTEGER DEFAULT 0`
    console.log('✅ Added minStockThreshold column')
    
    await prisma.$executeRaw`ALTER TABLE "Product" ADD COLUMN IF NOT EXISTS "maxStockCapacity" INTEGER DEFAULT 100`
    console.log('✅ Added maxStockCapacity column')
    
    // Check if stockStatus column exists and add if needed
    try {
      await prisma.$executeRaw`
        DO $$ 
        BEGIN
          IF NOT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'Product' AND column_name = 'stockStatus'
          ) THEN
            ALTER TABLE "Product" ADD COLUMN "stockStatus" TEXT DEFAULT 'IN_STOCK';
          END IF;
        END $$;
      `
      console.log('✅ Added/verified stockStatus column')
    } catch (error) {
      console.log('⚠️  stockStatus column may already exist')
    }
    
    // Migrate existing data
    console.log('📊 Migrating existing stock data...')
    
    // Update stockQuantity from ProductVariant if exists
    try {
      await prisma.$executeRaw`
        UPDATE "Product" 
        SET "stockQuantity" = COALESCE(
          (SELECT SUM("stock") FROM "ProductVariant" WHERE "productId" = "Product"."id"),
          0
        )
        WHERE "stockQuantity" = 0
      `
      console.log('✅ Migrated stock quantities from variants')
    } catch (error) {
      console.log('⚠️  No ProductVariant table or stock field found, using defaults')
    }
    
    // Set reasonable defaults for minStockThreshold
    await prisma.$executeRaw`
      UPDATE "Product" 
      SET "minStockThreshold" = CASE 
        WHEN "stockQuantity" > 50 THEN 10
        WHEN "stockQuantity" > 20 THEN 5
        WHEN "stockQuantity" > 0 THEN 2
        ELSE 0
      END
      WHERE "minStockThreshold" = 0
    `
    console.log('✅ Set minimum stock thresholds')
    
    // Set maxStockCapacity
    await prisma.$executeRaw`
      UPDATE "Product" 
      SET "maxStockCapacity" = CASE 
        WHEN "stockQuantity" > 100 THEN "stockQuantity" * 2
        WHEN "stockQuantity" > 0 THEN GREATEST("stockQuantity" + 50, 100)
        ELSE 100
      END
      WHERE "maxStockCapacity" = 100
    `
    console.log('✅ Set maximum stock capacities')
    
    // Update stock status
    await prisma.$executeRaw`
      UPDATE "Product" 
      SET "stockStatus" = CASE 
        WHEN "stockQuantity" <= 0 THEN 'OUT_OF_STOCK'
        WHEN "stockQuantity" <= "minStockThreshold" THEN 'LOW_STOCK'
        ELSE 'IN_STOCK'
      END
    `
    console.log('✅ Updated stock statuses')
    
    // Add indexes for performance
    try {
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "Product_stockQuantity_idx" ON "Product"("stockQuantity")`
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "Product_minStockThreshold_idx" ON "Product"("minStockThreshold")`
      await prisma.$executeRaw`CREATE INDEX IF NOT EXISTS "Product_trackStock_idx" ON "Product"("trackStock")`
      console.log('✅ Added performance indexes')
    } catch (error) {
      console.log('⚠️  Some indexes may already exist')
    }
    
    // Verify migration
    console.log('🔍 Verifying migration...')
    const verifyResult = await prisma.$queryRaw`
      SELECT column_name, data_type, column_default
      FROM information_schema.columns 
      WHERE table_name = 'Product' 
      AND column_name IN ('stockQuantity', 'minStockThreshold', 'maxStockCapacity', 'stockStatus')
      ORDER BY column_name
    `
    
    console.log('📋 Migration verification:')
    verifyResult.forEach(col => {
      console.log(`  ✅ ${col.column_name}: ${col.data_type} (default: ${col.column_default})`)
    })
    
    // Test field access
    const testProduct = await prisma.product.findFirst({
      select: {
        id: true,
        name: true,
        stockQuantity: true,
        minStockThreshold: true,
        maxStockCapacity: true,
        stockStatus: true
      }
    })
    
    if (testProduct) {
      console.log('\n🧪 Test product data:')
      console.log(`  Name: ${testProduct.name}`)
      console.log(`  Stock Quantity: ${testProduct.stockQuantity}`)
      console.log(`  Min Threshold: ${testProduct.minStockThreshold}`)
      console.log(`  Max Capacity: ${testProduct.maxStockCapacity}`)
      console.log(`  Status: ${testProduct.stockStatus}`)
    }
    
    console.log('\n✅ Stock management migration completed successfully!')
    return true
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

applyMigration()
  .then(() => {
    console.log('\n🎉 Migration completed! You can now run the seeding script.')
  })
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
