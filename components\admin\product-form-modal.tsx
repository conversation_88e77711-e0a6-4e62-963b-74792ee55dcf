"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import {
  Loader2,
  Plus,
  Trash2,
  FileText,
  Ruler,
  Package,
  Tag,
  DollarSign,
  CheckCircle,
  Globe,
  ImageIcon as ImageIconLucide,
  CalendarIcon,
  Percent,
  TrendingDown,
  TrendingUp,
  Clock,
  Users,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { useCreateProduct, useUpdateProduct } from "@/lib/hooks/use-products"
import { useMainCategories } from "@/lib/hooks/use-categories"
import { mockBrands, certificateTypes, specificationGroups } from "@/data/mock-data"
import type {
  Product,
  ProductFormData,
  ProductSpecification,
  ProductCertificate,
  ProductDimensions,
  ProductImage,
  ProductCreateInput,
  ProductUpdateInput,
} from "@/types"
import {
  calculateStockStatus,
  calculateProfitMargin,
  getStockStatusColor,
  getStockStatusText,
  calculateCurrentPrice,
  calculateProfitFromCurrentPrice,
  type ActiveDiscountInfo
} from "@/types"
import { Separator } from "@/components/ui/separator"
import { ImageUpload } from "@/components/ui/image-upload"
import { SEOFormSection } from "@/components/admin/seo-form-section"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { format } from "date-fns"
import { tr } from "date-fns/locale"
import { ProductDiscountManager } from "./product-discount-manager"
import ProductService from "@/lib/services/ProductService"

interface ProductFormModalProps {
  open: boolean
  onClose: () => void
  product: Product | null
  onSuccess: (message: string) => void
  onError: (message: string) => void
  preselectedCategoryId?: string // Optional category ID when adding from category page
}

export function ProductFormModal({ open, onClose, product, onSuccess, onError, preselectedCategoryId }: ProductFormModalProps) {
  const isEditing = !!product

  // Initialize form data with empty values - category will be set in useEffect
  const [formData, setFormData] = useState<ProductFormData>({
    name: "",
    description: "",
    shortDescription: "",
    categoryId: "",
    brand: "",
    model: "",
    // Simplified Pricing
    costPrice: undefined,
    basePrice: 0,
    zakatAmount: undefined,
    taxRate: 20,
    // Stock Management
    trackStock: true,
    stockQuantity: undefined,
    minStockThreshold: undefined,
    maxStockCapacity: undefined,
    stockLocation: undefined,
    reorderPoint: undefined,
    reorderQuantity: undefined,
    // Other fields
    weight: undefined,
    dimensions: undefined,
    specifications: [],
    certificates: [],
    isActive: true,
    isFeatured: false,
    isNew: true,
    isOnSale: false,
    // SEO Fields
    seoTitle: "",
    seoDescription: "",
    metaKeywords: [],
    canonicalUrl: "",
    robotsDirective: "index,follow",
    focusKeyword: "",
    seoScore: 0,

    // Open Graph Fields
    ogTitle: "",
    ogDescription: "",
    ogImage: "",
    ogType: "product",

    // Twitter Card Fields
    twitterCard: "summary_large_image",
    twitterTitle: "",
    twitterDescription: "",
    twitterImage: "",

    // Structured Data
    structuredData: {},

    // Additional SEO Fields
    alternativeText: "",
    breadcrumbs: [],
    schemaMarkup: "",
    hreflangTags: {},

    // Content SEO
    readabilityScore: 0,
    keywordDensity: 0,
    internalLinks: [],
    externalLinks: [],
    images: [],
    discounts: [],
  })

  const [productImages, setProductImages] = useState<ProductImage[]>([])
  const { data: categoriesData, loading: categoriesLoading, error: categoriesError } = useMainCategories()
  const { mutate: createProduct, loading: creating } = useCreateProduct()
  const { mutate: updateProduct, loading: updating } = useUpdateProduct()
  const loading = creating || updating

  // Handle categories data structure - it might be nested or direct array
  const categories = categoriesData?.data || categoriesData || []

  // Debug categories loading
  React.useEffect(() => {
    console.log('🏷️ ProductFormModal - Categories Debug:', {
      categoriesData,
      categories,
      categoriesLoading,
      categoriesError,
      categoriesCount: categories.length
    })
  }, [categoriesData, categories, categoriesLoading, categoriesError])

  // Handle form initialization when modal opens
  React.useEffect(() => {
    if (open) {
      if (isEditing && product) {
        // Editing mode - populate with product data
        setFormData({
          name: product.name || "",
          description: product.description || "",
          shortDescription: product.shortDescription || "",
          categoryId: product.categoryId || "",
          brand: product.brand || "",
          model: product.model || "",
          // Simplified Pricing
          costPrice: product.baseCostPrice,
          basePrice: product.basePrice || 0,
          zakatAmount: product.zakatAmount,
          taxRate: product.taxRate || 20,
          // Stock Management
          trackStock: product.trackStock !== false,
          stockQuantity: product.stockQuantity,
          minStockThreshold: product.minStockThreshold,
          maxStockCapacity: product.maxStockCapacity,
          stockLocation: product.stockLocation,
          reorderPoint: product.reorderPoint,
          reorderQuantity: product.reorderQuantity,
          // Other fields
          weight: product.weight,
          dimensions: product.dimensions,
          specifications: product.specifications || [],
          certificates: product.certificates || [],
          isActive: product.isActive !== false,
          isFeatured: product.isFeatured || false,
          isNew: product.isNew || false,
          isOnSale: product.isOnSale || false,
          // SEO Fields
          seoTitle: product.seoTitle || "",
          seoDescription: product.seoDescription || "",
          metaKeywords: product.metaKeywords || [],
          canonicalUrl: product.canonicalUrl || "",
          robotsDirective: product.robotsDirective || "index,follow",
          focusKeyword: product.focusKeyword || "",
          seoScore: product.seoScore || 0,

          // Open Graph Fields
          ogTitle: product.ogTitle || "",
          ogDescription: product.ogDescription || "",
          ogImage: product.ogImage || "",
          ogType: product.ogType || "product",

          // Twitter Card Fields
          twitterCard: product.twitterCard || "summary_large_image",
          twitterTitle: product.twitterTitle || "",
          twitterDescription: product.twitterDescription || "",
          twitterImage: product.twitterImage || "",

          // Structured Data
          structuredData: product.structuredData || {},

          // Additional SEO Fields
          alternativeText: product.alternativeText || "",
          breadcrumbs: product.breadcrumbs || [],
          schemaMarkup: product.schemaMarkup || "",
          hreflangTags: product.hreflangTags || {},

          // Content SEO
          readabilityScore: product.readabilityScore || 0,
          keywordDensity: product.keywordDensity || 0,
          internalLinks: product.internalLinks || [],
          externalLinks: product.externalLinks || [],
          images: product.images || [],
          discounts: product.discounts || [],
        })
      } else {
        // Creating mode - reset form and handle preselected category
        setFormData({
          name: "",
          description: "",
          shortDescription: "",
          categoryId: preselectedCategoryId || "",
          brand: "",
          model: "",
          // Simplified Pricing
          costPrice: undefined,
          basePrice: 0,
          zakatAmount: undefined,
          taxRate: 20,
          // Stock Management
          trackStock: true,
          stockQuantity: undefined,
          minStockThreshold: undefined,
          maxStockCapacity: undefined,
          stockLocation: undefined,
          reorderPoint: undefined,
          reorderQuantity: undefined,
          // Other fields
          weight: undefined,
          dimensions: undefined,
          specifications: [],
          certificates: [],
          isActive: true,
          isFeatured: false,
          isNew: true,
          isOnSale: false,
          // SEO Fields
          seoTitle: "",
          seoDescription: "",
          metaKeywords: [],
          canonicalUrl: "",
          robotsDirective: "index,follow",
          focusKeyword: "",
          seoScore: 0,

          // Open Graph Fields
          ogTitle: "",
          ogDescription: "",
          ogImage: "",
          ogType: "product",

          // Twitter Card Fields
          twitterCard: "summary_large_image",
          twitterTitle: "",
          twitterDescription: "",
          twitterImage: "",

          // Structured Data
          structuredData: {},

          // Additional SEO Fields
          alternativeText: "",
          breadcrumbs: [],
          schemaMarkup: "",
          hreflangTags: {},

          // Content SEO
          readabilityScore: 0,
          keywordDensity: 0,
          internalLinks: [],
          externalLinks: [],
          images: [],
          discounts: [],
        })
      }
    }
  }, [open, isEditing, product, preselectedCategoryId])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const productDataToSubmit: ProductCreateInput | ProductUpdateInput = {
        ...formData,
        // Simplified pricing fields
        basePrice: Number(formData.basePrice),
        baseCostPrice: formData.costPrice ? Number(formData.costPrice) : undefined,
        zakatAmount: formData.zakatAmount ? Number(formData.zakatAmount) : undefined,
        taxRate: Number(formData.taxRate),
        // Stock management fields
        stockQuantity: formData.stockQuantity ? Number(formData.stockQuantity) : undefined,
        minStockThreshold: formData.minStockThreshold ? Number(formData.minStockThreshold) : undefined,
        maxStockCapacity: formData.maxStockCapacity ? Number(formData.maxStockCapacity) : undefined,
        stockLocation: formData.stockLocation || undefined,
        reorderPoint: formData.reorderPoint ? Number(formData.reorderPoint) : undefined,
        reorderQuantity: formData.reorderQuantity ? Number(formData.reorderQuantity) : undefined,
        // Other fields
        weight: formData.weight ? Number(formData.weight) : undefined,
      } as ProductCreateInput | ProductUpdateInput

      if (isEditing) {
        await updateProduct({ id: product!.id, data: productDataToSubmit as ProductUpdateInput })
        onSuccess("Ürün başarıyla güncellendi!")
      } else {
        await createProduct(productDataToSubmit as ProductCreateInput)
        onSuccess("Ürün başarıyla oluşturuldu!")
      }
      handleClose()
    } catch (error: any) {
      onError(error.message || "Bir hata oluştu.")
    }
  }

  const handleInputChange = (field: keyof ProductFormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  // Helper function to get category name by ID
  const getCategoryName = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId)
    return category?.name || ""
  }

  // Helper functions for calculations
  const getCurrentPriceInfo = React.useMemo((): ActiveDiscountInfo => {
    const basePrice = formData.basePrice || 0
    const discounts = formData.discounts || []

    // Debug logging
    console.log('🔍 Price Calculation Debug:', {
      basePrice,
      discountsCount: discounts.length,
      discounts: discounts.map(d => d ? ({
        type: d.type,
        value: d.value,
        isActive: d.isActive,
        startDate: d.startDate,
        endDate: d.endDate
      }) : null)
    })

    const result = calculateCurrentPrice(basePrice, discounts)
    console.log('🔍 Calculation Result:', result)

    return result
  }, [formData.basePrice, formData.discounts])

  const calculateProfitAmount = () => {
    const cost = formData.costPrice || 0
    return getCurrentPriceInfo.currentPrice - cost
  }

  const calculateProfitMarginPercentage = () => {
    return calculateProfitFromCurrentPrice(formData.costPrice || 0, getCurrentPriceInfo.currentPrice)
  }

  const getStockStatus = () => {
    return calculateStockStatus(formData.stockQuantity || 0, formData.minStockThreshold || 0)
  }

  const hasActiveDiscount = () => {
    return getCurrentPriceInfo.discount !== null
  }

  // Reset form when modal closes
  const handleClose = () => {
    setFormData({
      name: "",
      description: "",
      shortDescription: "",
      categoryId: "",
      brand: "",
      model: "",
      // Simplified Pricing
      costPrice: undefined,
      basePrice: 0,
      zakatAmount: undefined,
      taxRate: 20,
      // Stock Management
      trackStock: true,
      stockQuantity: undefined,
      minStockThreshold: undefined,
      maxStockCapacity: undefined,
      stockLocation: undefined,
      reorderPoint: undefined,
      reorderQuantity: undefined,
      // Other fields
      weight: undefined,
      dimensions: undefined,
      specifications: [],
      certificates: [],
      isActive: true,
      isFeatured: false,
      isNew: true,
      isOnSale: false,
      // SEO Fields
      seoTitle: "",
      seoDescription: "",
      metaKeywords: [],
      canonicalUrl: "",
      robotsDirective: "index,follow",
      focusKeyword: "",
      seoScore: 0,

      // Open Graph Fields
      ogTitle: "",
      ogDescription: "",
      ogImage: "",
      ogType: "product",

      // Twitter Card Fields
      twitterCard: "summary_large_image",
      twitterTitle: "",
      twitterDescription: "",
      twitterImage: "",

      // Structured Data
      structuredData: {},

      // Additional SEO Fields
      alternativeText: "",
      breadcrumbs: [],
      schemaMarkup: "",
      hreflangTags: {},

      // Content SEO
      readabilityScore: 0,
      keywordDensity: 0,
      internalLinks: [],
      externalLinks: [],
      images: [],
      discounts: [],
    })
    setProductImages([])
    onClose()
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        if (!isOpen) {
          handleClose()
        }
      }}
    >
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Ürün Düzenle" : "Yeni Ürün Ekle"}</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="basic">Temel Bilgiler</TabsTrigger>
              <TabsTrigger value="pricing">Fiyat & Stok</TabsTrigger>
              <TabsTrigger value="discounts">İndirimler</TabsTrigger>
              <TabsTrigger value="media">Medya</TabsTrigger>
              <TabsTrigger value="seo">SEO</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Ürün Adı *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="Örn: 3M Peltor X5A Kulaklık"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="categoryId">Kategori *</Label>
                  <Select
                    value={formData.categoryId}
                    onValueChange={(value) => handleInputChange("categoryId", value)}
                    disabled={categoriesLoading}
                  >
                    <SelectTrigger>
                      <SelectValue
                        placeholder={
                          categoriesLoading
                            ? "Kategoriler yükleniyor..."
                            : categoriesError
                            ? "Kategori yükleme hatası"
                            : "Kategori seçin"
                        }
                      />
                    </SelectTrigger>
                    <SelectContent>
                      {categoriesError ? (
                        <SelectItem value="" disabled>
                          Hata: {categoriesError}
                        </SelectItem>
                      ) : categories.length === 0 ? (
                        <SelectItem value="" disabled>
                          Kategori bulunamadı
                        </SelectItem>
                      ) : (
                        categories.map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  {categoriesError && (
                    <p className="text-sm text-red-500 mt-1">
                      Kategoriler yüklenirken hata oluştu: {categoriesError}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="description">Açıklama *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange("description", e.target.value)}
                  placeholder="Ürün açıklaması..."
                  rows={4}
                  required
                />
              </div>
            </TabsContent>

            <TabsContent value="pricing" className="space-y-6">
              {/* Pricing Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    Fiyatlandırma
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="costPrice">Alış Fiyatı (Maliyet)</Label>
                      <Input
                        id="costPrice"
                        type="number"
                        value={formData.costPrice || ""}
                        onChange={(e) => handleInputChange("costPrice", e.target.value ? Number.parseFloat(e.target.value) : undefined)}
                        min="0"
                        step="0.01"
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <Label htmlFor="basePrice">Normal Satış Fiyatı *</Label>
                      <Input
                        id="basePrice"
                        type="number"
                        value={formData.basePrice}
                        onChange={(e) => {
                          const newValue = Number.parseFloat(e.target.value) || 0
                          console.log('📝 BasePrice changed:', newValue)
                          handleInputChange("basePrice", newValue)
                        }}
                        min="0"
                        step="0.01"
                        required
                        placeholder="0.00"
                      />
                    </div>
                  </div>

                  {/* Debug Info (remove in production) */}
                  {process.env.NODE_ENV === 'development' && (
                    <div className="p-2 bg-yellow-50 rounded text-xs">
                      <strong>Debug:</strong> basePrice={formData.basePrice}, discounts={formData.discounts?.length || 0},
                      currentPrice={getCurrentPriceInfo.currentPrice}
                    </div>
                  )}

                  {/* Current Price Display */}
                  {formData.basePrice > 0 && (
                    <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <h4 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
                        <Tag className="h-4 w-4" />
                        Güncel Satış Fiyatı
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <span className="text-sm text-blue-700">Müşteri Fiyatı:</span>
                          <p className="text-lg font-bold text-blue-900">
                            {getCurrentPriceInfo.currentPrice.toFixed(2)} ₺
                          </p>
                        </div>
                        {hasActiveDiscount() && (
                          <>
                            <div>
                              <span className="text-sm text-green-700">İndirim Tutarı:</span>
                              <p className="text-lg font-bold text-green-600">
                                -{getCurrentPriceInfo.discountAmount.toFixed(2)} ₺
                              </p>
                            </div>
                            <div>
                              <span className="text-sm text-green-700">İndirim Oranı:</span>
                              <p className="text-lg font-bold text-green-600">
                                %{getCurrentPriceInfo.discountPercentage.toFixed(1)}
                              </p>
                            </div>
                          </>
                        )}
                        {!hasActiveDiscount() && (
                          <div className="col-span-2">
                            <span className="text-sm text-gray-600">Aktif indirim bulunmuyor</span>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="zakatAmount">Zekat Tutarı</Label>
                      <Input
                        id="zakatAmount"
                        type="number"
                        value={formData.zakatAmount || ""}
                        onChange={(e) => handleInputChange("zakatAmount", e.target.value ? Number.parseFloat(e.target.value) : undefined)}
                        min="0"
                        step="0.01"
                        placeholder="0.00"
                      />
                    </div>
                    <div>
                      <Label htmlFor="taxRate">KDV Oranı (%)</Label>
                      <Input
                        id="taxRate"
                        type="number"
                        value={formData.taxRate}
                        onChange={(e) => handleInputChange("taxRate", Number.parseFloat(e.target.value) || 0)}
                        min="0"
                        max="100"
                        step="0.01"
                        placeholder="20"
                      />
                    </div>
                  </div>

                  {/* Profit Calculation Display */}
                  {formData.costPrice && formData.basePrice && (
                    <div className="mt-4 p-4 bg-green-50 rounded-lg border border-green-200">
                      <h4 className="font-medium text-green-900 mb-2 flex items-center gap-2">
                        <TrendingUp className="h-4 w-4" />
                        Kar Analizi (Güncel Fiyat Üzerinden)
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-green-700">Kar Tutarı:</span>
                          <p className="font-medium text-green-600">
                            {calculateProfitAmount().toFixed(2)} ₺
                          </p>
                        </div>
                        <div>
                          <span className="text-green-700">Kar Marjı:</span>
                          <p className="font-medium text-green-600">
                            %{calculateProfitMarginPercentage().toFixed(2)}
                          </p>
                        </div>
                        <div>
                          <span className="text-green-700">Maliyet Oranı:</span>
                          <p className="font-medium text-gray-700">
                            %{((formData.costPrice || 0) / getCurrentPriceInfo.currentPrice * 100).toFixed(2)}
                          </p>
                        </div>
                      </div>
                      {hasActiveDiscount() && (
                        <div className="mt-2 text-xs text-green-600">
                          * İndirimli fiyat üzerinden hesaplanmıştır
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Stock Management Section */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    Stok Yönetimi
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="trackStock"
                      checked={formData.trackStock}
                      onCheckedChange={(checked) => handleInputChange("trackStock", checked)}
                    />
                    <Label htmlFor="trackStock">Stok takibi yap</Label>
                  </div>

                  {formData.trackStock && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                          <Label htmlFor="stockQuantity">Mevcut Stok Miktarı</Label>
                          <Input
                            id="stockQuantity"
                            type="number"
                            value={formData.stockQuantity || ""}
                            onChange={(e) => handleInputChange("stockQuantity", e.target.value ? Number.parseInt(e.target.value) : undefined)}
                            min="0"
                            placeholder="0"
                          />
                        </div>
                        <div>
                          <Label htmlFor="minStockThreshold">Minimum Stok Eşiği</Label>
                          <Input
                            id="minStockThreshold"
                            type="number"
                            value={formData.minStockThreshold || ""}
                            onChange={(e) => handleInputChange("minStockThreshold", e.target.value ? Number.parseInt(e.target.value) : undefined)}
                            min="0"
                            placeholder="0"
                          />
                        </div>
                        <div>
                          <Label htmlFor="maxStockCapacity">Maksimum Stok Kapasitesi</Label>
                          <Input
                            id="maxStockCapacity"
                            type="number"
                            value={formData.maxStockCapacity || ""}
                            onChange={(e) => handleInputChange("maxStockCapacity", e.target.value ? Number.parseInt(e.target.value) : undefined)}
                            min="0"
                            placeholder="0"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div>
                          <Label htmlFor="reorderPoint">Yeniden Sipariş Noktası</Label>
                          <Input
                            id="reorderPoint"
                            type="number"
                            value={formData.reorderPoint || ""}
                            onChange={(e) => handleInputChange("reorderPoint", e.target.value ? Number.parseInt(e.target.value) : undefined)}
                            min="0"
                            placeholder="0"
                          />
                        </div>
                        <div>
                          <Label htmlFor="reorderQuantity">Yeniden Sipariş Miktarı</Label>
                          <Input
                            id="reorderQuantity"
                            type="number"
                            value={formData.reorderQuantity || ""}
                            onChange={(e) => handleInputChange("reorderQuantity", e.target.value ? Number.parseInt(e.target.value) : undefined)}
                            min="0"
                            placeholder="0"
                          />
                        </div>
                        <div>
                          <Label htmlFor="stockLocation">Stok Lokasyonu</Label>
                          <Input
                            id="stockLocation"
                            type="text"
                            value={formData.stockLocation || ""}
                            onChange={(e) => handleInputChange("stockLocation", e.target.value || undefined)}
                            placeholder="Depo A, Raf 1"
                          />
                        </div>
                      </div>

                      {/* Stock Status Display */}
                      {formData.stockQuantity !== undefined && (
                        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                          <h4 className="font-medium text-gray-900 mb-2">Stok Durumu</h4>
                          <div className="flex items-center gap-4">
                            <div>
                              <span className="text-gray-600">Durum:</span>
                              <span className={`ml-2 font-medium ${getStockStatusColor(getStockStatus())}`}>
                                {getStockStatusText(getStockStatus())}
                              </span>
                            </div>
                            <div>
                              <span className="text-gray-600">Mevcut Miktar:</span>
                              <span className="ml-2 font-medium">{formData.stockQuantity}</span>
                            </div>
                            {formData.minStockThreshold && (
                              <div>
                                <span className="text-gray-600">Minimum Eşik:</span>
                                <span className="ml-2 font-medium">{formData.minStockThreshold}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="discounts" className="space-y-4">
              <ProductDiscountManager
                discounts={formData.discounts || []}
                onDiscountsChange={(discounts) => handleInputChange("discounts", discounts)}
                productPrice={formData.price}
              />
            </TabsContent>

            <TabsContent value="media" className="space-y-4">
              <div>Medya yönetimi buraya gelecek</div>
            </TabsContent>

            <TabsContent value="seo" className="space-y-4">
              <SEOFormSection
                formData={formData}
                onFieldChange={handleInputChange}
                categories={categories}
              />
            </TabsContent>
          </Tabs>

          <div className="flex justify-end gap-3 border-t pt-6">
            <Button type="button" variant="outline" onClick={handleClose} disabled={loading}>
              İptal
            </Button>
            <Button type="submit" disabled={loading} className="bg-orange-500 hover:bg-orange-600">
              {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              {isEditing ? "Güncelle" : "Oluştur"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
