-- Migration: Add CategoryBanner table and migrate existing banner data
-- Date: 2024-07-19
-- Description: Create CategoryBanner table for multi-banner system and migrate existing bannerImage data

-- 1. Create new enums
CREATE TYPE "TransitionType" AS ENUM ('FADE', 'SLIDE', 'ZOOM', 'FLIP', 'CUBE', 'COVERFLOW');
CREATE TYPE "DeviceType" AS ENUM ('DESKTOP', 'MOBILE', 'TABLET');

-- 2. Create CategoryBanner table
CREATE TABLE "category_banners" (
    "id" TEXT NOT NULL,
    "categoryId" TEXT NOT NULL,
    "imageUrl" TEXT NOT NULL,
    "imageAlt" TEXT,
    "mobileImageUrl" TEXT,
    "title" TEXT,
    "subtitle" TEXT,
    "description" TEXT,
    "ctaText" TEXT,
    "ctaUrl" TEXT,
    "displayOrder" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "startDate" TIMESTAMP(3),
    "endDate" TIMESTAMP(3),
    "displayDuration" INTEGER NOT NULL DEFAULT 5,
    "transitionType" "TransitionType" NOT NULL DEFAULT 'FADE',
    "backgroundColor" TEXT,
    "textColor" TEXT,
    "clickCount" INTEGER NOT NULL DEFAULT 0,
    "impressionCount" INTEGER NOT NULL DEFAULT 0,
    "ctr" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "priority" INTEGER NOT NULL DEFAULT 1,
    "targetAudience" TEXT,
    "deviceType" "DeviceType"[] DEFAULT ARRAY['DESKTOP', 'MOBILE', 'TABLET']::"DeviceType"[],
    "geoLocation" TEXT,
    "seasonalTags" TEXT,
    "conversionGoal" TEXT,
    "budgetAllocation" DOUBLE PRECISION,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdBy" TEXT,

    CONSTRAINT "category_banners_pkey" PRIMARY KEY ("id")
);

-- 3. Create indexes
CREATE INDEX "category_banners_categoryId_displayOrder_idx" ON "category_banners"("categoryId", "displayOrder");
CREATE INDEX "category_banners_isActive_startDate_endDate_idx" ON "category_banners"("isActive", "startDate", "endDate");

-- 4. Add foreign key constraint
ALTER TABLE "category_banners" ADD CONSTRAINT "category_banners_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "categories"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- 5. Migrate existing banner data
-- Insert existing bannerImage data into CategoryBanner table
INSERT INTO "category_banners" (
    "id",
    "categoryId", 
    "imageUrl",
    "imageAlt",
    "title",
    "description",
    "displayOrder",
    "isActive",
    "transitionType",
    "priority",
    "createdAt",
    "updatedAt"
)
SELECT 
    gen_random_uuid()::text as "id",
    "id" as "categoryId",
    "bannerImage" as "imageUrl",
    "name" || ' banner' as "imageAlt",
    "name" as "title",
    "description" as "description",
    0 as "displayOrder",
    true as "isActive",
    'FADE'::"TransitionType" as "transitionType",
    1 as "priority",
    "createdAt",
    "updatedAt"
FROM "categories" 
WHERE "bannerImage" IS NOT NULL AND "bannerImage" != '';

-- 6. Remove bannerImage column from categories table (will be done in next migration after testing)
-- ALTER TABLE "categories" DROP COLUMN "bannerImage";

-- Note: Keep bannerImage column for now for backward compatibility
-- It will be removed in a future migration after full testing
