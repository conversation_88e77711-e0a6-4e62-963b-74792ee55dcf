"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Star, ThumbsUp, MessageCircle, User } from "lucide-react"

interface ProductReviewsProps {
  productId: string
}

interface Review {
  id: string
  userName: string
  rating: number
  title: string
  comment: string
  date: string
  verified: boolean
  helpful: number
}

// Mock reviews data
const mockReviews: Review[] = [
  {
    id: "1",
    userName: "Ahmet K.",
    rating: 5,
    title: "Mükemmel kalite",
    comment: "Çok kaliteli bir ürün. İş güvenliği açısından tam istediğim gibi. Kesinlikle tavsiye ederim.",
    date: "2024-01-15",
    verified: true,
    helpful: 12,
  },
  {
    id: "2",
    userName: "Fatma S.",
    rating: 4,
    title: "<PERSON><PERSON> ama pahalı",
    comment: "<PERSON>r<PERSON>n kaliteli ancak fiyatı biraz yüksek. Yine de iş güvenliği için gerekli bir yatırım.",
    date: "2024-01-10",
    verified: true,
    helpful: 8,
  },
  {
    id: "3",
    userName: "Mehmet Y.",
    rating: 5,
    title: "Harika ürün",
    comment: "Çok memnun kaldım. Hızlı teslimat ve kaliteli ambalaj. Teşekkürler.",
    date: "2024-01-05",
    verified: false,
    helpful: 5,
  },
]

export function ProductReviews({ productId }: ProductReviewsProps) {
  const [reviews] = useState<Review[]>(mockReviews)
  const [newReview, setNewReview] = useState("")
  const [newRating, setNewRating] = useState(0)
  const [showReviewForm, setShowReviewForm] = useState(false)

  const averageRating =
    reviews.length > 0 ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length : 0

  const ratingDistribution = [5, 4, 3, 2, 1].map((rating) => ({
    rating,
    count: reviews.filter((review) => review.rating === rating).length,
    percentage:
      reviews.length > 0 ? (reviews.filter((review) => review.rating === rating).length / reviews.length) * 100 : 0,
  }))

  const handleSubmitReview = () => {
    // TODO: Implement review submission
    console.log("Submitting review:", { rating: newRating, comment: newReview })
    setNewReview("")
    setNewRating(0)
    setShowReviewForm(false)
  }

  return (
    <div className="space-y-6">
      {/* Review Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="w-5 h-5 text-yellow-400 fill-current" />
            Müşteri Değerlendirmeleri
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Average Rating */}
            <div className="text-center">
              <div className="text-4xl font-bold text-gray-900 mb-2">{averageRating.toFixed(1)}</div>
              <div className="flex justify-center mb-2">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Star
                    key={i}
                    className={`w-5 h-5 ${
                      i < Math.floor(averageRating) ? "text-yellow-400 fill-current" : "text-gray-300"
                    }`}
                  />
                ))}
              </div>
              <p className="text-sm text-gray-600">{reviews.length} değerlendirme</p>
            </div>

            {/* Rating Distribution */}
            <div className="space-y-2">
              {ratingDistribution.map(({ rating, count, percentage }) => (
                <div key={rating} className="flex items-center gap-2">
                  <span className="text-sm w-8">{rating}</span>
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div className="bg-yellow-400 h-2 rounded-full" style={{ width: `${percentage}%` }} />
                  </div>
                  <span className="text-sm text-gray-600 w-8">{count}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Write Review Button */}
          <div className="mt-6 pt-6 border-t">
            <Button onClick={() => setShowReviewForm(!showReviewForm)} variant="outline" className="w-full md:w-auto">
              <MessageCircle className="w-4 h-4 mr-2" />
              Değerlendirme Yaz
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Review Form */}
      {showReviewForm && (
        <Card>
          <CardHeader>
            <CardTitle>Değerlendirme Yazın</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Rating Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Puanınız</label>
              <div className="flex gap-1">
                {Array.from({ length: 5 }).map((_, i) => (
                  <button key={i} onClick={() => setNewRating(i + 1)} className="p-1">
                    <Star className={`w-6 h-6 ${i < newRating ? "text-yellow-400 fill-current" : "text-gray-300"}`} />
                  </button>
                ))}
              </div>
            </div>

            {/* Comment */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Yorumunuz</label>
              <Textarea
                value={newReview}
                onChange={(e) => setNewReview(e.target.value)}
                placeholder="Ürün hakkındaki düşüncelerinizi paylaşın..."
                rows={4}
              />
            </div>

            {/* Submit Buttons */}
            <div className="flex gap-2">
              <Button onClick={handleSubmitReview} disabled={newRating === 0 || newReview.trim() === ""}>
                Gönder
              </Button>
              <Button variant="outline" onClick={() => setShowReviewForm(false)}>
                İptal
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Reviews List */}
      <div className="space-y-4">
        {reviews.map((review) => (
          <Card key={review.id}>
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                    <User className="w-5 h-5 text-gray-600" />
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-gray-900">{review.userName}</span>
                      {review.verified && (
                        <Badge variant="secondary" className="text-xs">
                          Doğrulanmış Alıcı
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2 mt-1">
                      <div className="flex">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <Star
                            key={i}
                            className={`w-4 h-4 ${
                              i < review.rating ? "text-yellow-400 fill-current" : "text-gray-300"
                            }`}
                          />
                        ))}
                      </div>
                      <span className="text-sm text-gray-500">{new Date(review.date).toLocaleDateString("tr-TR")}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium text-gray-900">{review.title}</h4>
                <p className="text-gray-700">{review.comment}</p>
              </div>

              <div className="flex items-center gap-4 mt-4 pt-4 border-t">
                <button className="flex items-center gap-1 text-sm text-gray-600 hover:text-gray-900">
                  <ThumbsUp className="w-4 h-4" />
                  Faydalı ({review.helpful})
                </button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {reviews.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <MessageCircle className="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Henüz değerlendirme yok</h3>
            <p className="text-gray-600 mb-6">Bu ürün için ilk değerlendirmeyi siz yazın.</p>
            <Button onClick={() => setShowReviewForm(true)}>İlk Değerlendirmeyi Yaz</Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
