import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 Applying stock management migration to products table...')

    // Step 1: Add missing stock management fields to products table (lowercase)
    await prisma.$executeRaw`
      ALTER TABLE "products" ADD COLUMN IF NOT EXISTS "stockQuantity" INTEGER DEFAULT 0;
    `

    await prisma.$executeRaw`
      ALTER TABLE "products" ADD COLUMN IF NOT EXISTS "minStockThreshold" INTEGER DEFAULT 0;
    `

    await prisma.$executeRaw`
      ALTER TABLE "products" ADD COLUMN IF NOT EXISTS "maxStockCapacity" INTEGER DEFAULT 100;
    `

    console.log('✅ Stock fields added to Product table')

    // Step 2: Set reasonable defaults for existing products
    await prisma.$executeRaw`
      UPDATE "products"
      SET "stockQuantity" = 50
      WHERE "stockQuantity" = 0;
    `

    await prisma.$executeRaw`
      UPDATE "products"
      SET "minStockThreshold" = 10
      WHERE "minStockThreshold" = 0;
    `

    await prisma.$executeRaw`
      UPDATE "products"
      SET "maxStockCapacity" = 100
      WHERE "maxStockCapacity" = 100;
    `

    console.log('✅ Default stock values set for existing products')

    // Step 3: Add indexes for performance
    try {
      await prisma.$executeRaw`
        CREATE INDEX IF NOT EXISTS "products_stockQuantity_idx" ON "products"("stockQuantity");
      `

      await prisma.$executeRaw`
        CREATE INDEX IF NOT EXISTS "products_minStockThreshold_idx" ON "products"("minStockThreshold");
      `

      await prisma.$executeRaw`
        CREATE INDEX IF NOT EXISTS "products_trackStock_idx" ON "products"("trackStock");
      `
      
      console.log('✅ Indexes created')
    } catch (indexError) {
      console.log('⚠️ Index creation failed (might already exist):', indexError)
    }

    // Step 4: Test that the fields now exist
    const testProduct = await prisma.product.findFirst({
      select: {
        id: true,
        name: true,
        stockQuantity: true,
        minStockThreshold: true,
        maxStockCapacity: true,
        stockStatus: true,
        trackStock: true,
        basePrice: true
      }
    })

    if (testProduct) {
      console.log('✅ Migration successful! Stock fields are now accessible')
      console.log('Sample product:', {
        id: testProduct.id,
        name: testProduct.name,
        stockQuantity: testProduct.stockQuantity,
        minStockThreshold: testProduct.minStockThreshold,
        maxStockCapacity: testProduct.maxStockCapacity,
        stockStatus: testProduct.stockStatus
      })
    }

    return NextResponse.json({
      success: true,
      message: 'Stock management migration applied successfully',
      testProduct: testProduct,
      nextSteps: [
        'Test the individual product API endpoints',
        'Check the admin panel edit form',
        'Verify stock data consistency'
      ]
    })

  } catch (error: any) {
    console.error('❌ Migration failed:', error)
    return NextResponse.json({
      success: false,
      error: error.message,
      details: 'Failed to apply stock management migration'
    }, { status: 500 })
  }
}
