"use client"

import type React from "react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Minus, Plus } from "lucide-react"

interface ProductQuantitySelectorProps {
  quantity: number
  onQuantityChange: (quantity: number) => void
  maxQuantity?: number
  minQuantity?: number
  disabled?: boolean
}

export function ProductQuantitySelector({
  quantity,
  onQuantityChange,
  maxQuantity = 999,
  minQuantity = 1,
  disabled = false,
}: ProductQuantitySelectorProps) {
  const handleDecrease = () => {
    if (quantity > minQuantity) {
      onQuantityChange(quantity - 1)
    }
  }

  const handleIncrease = () => {
    if (quantity < maxQuantity) {
      onQuantityChange(quantity + 1)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number.parseInt(e.target.value) || minQuantity
    const clampedValue = Math.max(minQuantity, Math.min(maxQuantity, value))
    onQuantityChange(clampedValue)
  }

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm font-medium text-gray-700">Adet:</span>
      <div className="flex items-center border rounded-md">
        <Button
          variant="ghost"
          size="icon"
          className="h-10 w-10 rounded-r-none"
          onClick={handleDecrease}
          disabled={disabled || quantity <= minQuantity}
        >
          <Minus className="w-4 h-4" />
        </Button>
        <Input
          type="number"
          value={quantity}
          onChange={handleInputChange}
          className="w-16 h-10 text-center border-0 border-x rounded-none focus-visible:ring-0"
          min={minQuantity}
          max={maxQuantity}
          disabled={disabled}
        />
        <Button
          variant="ghost"
          size="icon"
          className="h-10 w-10 rounded-l-none"
          onClick={handleIncrease}
          disabled={disabled || quantity >= maxQuantity}
        >
          <Plus className="w-4 h-4" />
        </Button>
      </div>
      {maxQuantity < 999 && <span className="text-xs text-gray-500">Maksimum {maxQuantity} adet</span>}
    </div>
  )
}
