import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  const testResults = []
  let allTestsPassed = true

  try {
    console.log('🧪 Starting Stock Management UI Tests...')

    // Test 1: Create Test Products with Different Stock Levels
    console.log('\n📋 Test 1: Create Test Products with Different Stock Levels')
    const testProducts = []
    
    try {
      // Get or create test category
      let testCategory = await prisma.category.findFirst({
        where: { name: 'Test Stok Kategorisi' }
      })
      
      if (!testCategory) {
        testCategory = await prisma.category.create({
          data: {
            name: 'Test Stok Kategorisi',
            slug: 'test-stok-kategorisi',
            description: 'Test kategorisi stok yönetimi için',
            icon: '📦',
            isActive: true,
            sortOrder: 999
          }
        })
      }

      // Create products with different stock statuses
      const productData = [
        {
          name: 'Test Ürün - Stokta Var',
          slug: 'test-urun-stokta-var-' + Date.now(),
          stockQuantity: 100,
          minStockThreshold: 10,
          maxStockCapacity: 500,
          stockStatus: 'IN_STOCK'
        },
        {
          name: 'Test Ürün - Düşük Stok',
          slug: 'test-urun-dusuk-stok-' + Date.now(),
          stockQuantity: 5,
          minStockThreshold: 10,
          maxStockCapacity: 200,
          stockStatus: 'LOW_STOCK'
        },
        {
          name: 'Test Ürün - Stokta Yok',
          slug: 'test-urun-stokta-yok-' + Date.now(),
          stockQuantity: 0,
          minStockThreshold: 5,
          maxStockCapacity: 100,
          stockStatus: 'OUT_OF_STOCK'
        }
      ]

      for (const data of productData) {
        const product = await prisma.product.create({
          data: {
            ...data,
            description: `Test ürünü - ${data.name}`,
            shortDescription: `Test ürünü`,
            categoryId: testCategory.id,
            brand: 'Test Marka',
            basePrice: 100.0,
            baseCostPrice: 50.0,
            trackStock: true,
            isActive: true,
            publishedAt: new Date()
          }
        })
        testProducts.push(product)
      }

      console.log('✅ Test products created:', testProducts.length)

      testResults.push({
        test: 'Create Test Products with Different Stock Levels',
        status: 'PASSED',
        details: {
          productsCreated: testProducts.length,
          productIds: testProducts.map(p => p.id)
        }
      })
    } catch (error: any) {
      console.log('❌ Test products creation failed:', error.message)
      testResults.push({
        test: 'Create Test Products with Different Stock Levels',
        status: 'FAILED',
        error: error.message
      })
      allTestsPassed = false
    }

    // Test 2: Create Sample Stock Movements
    console.log('\n📋 Test 2: Create Sample Stock Movements')
    if (testProducts.length > 0) {
      try {
        const movements = []
        
        // Create stock movements for first product
        const product1 = testProducts[0]
        
        // Stock in movement
        const stockIn = await prisma.stockMovement.create({
          data: {
            productId: product1.id,
            movementType: 'in',
            quantity: 50,
            previousStock: 100,
            newStock: 150,
            reason: 'purchase',
            reference: 'PO-TEST-001',
            unitCost: 45.0,
            supplier: 'Test Tedarikçi',
            notes: 'Test stok giriş hareketi',
            createdBy: 'test-user'
          }
        })
        movements.push(stockIn)

        // Update product stock
        await prisma.product.update({
          where: { id: product1.id },
          data: { stockQuantity: 150 }
        })

        // Stock out movement
        const stockOut = await prisma.stockMovement.create({
          data: {
            productId: product1.id,
            movementType: 'out',
            quantity: -25,
            previousStock: 150,
            newStock: 125,
            reason: 'sale',
            reference: 'ORDER-TEST-001',
            notes: 'Test stok çıkış hareketi',
            createdBy: 'test-user'
          }
        })
        movements.push(stockOut)

        // Update product stock
        await prisma.product.update({
          where: { id: product1.id },
          data: { stockQuantity: 125 }
        })

        console.log('✅ Sample stock movements created:', movements.length)

        testResults.push({
          test: 'Create Sample Stock Movements',
          status: 'PASSED',
          details: {
            movementsCreated: movements.length,
            productId: product1.id
          }
        })
      } catch (error: any) {
        console.log('❌ Sample stock movements creation failed:', error.message)
        testResults.push({
          test: 'Create Sample Stock Movements',
          status: 'FAILED',
          error: error.message
        })
        allTestsPassed = false
      }
    }

    // Test 3: Test Products API with trackStock Filter
    console.log('\n📋 Test 3: Test Products API with trackStock Filter')
    try {
      const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/products?trackStock=true&limit=100`)
      
      if (response.ok) {
        const result = await response.json()
        const trackStockProducts = result.data.filter((p: any) => p.trackStock === true)
        
        console.log('✅ Products API with trackStock filter working:', trackStockProducts.length, 'products found')

        testResults.push({
          test: 'Test Products API with trackStock Filter',
          status: 'PASSED',
          details: {
            totalProducts: result.data.length,
            trackStockProducts: trackStockProducts.length
          }
        })
      } else {
        throw new Error(`API returned ${response.status}`)
      }
    } catch (error: any) {
      console.log('❌ Products API test failed:', error.message)
      testResults.push({
        test: 'Test Products API with trackStock Filter',
        status: 'FAILED',
        error: error.message
      })
      allTestsPassed = false
    }

    // Test 4: Test Product Stock API for Test Product
    console.log('\n📋 Test 4: Test Product Stock API for Test Product')
    if (testProducts.length > 0) {
      try {
        const testProductId = testProducts[0].id
        const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/products/${testProductId}/stock`)
        
        if (response.ok) {
          const result = await response.json()
          console.log('✅ Product stock API working for test product')
          console.log('   - Current stock:', result.data.product.stockQuantity)
          console.log('   - Recent movements:', result.data.recentMovements.length)
          console.log('   - Statistics:', result.data.statistics.totalMovements, 'total movements')

          testResults.push({
            test: 'Test Product Stock API for Test Product',
            status: 'PASSED',
            details: {
              productId: testProductId,
              currentStock: result.data.product.stockQuantity,
              recentMovements: result.data.recentMovements.length,
              totalMovements: result.data.statistics.totalMovements
            }
          })
        } else {
          throw new Error(`API returned ${response.status}`)
        }
      } catch (error: any) {
        console.log('❌ Product stock API test failed:', error.message)
        testResults.push({
          test: 'Test Product Stock API for Test Product',
          status: 'FAILED',
          error: error.message
        })
        allTestsPassed = false
      }
    }

    // Test 5: Test Stock Movements API with Product Filter
    console.log('\n📋 Test 5: Test Stock Movements API with Product Filter')
    if (testProducts.length > 0) {
      try {
        const testProductId = testProducts[0].id
        const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/stock-movements?productId=${testProductId}&limit=10`)
        
        if (response.ok) {
          const result = await response.json()
          console.log('✅ Stock movements API with product filter working:', result.data.length, 'movements found')

          testResults.push({
            test: 'Test Stock Movements API with Product Filter',
            status: 'PASSED',
            details: {
              productId: testProductId,
              movementsFound: result.data.length,
              totalCount: result.pagination.total
            }
          })
        } else {
          throw new Error(`API returned ${response.status}`)
        }
      } catch (error: any) {
        console.log('❌ Stock movements API test failed:', error.message)
        testResults.push({
          test: 'Test Stock Movements API with Product Filter',
          status: 'FAILED',
          error: error.message
        })
        allTestsPassed = false
      }
    }

    console.log('\n🎯 UI Test Summary:')
    console.log(`Total Tests: ${testResults.length}`)
    console.log(`Passed: ${testResults.filter(t => t.status === 'PASSED').length}`)
    console.log(`Failed: ${testResults.filter(t => t.status === 'FAILED').length}`)
    console.log('\n📝 Test products created for UI testing. You can now visit:')
    console.log('   http://localhost:3000/admin/stok-yonetimi')

    return NextResponse.json({
      success: allTestsPassed,
      message: allTestsPassed ? 'All UI tests passed! Test data created.' : 'Some UI tests failed',
      summary: {
        total: testResults.length,
        passed: testResults.filter(t => t.status === 'PASSED').length,
        failed: testResults.filter(t => t.status === 'FAILED').length
      },
      results: testResults,
      testData: {
        products: testProducts.map(p => ({
          id: p.id,
          name: p.name,
          stockQuantity: p.stockQuantity,
          stockStatus: p.stockStatus
        }))
      },
      nextSteps: [
        'Visit http://localhost:3000/admin/stok-yonetimi to test the UI',
        'Try creating stock movements using the modal',
        'Test filtering and pagination in stock history',
        'Verify stock status indicators and alerts'
      ]
    })

  } catch (error: any) {
    console.error('❌ UI test suite failed:', error)
    return NextResponse.json({
      success: false,
      error: 'UI test suite execution failed',
      details: error.message,
      results: testResults
    }, { status: 500 })
  }
}
