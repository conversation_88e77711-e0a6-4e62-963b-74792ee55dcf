import { CategoryGridClient } from "@/components/category-card"

// Fallback kategoriler - API çalışmazsa kullanılacak
const fallbackCategories = [
  {
    id: "1",
    name: "<PERSON><PERSON>",
    slug: "kulak-koruma",
    description: "<PERSON><PERSON><PERSON><PERSON>ltüden korunma ekipmanları",
    productCount: 45,
    isActive: true,
    sortOrder: 1,
  },
  {
    id: "2",
    name: "<PERSON><PERSON>z <PERSON>rum<PERSON>",
    slug: "goz-koruma",
    description: "Güvenlik gözlükleri ve koruyucular",
    productCount: 32,
    isActive: true,
    sortOrder: 2,
  },
  {
    id: "3",
    name: "<PERSON> Koruma",
    slug: "el-koruma",
    description: "<PERSON>ş eldivenleri ve el koruyucuları",
    productCount: 67,
    isActive: true,
    sortOrder: 3,
  },
  {
    id: "4",
    name: "<PERSON>yak <PERSON>",
    slug: "ayak-koruma",
    description: "Güvenlik ayakkabıları ve botları",
    productCount: 28,
    isActive: true,
    sortOrder: 4,
  },
  {
    id: "5",
    name: "<PERSON>ş Koruma",
    slug: "bas-koruma",
    description: "Baretler ve kask çeşitleri",
    productCount: 19,
    isActive: true,
    sortOrder: 5,
  },
  {
    id: "6",
    name: "Vücut Koruma",
    slug: "vucut-koruma",
    description: "Koruyucu giysiler ve tulumlar",
    productCount: 41,
    isActive: true,
    sortOrder: 6,
  },
]

async function fetchMainCategories() {
  try {
    // Server-side fetch için tam URL kullan
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000'
    const response = await fetch(`${baseUrl}/api/categories/main`, {
      cache: 'no-store',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = await response.json()

    // API response format: { success: true, data: categories[] }
    if (result.success && Array.isArray(result.data)) {
      return result.data
    }

    return fallbackCategories
  } catch (error) {
    console.error("Kategoriler yüklenirken hata:", error)
    return fallbackCategories
  }
}

export async function CategoryGrid() {
  const categories = await fetchMainCategories()

  return (
    <section className="py-16 bg-slate-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">Ürün Kategorileri</h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            İş güvenliği için ihtiyacınız olan tüm koruyucu ekipmanları kategoriler halinde keşfedin
          </p>
        </div>

        <CategoryGridClient categories={categories} />
      </div>
    </section>
  )
}
