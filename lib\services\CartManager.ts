import { prisma } from '@/lib/prisma'
import { StockManager } from './StockManager'
import { calculateCurrentPrice } from '@/types'

export interface CartItemData {
  productId: string
  quantity: number
}

export interface EnhancedCartItem {
  id: string
  productId: string
  quantity: number
  unitPrice: number
  currentPrice: number
  totalPrice: number
  reservationId?: string
  product: {
    id: string
    name: string
    slug: string
    basePrice: number
    stockQuantity: number
    reservedStock: number
    stockStatus: string
    images: Array<{ url: string; alt?: string }>
    discounts: Array<{
      type: string
      value: number
      isActive: boolean
      startDate: string
      endDate: string
    }>
  }
}

export interface CartSummary {
  items: EnhancedCartItem[]
  totalItems: number
  subtotal: number
  discountAmount: number
  totalAmount: number
  hasStockIssues: boolean
  stockIssues: Array<{
    productId: string
    productName: string
    requestedQuantity: number
    availableStock: number
  }>
}

/**
 * Enhanced Cart Manager with database persistence and stock integration
 */
export class CartManager {
  
  /**
   * Get or create cart for session
   */
  static async getCart(sessionId: string, userId?: string): Promise<string> {
    let cart = await prisma.cart.findUnique({
      where: { sessionId }
    })
    
    if (!cart) {
      cart = await prisma.cart.create({
        data: {
          sessionId,
          userId,
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
        }
      })
    }
    
    return cart.id
  }
  
  /**
   * Add item to cart with stock reservation
   */
  static async addItem(
    sessionId: string, 
    itemData: CartItemData,
    userId?: string
  ): Promise<{ success: boolean; error?: string; cartItem?: EnhancedCartItem }> {
    try {
      return await prisma.$transaction(async (tx) => {
        // 1. Get or create cart
        const cartId = await this.getCart(sessionId, userId)
        
        // 2. Get product with current pricing
        const product = await tx.product.findUnique({
          where: { id: itemData.productId },
          include: {
            images: { take: 1 },
            discounts: {
              where: {
                isActive: true,
                startDate: { lte: new Date() },
                endDate: { gte: new Date() }
              }
            }
          }
        })
        
        if (!product) {
          return { success: false, error: 'Product not found' }
        }
        
        // 3. Calculate current price with discounts
        const priceInfo = calculateCurrentPrice(product.basePrice, product.discounts)
        
        // 4. Check if item already exists in cart
        const existingItem = await tx.cartItem.findUnique({
          where: { 
            cartId_productId: { 
              cartId, 
              productId: itemData.productId 
            } 
          }
        })
        
        const totalQuantity = existingItem 
          ? existingItem.quantity + itemData.quantity 
          : itemData.quantity
        
        // 5. Validate and reserve stock
        const stockValidation = await StockManager.validateStock(
          itemData.productId, 
          totalQuantity
        )
        
        if (!stockValidation.isValid) {
          return { 
            success: false, 
            error: stockValidation.error 
          }
        }
        
        // 6. Reserve additional stock
        const reservation = await StockManager.reserveStock(
          itemData.productId,
          itemData.quantity,
          sessionId
        )
        
        if (!reservation.success) {
          return { 
            success: false, 
            error: reservation.error 
          }
        }
        
        // 7. Update or create cart item
        let cartItem
        if (existingItem) {
          // Release old reservation if exists
          if (existingItem.reservationId) {
            await StockManager.releaseReservation(existingItem.reservationId)
          }
          
          cartItem = await tx.cartItem.update({
            where: { id: existingItem.id },
            data: {
              quantity: totalQuantity,
              currentPrice: priceInfo.currentPrice,
              totalPrice: totalQuantity * priceInfo.currentPrice,
              reservationId: reservation.reservationId
            }
          })
        } else {
          cartItem = await tx.cartItem.create({
            data: {
              cartId,
              productId: itemData.productId,
              quantity: itemData.quantity,
              unitPrice: product.basePrice,
              currentPrice: priceInfo.currentPrice,
              totalPrice: itemData.quantity * priceInfo.currentPrice,
              reservationId: reservation.reservationId
            }
          })
        }
        
        // 8. Update cart totals
        await this.updateCartTotals(cartId, tx)
        
        // 9. Return enhanced cart item
        const enhancedItem: EnhancedCartItem = {
          id: cartItem.id,
          productId: product.id,
          quantity: cartItem.quantity,
          unitPrice: cartItem.unitPrice,
          currentPrice: cartItem.currentPrice,
          totalPrice: cartItem.totalPrice,
          reservationId: cartItem.reservationId || undefined,
          product: {
            id: product.id,
            name: product.name,
            slug: product.slug,
            basePrice: product.basePrice,
            stockQuantity: product.stockQuantity,
            reservedStock: product.reservedStock,
            stockStatus: product.stockStatus,
            images: product.images.map(img => ({ url: img.url, alt: img.alt })),
            discounts: product.discounts.map(d => ({
              type: d.type,
              value: d.value,
              isActive: d.isActive,
              startDate: d.startDate.toISOString(),
              endDate: d.endDate.toISOString()
            }))
          }
        }
        
        return { success: true, cartItem: enhancedItem }
      })
    } catch (error) {
      console.error('Add to cart error:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }
  
  /**
   * Update cart item quantity
   */
  static async updateItemQuantity(
    sessionId: string,
    cartItemId: string,
    newQuantity: number
  ): Promise<{ success: boolean; error?: string }> {
    try {
      return await prisma.$transaction(async (tx) => {
        const cartItem = await tx.cartItem.findFirst({
          where: {
            id: cartItemId,
            cart: { sessionId }
          }
        })
        
        if (!cartItem) {
          return { success: false, error: 'Cart item not found' }
        }
        
        if (newQuantity <= 0) {
          return await this.removeItem(sessionId, cartItemId)
        }
        
        // Validate new quantity
        const stockValidation = await StockManager.validateStock(
          cartItem.productId,
          newQuantity
        )
        
        if (!stockValidation.isValid) {
          return { success: false, error: stockValidation.error }
        }
        
        // Release old reservation
        if (cartItem.reservationId) {
          await StockManager.releaseReservation(cartItem.reservationId)
        }
        
        // Create new reservation
        const reservation = await StockManager.reserveStock(
          cartItem.productId,
          newQuantity,
          sessionId,
          cartItemId
        )
        
        if (!reservation.success) {
          return { success: false, error: reservation.error }
        }
        
        // Update cart item
        await tx.cartItem.update({
          where: { id: cartItemId },
          data: {
            quantity: newQuantity,
            totalPrice: newQuantity * cartItem.currentPrice,
            reservationId: reservation.reservationId
          }
        })
        
        // Update cart totals
        await this.updateCartTotals(cartItem.cartId, tx)
        
        return { success: true }
      })
    } catch (error) {
      console.error('Update cart item error:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }
  
  /**
   * Remove item from cart
   */
  static async removeItem(
    sessionId: string,
    cartItemId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      return await prisma.$transaction(async (tx) => {
        const cartItem = await tx.cartItem.findFirst({
          where: {
            id: cartItemId,
            cart: { sessionId }
          }
        })
        
        if (!cartItem) {
          return { success: false, error: 'Cart item not found' }
        }
        
        // Release stock reservation
        if (cartItem.reservationId) {
          await StockManager.releaseReservation(cartItem.reservationId)
        }
        
        // Remove cart item
        await tx.cartItem.delete({
          where: { id: cartItemId }
        })
        
        // Update cart totals
        await this.updateCartTotals(cartItem.cartId, tx)
        
        return { success: true }
      })
    } catch (error) {
      console.error('Remove cart item error:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }
  
  /**
   * Get cart summary with enhanced data
   */
  static async getCartSummary(sessionId: string): Promise<CartSummary> {
    const cart = await prisma.cart.findUnique({
      where: { sessionId },
      include: {
        items: {
          include: {
            product: {
              include: {
                images: { take: 1 },
                discounts: {
                  where: {
                    isActive: true,
                    startDate: { lte: new Date() },
                    endDate: { gte: new Date() }
                  }
                }
              }
            }
          }
        }
      }
    })
    
    if (!cart) {
      return {
        items: [],
        totalItems: 0,
        subtotal: 0,
        discountAmount: 0,
        totalAmount: 0,
        hasStockIssues: false,
        stockIssues: []
      }
    }
    
    const items: EnhancedCartItem[] = []
    const stockIssues: CartSummary['stockIssues'] = []
    let subtotal = 0
    let discountAmount = 0
    
    for (const item of cart.items) {
      // Check current stock availability
      const stockInfo = await StockManager.getStockInfo(item.productId)
      
      if (item.quantity > stockInfo.availableStock) {
        stockIssues.push({
          productId: item.productId,
          productName: item.product.name,
          requestedQuantity: item.quantity,
          availableStock: stockInfo.availableStock
        })
      }
      
      // Calculate current pricing
      const priceInfo = calculateCurrentPrice(
        item.product.basePrice, 
        item.product.discounts
      )
      
      const enhancedItem: EnhancedCartItem = {
        id: item.id,
        productId: item.productId,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        currentPrice: priceInfo.currentPrice,
        totalPrice: item.quantity * priceInfo.currentPrice,
        reservationId: item.reservationId || undefined,
        product: {
          id: item.product.id,
          name: item.product.name,
          slug: item.product.slug,
          basePrice: item.product.basePrice,
          stockQuantity: item.product.stockQuantity,
          reservedStock: item.product.reservedStock,
          stockStatus: item.product.stockStatus,
          images: item.product.images.map(img => ({ url: img.url, alt: img.alt })),
          discounts: item.product.discounts.map(d => ({
            type: d.type,
            value: d.value,
            isActive: d.isActive,
            startDate: d.startDate.toISOString(),
            endDate: d.endDate.toISOString()
          }))
        }
      }
      
      items.push(enhancedItem)
      subtotal += item.quantity * item.product.basePrice
      discountAmount += item.quantity * (item.product.basePrice - priceInfo.currentPrice)
    }
    
    return {
      items,
      totalItems: items.reduce((sum, item) => sum + item.quantity, 0),
      subtotal,
      discountAmount,
      totalAmount: subtotal - discountAmount,
      hasStockIssues: stockIssues.length > 0,
      stockIssues
    }
  }
  
  /**
   * Update cart totals (internal helper)
   */
  private static async updateCartTotals(cartId: string, tx: any) {
    const cartItems = await tx.cartItem.findMany({
      where: { cartId }
    })
    
    const totalItems = cartItems.reduce((sum: number, item: any) => sum + item.quantity, 0)
    const totalAmount = cartItems.reduce((sum: number, item: any) => sum + item.totalPrice, 0)
    
    await tx.cart.update({
      where: { id: cartId },
      data: {
        totalItems,
        totalAmount,
        updatedAt: new Date()
      }
    })
  }
  
  /**
   * Clear expired carts (scheduled job)
   */
  static async clearExpiredCarts(): Promise<number> {
    try {
      const expiredCarts = await prisma.cart.findMany({
        where: { expiresAt: { lt: new Date() } },
        include: { items: true }
      })
      
      let clearedCount = 0
      
      for (const cart of expiredCarts) {
        // Release all reservations
        for (const item of cart.items) {
          if (item.reservationId) {
            await StockManager.releaseReservation(item.reservationId)
          }
        }
        
        // Delete cart (cascade will delete items)
        await prisma.cart.delete({
          where: { id: cart.id }
        })
        
        clearedCount++
      }
      
      return clearedCount
    } catch (error) {
      console.error('Clear expired carts error:', error)
      return 0
    }
  }
}
