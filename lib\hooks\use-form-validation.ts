'use client'

import { useState, useCallback, useEffect } from 'react'
import { ZodSchema, ZodError } from 'zod'

export interface ValidationError {
  field: string
  message: string
}

export interface FormValidationState {
  errors: Record<string, string>
  isValid: boolean
  isValidating: boolean
  touchedFields: Set<string>
}

export interface UseFormValidationOptions<T> {
  schema: ZodSchema<T>
  validateOnChange?: boolean
  validateOnBlur?: boolean
  debounceMs?: number
}

export function useFormValidation<T extends Record<string, any>>({
  schema,
  validateOnChange = true,
  validateOnBlur = true,
  debounceMs = 300
}: UseFormValidationOptions<T>) {
  const [state, setState] = useState<FormValidationState>({
    errors: {},
    isValid: true,
    isValidating: false,
    touchedFields: new Set()
  })

  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null)

  // Validate a single field
  const validateField = useCallback((fieldName: string, value: any, allValues: T) => {
    try {
      // Create a partial schema for the specific field
      const fieldSchema = schema.pick({ [fieldName]: true } as any)
      fieldSchema.parse({ [fieldName]: value })
      
      // If validation passes, remove error for this field
      setState(prev => ({
        ...prev,
        errors: {
          ...prev.errors,
          [fieldName]: ''
        }
      }))
      
      return true
    } catch (error) {
      if (error instanceof ZodError) {
        const fieldError = error.errors.find(err => err.path.includes(fieldName))
        if (fieldError) {
          setState(prev => ({
            ...prev,
            errors: {
              ...prev.errors,
              [fieldName]: fieldError.message
            }
          }))
        }
      }
      return false
    }
  }, [schema])

  // Validate all fields
  const validateAll = useCallback((values: T) => {
    setState(prev => ({ ...prev, isValidating: true }))
    
    try {
      schema.parse(values)
      setState(prev => ({
        ...prev,
        errors: {},
        isValid: true,
        isValidating: false
      }))
      return { isValid: true, errors: {} }
    } catch (error) {
      if (error instanceof ZodError) {
        const errors: Record<string, string> = {}
        error.errors.forEach(err => {
          const fieldName = err.path.join('.')
          errors[fieldName] = err.message
        })
        
        setState(prev => ({
          ...prev,
          errors,
          isValid: false,
          isValidating: false
        }))
        
        return { isValid: false, errors }
      }
      
      setState(prev => ({
        ...prev,
        isValid: false,
        isValidating: false
      }))
      
      return { isValid: false, errors: { general: 'Validation failed' } }
    }
  }, [schema])

  // Handle field change with debounced validation
  const handleFieldChange = useCallback((fieldName: string, value: any, allValues: T) => {
    // Mark field as touched
    setState(prev => ({
      ...prev,
      touchedFields: new Set([...prev.touchedFields, fieldName])
    }))

    if (validateOnChange) {
      // Clear existing timer
      if (debounceTimer) {
        clearTimeout(debounceTimer)
      }

      // Set new timer for debounced validation
      const timer = setTimeout(() => {
        validateField(fieldName, value, allValues)
      }, debounceMs)

      setDebounceTimer(timer)
    }
  }, [validateOnChange, validateField, debounceMs, debounceTimer])

  // Handle field blur
  const handleFieldBlur = useCallback((fieldName: string, value: any, allValues: T) => {
    // Mark field as touched
    setState(prev => ({
      ...prev,
      touchedFields: new Set([...prev.touchedFields, fieldName])
    }))

    if (validateOnBlur) {
      validateField(fieldName, value, allValues)
    }
  }, [validateOnBlur, validateField])

  // Clear errors
  const clearErrors = useCallback(() => {
    setState(prev => ({
      ...prev,
      errors: {},
      isValid: true
    }))
  }, [])

  // Clear specific field error
  const clearFieldError = useCallback((fieldName: string) => {
    setState(prev => ({
      ...prev,
      errors: {
        ...prev.errors,
        [fieldName]: ''
      }
    }))
  }, [])

  // Reset validation state
  const reset = useCallback(() => {
    setState({
      errors: {},
      isValid: true,
      isValidating: false,
      touchedFields: new Set()
    })
  }, [])

  // Get error for specific field
  const getFieldError = useCallback((fieldName: string) => {
    return state.errors[fieldName] || ''
  }, [state.errors])

  // Check if field has error
  const hasFieldError = useCallback((fieldName: string) => {
    return Boolean(state.errors[fieldName])
  }, [state.errors])

  // Check if field is touched
  const isFieldTouched = useCallback((fieldName: string) => {
    return state.touchedFields.has(fieldName)
  }, [state.touchedFields])

  // Get field validation state
  const getFieldState = useCallback((fieldName: string) => {
    return {
      error: getFieldError(fieldName),
      hasError: hasFieldError(fieldName),
      isTouched: isFieldTouched(fieldName),
      showError: isFieldTouched(fieldName) && hasFieldError(fieldName)
    }
  }, [getFieldError, hasFieldError, isFieldTouched])

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer)
      }
    }
  }, [debounceTimer])

  return {
    // State
    errors: state.errors,
    isValid: state.isValid,
    isValidating: state.isValidating,
    touchedFields: state.touchedFields,
    
    // Methods
    validateAll,
    validateField,
    handleFieldChange,
    handleFieldBlur,
    clearErrors,
    clearFieldError,
    reset,
    
    // Field helpers
    getFieldError,
    hasFieldError,
    isFieldTouched,
    getFieldState
  }
}
