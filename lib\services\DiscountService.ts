import { prisma } from "@/lib/prisma"
import type { ProductDiscount, DiscountType, DiscountUsage } from "@/types"

export class DiscountService {
  
  /**
   * Get active discounts for a product
   */
  static async getActiveDiscountsForProduct(productId: string, variantId?: string): Promise<ProductDiscount[]> {
    const now = new Date()
    
    const discounts = await prisma.productDiscount.findMany({
      where: {
        OR: [
          { productId: productId },
          { productId: null }, // Global discounts
          { 
            product: {
              categoryId: {
                in: await this.getProductCategoryIds(productId)
              }
            }
          }
        ],
        isActive: true,
        startDate: { lte: now },
        endDate: { gte: now },
        OR: [
          { usageLimit: null },
          { usageCount: { lt: prisma.productDiscount.fields.usageLimit } }
        ]
      },
      include: {
        usages: true
      },
      orderBy: {
        priority: 'desc'
      }
    })
    
    return discounts as ProductDiscount[]
  }
  
  /**
   * Calculate discount amount for a product
   */
  static calculateDiscountAmount(
    originalPrice: number,
    discount: ProductDiscount,
    quantity: number = 1
  ): { discountAmount: number; finalPrice: number; applicable: boolean } {
    
    // Check minimum order amount
    if (discount.minOrderAmount && originalPrice * quantity < discount.minOrderAmount) {
      return { discountAmount: 0, finalPrice: originalPrice, applicable: false }
    }
    
    let discountAmount = 0
    
    switch (discount.type) {
      case 'PERCENTAGE':
        discountAmount = (originalPrice * discount.value) / 100
        if (discount.maxDiscount && discountAmount > discount.maxDiscount) {
          discountAmount = discount.maxDiscount
        }
        break
        
      case 'FIXED_AMOUNT':
        discountAmount = Math.min(discount.value, originalPrice)
        break
        
      case 'BUY_X_GET_Y':
        if (quantity >= (discount.buyQuantity || 1)) {
          const freeItems = Math.floor(quantity / (discount.buyQuantity || 1)) * (discount.getQuantity || 1)
          const discountPercent = discount.getDiscountPercent || 100
          discountAmount = (freeItems * originalPrice * discountPercent) / 100
        }
        break
        
      case 'BULK_DISCOUNT':
        // Implement bulk discount logic based on conditions
        if (discount.conditions) {
          const conditions = discount.conditions as any
          for (const tier of conditions.tiers || []) {
            if (quantity >= tier.minQuantity) {
              discountAmount = (originalPrice * quantity * tier.discount) / 100
            }
          }
        }
        break
        
      default:
        discountAmount = 0
    }
    
    const finalPrice = Math.max(0, originalPrice - discountAmount)
    
    return {
      discountAmount,
      finalPrice,
      applicable: discountAmount > 0
    }
  }
  
  /**
   * Apply discount and track usage
   */
  static async applyDiscount(
    discountId: string,
    productId: string,
    originalAmount: number,
    discountAmount: number,
    finalAmount: number,
    userId?: string,
    orderId?: string,
    variantId?: string
  ): Promise<DiscountUsage> {
    
    // Create usage record
    const usage = await prisma.discountUsage.create({
      data: {
        discountId,
        userId,
        orderId,
        productId,
        variantId,
        originalAmount,
        discountAmount,
        finalAmount
      }
    })
    
    // Update discount usage count
    await prisma.productDiscount.update({
      where: { id: discountId },
      data: {
        usageCount: { increment: 1 },
        totalSavings: { increment: discountAmount }
      }
    })
    
    return usage as DiscountUsage
  }
  
  /**
   * Create a new discount
   */
  static async createDiscount(discountData: Omit<ProductDiscount, 'id' | 'usageCount' | 'clickCount' | 'conversionCount' | 'totalSavings' | 'createdAt' | 'updatedAt'>): Promise<ProductDiscount> {
    const discount = await prisma.productDiscount.create({
      data: {
        ...discountData,
        usageCount: 0,
        clickCount: 0,
        conversionCount: 0,
        totalSavings: 0
      },
      include: {
        usages: true
      }
    })
    
    return discount as ProductDiscount
  }
  
  /**
   * Get discount analytics
   */
  static async getDiscountAnalytics(discountId: string) {
    const discount = await prisma.productDiscount.findUnique({
      where: { id: discountId },
      include: {
        usages: {
          include: {
            discount: true
          }
        }
      }
    })
    
    if (!discount) return null
    
    const totalUsage = discount.usages.length
    const totalSavings = discount.usages.reduce((sum, usage) => sum + usage.discountAmount, 0)
    const avgDiscountAmount = totalUsage > 0 ? totalSavings / totalUsage : 0
    
    return {
      discount,
      analytics: {
        totalUsage,
        totalSavings,
        avgDiscountAmount,
        conversionRate: discount.clickCount > 0 ? (discount.conversionCount / discount.clickCount) * 100 : 0
      }
    }
  }
  
  /**
   * Helper method to get product category IDs
   */
  private static async getProductCategoryIds(productId: string): Promise<string[]> {
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: { categoryId: true }
    })
    
    return product ? [product.categoryId] : []
  }
  
  /**
   * Validate discount code
   */
  static async validateDiscountCode(code: string, userId?: string): Promise<{ valid: boolean; discount?: ProductDiscount; message?: string }> {
    const now = new Date()
    
    const discount = await prisma.productDiscount.findFirst({
      where: {
        code: code,
        isActive: true,
        startDate: { lte: now },
        endDate: { gte: now }
      },
      include: {
        usages: true
      }
    })
    
    if (!discount) {
      return { valid: false, message: "Geçersiz indirim kodu" }
    }
    
    // Check usage limit
    if (discount.usageLimit && discount.usageCount >= discount.usageLimit) {
      return { valid: false, message: "İndirim kodu kullanım limitine ulaştı" }
    }
    
    // Check user limit
    if (discount.userLimit && userId) {
      const userUsageCount = discount.usages.filter(usage => usage.userId === userId).length
      if (userUsageCount >= discount.userLimit) {
        return { valid: false, message: "Bu indirim kodunu kullanım limitinize ulaştınız" }
      }
    }
    
    return { valid: true, discount: discount as ProductDiscount }
  }
}

export default DiscountService
