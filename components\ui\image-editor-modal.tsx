"use client"

import React, { useState, useRef, use<PERSON><PERSON>back, useEffect } from 'react'
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, <PERSON><PERSON>Title, DialogFooter } from './dialog'
import { Button } from './button'
import { Input } from './input'
import { Label } from './label'
import { Slider } from './slider'
import { Card, CardContent } from './card'
import { Crop, RotateCw, ZoomIn, ZoomOut, Save, X } from 'lucide-react'
import { cn } from '@/lib/utils'

interface ImageEditorModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  imageFile: File | null
  onSave: (processedImageBlob: Blob) => void
  targetDimensions?: {
    width: number
    height: number
  }
}

interface CropArea {
  x: number
  y: number
  width: number
  height: number
}

export function ImageEditorModal({
  open,
  onOpenChange,
  imageFile,
  onSave,
  targetDimensions = { width: 400, height: 300 }
}: ImageEditorModalProps) {
  console.log('🎨 ImageEditorModal render:', {
    open,
    imageFile: imageFile ? { name: imageFile.name, size: imageFile.size } : null,
    targetDimensions
  })
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  const imageContainerRef = useRef<HTMLDivElement>(null)
  const [imageUrl, setImageUrl] = useState<string>('')
  const [cropArea, setCropArea] = useState<CropArea>({ x: 0, y: 0, width: 100, height: 100 })
  const [scale, setScale] = useState(1)
  const [rotation, setRotation] = useState(0)
  const [finalWidth, setFinalWidth] = useState(targetDimensions.width)
  const [finalHeight, setFinalHeight] = useState(targetDimensions.height)
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [originalImageDimensions, setOriginalImageDimensions] = useState({ width: 0, height: 0 })
  const [displayImageDimensions, setDisplayImageDimensions] = useState({ width: 0, height: 0 })

  // Load image when file changes
  useEffect(() => {
    console.log('🎨 ImageEditorModal useEffect triggered:', { imageFile })
    if (imageFile && open) {
      console.log('🎨 Creating object URL for file:', imageFile.name)
      const url = URL.createObjectURL(imageFile)
      setImageUrl(url)
      console.log('🎨 Set image URL:', url)

      return () => {
        console.log('🎨 Cleaning up object URL:', url)
        URL.revokeObjectURL(url)
      }
    } else if (!open) {
      // Clear image URL when modal is closed
      setImageUrl('')
    }
  }, [imageFile, open])

  // Reset state when modal closes
  useEffect(() => {
    if (!open) {
      console.log('🎨 ImageEditorModal closed, resetting state')
      setImageUrl('')
      setCropArea({ x: 0, y: 0, width: 100, height: 100 })
      setScale(1)
      setRotation(0)
      setFinalWidth(targetDimensions.width)
      setFinalHeight(targetDimensions.height)
      setIsDragging(false)
      setOriginalImageDimensions({ width: 0, height: 0 })
      setDisplayImageDimensions({ width: 0, height: 0 })
    }
  }, [open, targetDimensions.width, targetDimensions.height])

  // Update crop area when finalWidth/finalHeight changes
  useEffect(() => {
    if (originalImageDimensions.width > 0 && originalImageDimensions.height > 0) {
      // Calculate crop area based on finalWidth/finalHeight and scale
      const scaledWidth = finalWidth * scale
      const scaledHeight = finalHeight * scale

      // Ensure crop area fits within image bounds
      const maxWidth = Math.min(scaledWidth, originalImageDimensions.width)
      const maxHeight = Math.min(scaledHeight, originalImageDimensions.height)

      // Center the crop area
      const x = Math.max(0, (originalImageDimensions.width - maxWidth) / 2)
      const y = Math.max(0, (originalImageDimensions.height - maxHeight) / 2)

      setCropArea({
        x,
        y,
        width: maxWidth,
        height: maxHeight
      })

      console.log('🎨 Updated crop area:', { x, y, width: maxWidth, height: maxHeight, scale, finalWidth, finalHeight })
    }
  }, [finalWidth, finalHeight, scale, originalImageDimensions])

  // Handle image load to get original dimensions
  const handleImageLoad = () => {
    if (imageRef.current) {
      const img = imageRef.current
      setOriginalImageDimensions({ width: img.naturalWidth, height: img.naturalHeight })
      setDisplayImageDimensions({ width: img.offsetWidth, height: img.offsetHeight })
      console.log('🎨 Image loaded:', {
        natural: { width: img.naturalWidth, height: img.naturalHeight },
        display: { width: img.offsetWidth, height: img.offsetHeight }
      })
    }
  }

  // Calculate crop area position relative to displayed image
  const getCropAreaStyle = () => {
    if (displayImageDimensions.width === 0 || originalImageDimensions.width === 0) {
      return { display: 'none' }
    }

    // Calculate scale factor between displayed image and original image
    const scaleX = displayImageDimensions.width / originalImageDimensions.width
    const scaleY = displayImageDimensions.height / originalImageDimensions.height

    return {
      position: 'absolute' as const,
      left: `${cropArea.x * scaleX}px`,
      top: `${cropArea.y * scaleY}px`,
      width: `${cropArea.width * scaleX}px`,
      height: `${cropArea.height * scaleY}px`,
      border: '2px solid #3b82f6',
      backgroundColor: 'rgba(59, 130, 246, 0.2)',
      cursor: 'move',
      pointerEvents: 'auto' as const
    }
  }

  // Handle crop area drag
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault()
    setIsDragging(true)
    setDragStart({ x: e.clientX, y: e.clientY })
  }

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || displayImageDimensions.width === 0 || originalImageDimensions.width === 0) return

    const deltaX = e.clientX - dragStart.x
    const deltaY = e.clientY - dragStart.y

    // Calculate scale factor between displayed image and original image
    const scaleX = originalImageDimensions.width / displayImageDimensions.width
    const scaleY = originalImageDimensions.height / displayImageDimensions.height

    // Convert screen delta to original image coordinates
    const originalDeltaX = deltaX * scaleX
    const originalDeltaY = deltaY * scaleY

    setCropArea(prev => ({
      ...prev,
      x: Math.max(0, Math.min(originalImageDimensions.width - prev.width, prev.x + originalDeltaX)),
      y: Math.max(0, Math.min(originalImageDimensions.height - prev.height, prev.y + originalDeltaY))
    }))

    setDragStart({ x: e.clientX, y: e.clientY })
  }, [isDragging, dragStart, displayImageDimensions, originalImageDimensions])

  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
  }, [])

  // Add global mouse event listeners for dragging
  useEffect(() => {
    if (isDragging && open) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [isDragging, handleMouseMove, handleMouseUp, open])

  // Cleanup event listeners when modal closes
  useEffect(() => {
    if (!open) {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      setIsDragging(false)
    }
  }, [open, handleMouseMove, handleMouseUp])

  // Process and save image
  const handleSave = async () => {
    console.log('🎨 ImageEditorModal: handleSave called', {
      imageRef: !!imageRef.current,
      cropArea,
      finalWidth,
      finalHeight,
      rotation
    })

    const image = imageRef.current
    if (!image) {
      console.error('❌ ImageEditorModal: No image reference found')
      return
    }

    console.log('🎨 ImageEditorModal: Creating canvas for processing')

    // Create a canvas for processing
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    if (!ctx) {
      console.error('❌ ImageEditorModal: Failed to get canvas context')
      return
    }

    // Set canvas size to final dimensions (not scaled crop area)
    canvas.width = finalWidth
    canvas.height = finalHeight

    console.log('🎨 ImageEditorModal: Canvas created', {
      canvasWidth: canvas.width,
      canvasHeight: canvas.height
    })

    try {
      // Save context state
      ctx.save()

      // Apply rotation if needed
      if (rotation !== 0) {
        ctx.translate(finalWidth / 2, finalHeight / 2)
        ctx.rotate((rotation * Math.PI) / 180)
        ctx.translate(-finalWidth / 2, -finalHeight / 2)
        console.log('🎨 ImageEditorModal: Applied rotation', rotation)
      }

      // Draw the cropped area to the canvas at final dimensions
      ctx.drawImage(
        image,
        cropArea.x,
        cropArea.y,
        cropArea.width,
        cropArea.height,
        0,
        0,
        finalWidth,
        finalHeight
      )

      console.log('🎨 ImageEditorModal: Image drawn to canvas', {
        sourceArea: cropArea,
        targetSize: { width: finalWidth, height: finalHeight }
      })

      // Restore context state
      ctx.restore()

      // Convert to blob and save
      console.log('🎨 ImageEditorModal: Converting canvas to blob...')
      canvas.toBlob((blob) => {
        if (blob) {
          console.log('✅ ImageEditorModal: Blob created successfully', {
            blobSize: blob.size,
            blobType: blob.type
          })

          console.log('🎨 ImageEditorModal: Calling onSave callback...')
          onSave(blob)

          console.log('🎨 ImageEditorModal: Closing modal...')
          onOpenChange(false)
        } else {
          console.error('❌ ImageEditorModal: Failed to create blob from canvas')
        }
      }, 'image/jpeg', 0.9)
    } catch (error) {
      console.error('❌ ImageEditorModal: Error during image processing:', error)
    }
  }

  // Reset to defaults
  const handleReset = () => {
    setScale(1)
    setRotation(0)
    setFinalWidth(targetDimensions.width)
    setFinalHeight(targetDimensions.height)
    // Crop area will be recalculated by useEffect when finalWidth/finalHeight change
  }

  console.log('🎨 ImageEditorModal rendering with open:', open)

  // Add a simple test div when modal should be open
  if (open) {
    console.log('🎨 Modal should be visible now!')
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Crop className="h-5 w-5" />
            Görsel Düzenle
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Image with Crop Overlay */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Görsel Düzenleme</h3>
            <div
              ref={imageContainerRef}
              className="relative border rounded-lg overflow-hidden bg-gray-50"
              style={{ minHeight: '300px' }}
            >
              {imageUrl && (
                <>
                  <img
                    ref={imageRef}
                    src={imageUrl}
                    alt="Original"
                    className="max-w-full h-auto block"
                    onLoad={handleImageLoad}
                    style={{
                      transform: `rotate(${rotation}deg)`,
                      transformOrigin: 'center'
                    }}
                  />

                  {/* Crop overlay */}
                  <div
                    style={getCropAreaStyle()}
                    onMouseDown={handleMouseDown}
                  >
                    <div className="absolute inset-0 border-2 border-blue-500 bg-blue-500/20">
                      <div className="absolute top-1 left-1 bg-blue-500 text-white text-xs px-1 rounded">
                        {Math.round(cropArea.width)} × {Math.round(cropArea.height)}
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Controls */}
          <div className="space-y-4">
            <h3 className="text-sm font-medium">Ayarlar</h3>

            {/* Dimension Controls */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="width">Genişlik (px)</Label>
                <Input
                  id="width"
                  type="number"
                  value={finalWidth}
                  onChange={(e) => setFinalWidth(Number(e.target.value))}
                  min="50"
                  max="2000"
                />
              </div>
              <div>
                <Label htmlFor="height">Yükseklik (px)</Label>
                <Input
                  id="height"
                  type="number"
                  value={finalHeight}
                  onChange={(e) => setFinalHeight(Number(e.target.value))}
                  min="50"
                  max="2000"
                />
              </div>
            </div>

            {/* Scale Control */}
            <div>
              <Label>Ölçek: {scale.toFixed(2)}x</Label>
              <div className="flex items-center gap-2 mt-2">
                <ZoomOut className="h-4 w-4" />
                <Slider
                  value={[scale]}
                  onValueChange={(value) => setScale(value[0])}
                  min={0.1}
                  max={3}
                  step={0.1}
                  className="flex-1"
                />
                <ZoomIn className="h-4 w-4" />
              </div>
            </div>

            {/* Rotation Control */}
            <div>
              <Label>Döndürme: {rotation}°</Label>
              <div className="flex items-center gap-2 mt-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setRotation(prev => prev - 90)}
                >
                  <RotateCw className="h-4 w-4 rotate-180" />
                </Button>
                <Slider
                  value={[rotation]}
                  onValueChange={(value) => setRotation(value[0])}
                  min={-180}
                  max={180}
                  step={15}
                  className="flex-1"
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setRotation(prev => prev + 90)}
                >
                  <RotateCw className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Info */}
            <div className="text-xs text-gray-500 space-y-1">
              <p>Orijinal boyut: {originalImageDimensions.width} × {originalImageDimensions.height}</p>
              <p>Kırpma alanı: {Math.round(cropArea.width)} × {Math.round(cropArea.height)}</p>
              <p>Çıktı boyutu: {finalWidth} × {finalHeight}</p>
              <p>Ölçek: {scale.toFixed(2)}x</p>
              <p className="text-blue-600">• Mavi alan kırpılacak bölgeyi gösterir</p>
              <p className="text-blue-600">• Genişlik/Yükseklik değerleri kırpma alanını kontrol eder</p>
              <p className="text-blue-600">• Ölçek kırpma alanının görsel boyutunu değiştirir</p>
            </div>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button type="button" variant="outline" onClick={handleReset}>
            Sıfırla
          </Button>
          <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
            <X className="h-4 w-4 mr-2" />
            İptal
          </Button>
          <Button type="button" onClick={handleSave}>
            <Save className="h-4 w-4 mr-2" />
            Kaydet ve Uygula
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
