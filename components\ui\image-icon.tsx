import type React from "react"
import Image from "next/image"
import { cn } from "@/lib/utils"

interface ImageIconProps extends React.ComponentProps<typeof Image> {
  src: string
  alt: string
}

const ImageIcon = ({ src, alt, className, ...props }: ImageIconProps) => {
  return (
    <div className={cn("relative overflow-hidden", className)}>
      <Image
        src={src || "/placeholder.svg"}
        alt={alt}
        layout="fill"
        objectFit="cover"
        className="absolute inset-0"
        {...props}
      />
    </div>
  )
}

export default ImageIcon
