"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertCircle, ArrowLeft, Home, Search } from "lucide-react"

export default function ProductNotFound() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
            <AlertCircle className="w-8 h-8 text-red-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900">Ürün Bulunamadı</CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-6">
          <p className="text-gray-600">
            <PERSON>dığın<PERSON>z ürün bulunamad<PERSON>. <PERSON><PERSON><PERSON><PERSON> kaldırılmış, taşınmış veya geçici olarak mevcut olmayabilir.
          </p>

          <div className="space-y-3">
            <Button asChild className="w-full">
              <Link href="/">
                <Home className="w-4 h-4 mr-2" />
                Ana Sayfaya Dön
              </Link>
            </Button>

            <Button variant="outline" asChild className="w-full bg-transparent">
              <Link href="/urunler">
                <Search className="w-4 h-4 mr-2" />
                Tüm Ürünleri Görüntüle
              </Link>
            </Button>

            <Button variant="ghost" onClick={() => window.history.back()} className="w-full">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Geri Dön
            </Button>
          </div>

          <div className="pt-4 border-t">
            <p className="text-sm text-gray-500">
              Yardıma mı ihtiyacınız var?{" "}
              <Link href="/iletisim" className="text-orange-600 hover:text-orange-700 font-medium">
                İletişime geçin
              </Link>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
