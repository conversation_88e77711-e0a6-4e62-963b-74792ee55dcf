#!/usr/bin/env tsx

/**
 * Seeding Verification Script
 * 
 * This script verifies that the comprehensive seeding was successful
 * and that the stock data inconsistency issue has been resolved.
 * 
 * Usage:
 * npx tsx scripts/verify-seeding.ts
 */

import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function verifySeeding() {
  console.log('🔍 Verifying comprehensive seeding results...')
  console.log('=' .repeat(60))
  
  try {
    // 1. Check database schema
    console.log('\n📋 1. Database Schema Verification')
    await verifySchema()
    
    // 2. Check categories
    console.log('\n📂 2. Category Verification')
    await verifyCategories()
    
    // 3. Check products
    console.log('\n📦 3. Product Verification')
    await verifyProducts()
    
    // 4. Check stock data consistency
    console.log('\n📊 4. Stock Data Consistency Check')
    await verifyStockConsistency()
    
    // 5. Check product distribution
    console.log('\n📈 5. Product Distribution Analysis')
    await verifyDistribution()
    
    // 6. Sample data display
    console.log('\n🔍 6. Sample Data Preview')
    await displaySampleData()
    
    console.log('\n' + '=' .repeat(60))
    console.log('✅ Verification completed successfully!')
    console.log('\n🎯 Stock Data Inconsistency Status: RESOLVED')
    console.log('💡 Both product detail page and edit form will now show consistent stock data.')
    
  } catch (error) {
    console.error('\n❌ Verification failed:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

async function verifySchema() {
  try {
    const testProduct = await prisma.product.findFirst({
      select: {
        id: true,
        stockQuantity: true,
        minStockThreshold: true,
        maxStockCapacity: true,
        stockStatus: true
      }
    })
    
    if (!testProduct) {
      console.log('⚠️  No products found to test schema')
      return
    }
    
    const hasAllFields = testProduct.stockQuantity !== undefined && 
                        testProduct.minStockThreshold !== undefined && 
                        testProduct.maxStockCapacity !== undefined && 
                        testProduct.stockStatus !== undefined
    
    if (hasAllFields) {
      console.log('✅ All stock management fields are accessible')
    } else {
      console.log('❌ Some stock management fields are missing')
      console.log('   stockQuantity:', testProduct.stockQuantity !== undefined ? '✅' : '❌')
      console.log('   minStockThreshold:', testProduct.minStockThreshold !== undefined ? '✅' : '❌')
      console.log('   maxStockCapacity:', testProduct.maxStockCapacity !== undefined ? '✅' : '❌')
      console.log('   stockStatus:', testProduct.stockStatus !== undefined ? '✅' : '❌')
    }
    
  } catch (error) {
    console.log('❌ Schema verification failed:', error.message)
  }
}

async function verifyCategories() {
  const categories = await prisma.category.findMany({
    select: {
      id: true,
      name: true,
      productCount: true,
      isActive: true
    },
    orderBy: { sortOrder: 'asc' }
  })
  
  console.log(`📊 Found ${categories.length} categories:`)
  
  let totalProducts = 0
  categories.forEach(category => {
    console.log(`   ${category.name}: ${category.productCount} products ${category.isActive ? '✅' : '❌'}`)
    totalProducts += category.productCount
  })
  
  console.log(`📈 Total products across categories: ${totalProducts}`)
  
  if (categories.length >= 8) {
    console.log('✅ Category count meets requirement (8+)')
  } else {
    console.log('⚠️  Category count below requirement (8+)')
  }
}

async function verifyProducts() {
  const productCount = await prisma.product.count()
  const activeProducts = await prisma.product.count({ where: { isActive: true } })
  
  console.log(`📊 Total products: ${productCount}`)
  console.log(`📊 Active products: ${activeProducts}`)
  
  if (productCount >= 80) {
    console.log('✅ Product count meets requirement (80+)')
  } else {
    console.log(`⚠️  Product count below target (${productCount}/80)`)
  }
  
  // Check for required fields
  const productsWithCompleteData = await prisma.product.count({
    where: {
      AND: [
        { stockQuantity: { not: null } },
        { minStockThreshold: { not: null } },
        { stockStatus: { not: null } },
        { basePrice: { gt: 0 } },
        { brand: { not: '' } }
      ]
    }
  })
  
  console.log(`📊 Products with complete data: ${productsWithCompleteData}/${productCount}`)
  
  if (productsWithCompleteData === productCount) {
    console.log('✅ All products have complete stock and pricing data')
  } else {
    console.log(`⚠️  ${productCount - productsWithCompleteData} products have incomplete data`)
  }
}

async function verifyStockConsistency() {
  // Check for null stock values (the main issue we're fixing)
  const nullStockCount = await prisma.product.count({
    where: {
      OR: [
        { stockQuantity: null },
        { minStockThreshold: null },
        { stockStatus: null }
      ]
    }
  })
  
  if (nullStockCount === 0) {
    console.log('✅ No products with null stock values found')
    console.log('✅ Stock data inconsistency issue: RESOLVED')
  } else {
    console.log(`❌ Found ${nullStockCount} products with null stock values`)
    console.log('❌ Stock data inconsistency issue: NOT RESOLVED')
  }
  
  // Check stock status calculation consistency
  const inconsistentStock = await prisma.product.findMany({
    where: {
      OR: [
        // Out of stock but stockQuantity > 0
        { AND: [{ stockStatus: 'OUT_OF_STOCK' }, { stockQuantity: { gt: 0 } }] },
        // In stock but stockQuantity = 0
        { AND: [{ stockStatus: 'IN_STOCK' }, { stockQuantity: 0 }] }
      ]
    },
    select: { id: true, name: true, stockQuantity: true, stockStatus: true }
  })
  
  if (inconsistentStock.length === 0) {
    console.log('✅ Stock status calculations are consistent')
  } else {
    console.log(`⚠️  Found ${inconsistentStock.length} products with inconsistent stock status`)
    inconsistentStock.forEach(product => {
      console.log(`   - ${product.name}: ${product.stockQuantity} units but status is ${product.stockStatus}`)
    })
  }
}

async function verifyDistribution() {
  // Stock status distribution
  const stockDistribution = await prisma.product.groupBy({
    by: ['stockStatus'],
    _count: { stockStatus: true }
  })
  
  console.log('📊 Stock Status Distribution:')
  stockDistribution.forEach(group => {
    const percentage = ((group._count.stockStatus / await prisma.product.count()) * 100).toFixed(1)
    console.log(`   ${group.stockStatus}: ${group._count.stockStatus} products (${percentage}%)`)
  })
  
  // Category distribution
  const categoryDistribution = await prisma.product.groupBy({
    by: ['categoryId'],
    _count: { categoryId: true }
  })
  
  console.log(`📊 Products per category: ${categoryDistribution.length} categories`)
  const avgProductsPerCategory = categoryDistribution.reduce((sum, cat) => sum + cat._count.categoryId, 0) / categoryDistribution.length
  console.log(`📊 Average products per category: ${avgProductsPerCategory.toFixed(1)}`)
  
  if (avgProductsPerCategory >= 8) {
    console.log('✅ Category distribution meets requirement (8+ per category)')
  } else {
    console.log('⚠️  Some categories may have fewer than 8 products')
  }
}

async function displaySampleData() {
  const sampleProducts = await prisma.product.findMany({
    take: 5,
    select: {
      name: true,
      brand: true,
      basePrice: true,
      stockQuantity: true,
      minStockThreshold: true,
      stockStatus: true,
      category: {
        select: { name: true }
      }
    },
    orderBy: { createdAt: 'desc' }
  })
  
  console.log('🔍 Sample Products (Latest 5):')
  sampleProducts.forEach((product, index) => {
    console.log(`   ${index + 1}. ${product.name}`)
    console.log(`      Category: ${product.category.name}`)
    console.log(`      Brand: ${product.brand}`)
    console.log(`      Price: ${product.basePrice} TL`)
    console.log(`      Stock: ${product.stockQuantity} units (${product.stockStatus})`)
    console.log(`      Min Threshold: ${product.minStockThreshold}`)
    console.log('')
  })
}

// Run if called directly
if (require.main === module) {
  verifySeeding()
    .catch((error) => {
      console.error(error)
      process.exit(1)
    })
}
