import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Debug Stock API called')

    // Step 1: Check if stock fields exist by trying to select them
    let stockFieldsExist = false
    let testProduct = null
    let error = null

    try {
      testProduct = await prisma.product.findFirst({
        select: {
          id: true,
          name: true,
          stockQuantity: true,
          minStockThreshold: true,
          maxStockCapacity: true,
          stockStatus: true,
          trackStock: true,
          basePrice: true
        }
      })
      stockFieldsExist = true
    } catch (err: any) {
      error = err.message
      stockFieldsExist = false
    }

    // Step 2: If stock fields don't exist, try basic fields
    let basicProduct = null
    if (!stockFieldsExist) {
      try {
        basicProduct = await prisma.product.findFirst({
          select: {
            id: true,
            name: true,
            basePrice: true,
            trackStock: true
          }
        })
      } catch (err: any) {
        // Even basic fields failed
      }
    }

    // Step 3: Get all products with whatever fields are available
    let allProducts = []
    try {
      allProducts = await prisma.product.findMany({
        take: 3,
        select: {
          id: true,
          name: true,
          basePrice: true
        }
      })
    } catch (err: any) {
      // Ignore
    }

    return NextResponse.json({
      success: true,
      diagnosis: {
        stockFieldsExist,
        error: error,
        testProduct: testProduct,
        basicProduct: basicProduct,
        totalProducts: allProducts.length,
        sampleProducts: allProducts
      },
      recommendations: stockFieldsExist 
        ? ['Stock fields exist - check API transformation logic']
        : ['Stock fields missing - run migration: add_stock_management_fields.sql']
    })

  } catch (error: any) {
    console.error('Debug Stock API error:', error)
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 })
  }
}
