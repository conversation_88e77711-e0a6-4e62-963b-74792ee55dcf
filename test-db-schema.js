const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testDatabaseSchema() {
  try {
    console.log('Testing database connection...')
    
    // Test basic connection
    await prisma.$connect()
    console.log('✅ Database connected successfully')
    
    // Check if Product table exists and what fields it has
    const result = await prisma.$queryRaw`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'Product' 
      AND column_name LIKE '%stock%'
      ORDER BY column_name;
    `
    
    console.log('\n📋 Stock-related fields in Product table:')
    console.table(result)
    
    // Check if we have any products
    const productCount = await prisma.product.count()
    console.log(`\n📊 Total products in database: ${productCount}`)
    
    if (productCount > 0) {
      // Get a sample product to see what fields are available
      const sampleProduct = await prisma.product.findFirst({
        select: {
          id: true,
          name: true,
          stockQuantity: true,
          minStockThreshold: true,
          maxStockCapacity: true,
          trackStock: true,
          stockStatus: true
        }
      })
      
      console.log('\n🔍 Sample product stock data:')
      console.log(JSON.stringify(sampleProduct, null, 2))
    }
    
  } catch (error) {
    console.error('❌ Database test failed:', error.message)
    if (error.code) {
      console.error('Error code:', error.code)
    }
  } finally {
    await prisma.$disconnect()
  }
}

testDatabaseSchema()
