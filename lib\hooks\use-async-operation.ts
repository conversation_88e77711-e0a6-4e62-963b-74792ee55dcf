'use client'

import { useState, useCallback, useRef, useEffect } from 'react'

export interface AsyncOperationState<T> {
  data: T | null
  loading: boolean
  error: string | null
  success: boolean
}

export interface UseAsyncOperationOptions {
  onSuccess?: (data: any) => void
  onError?: (error: string) => void
  resetOnSuccess?: boolean
  resetOnError?: boolean
}

export function useAsyncOperation<T = any>(options: UseAsyncOperationOptions = {}) {
  const [state, setState] = useState<AsyncOperationState<T>>({
    data: null,
    loading: false,
    error: null,
    success: false
  })

  const isMountedRef = useRef(true)

  useEffect(() => {
    return () => {
      isMountedRef.current = false
    }
  }, [])

  const execute = useCallback(async (asyncFunction: () => Promise<T>) => {
    if (!isMountedRef.current) return

    setState(prev => ({
      ...prev,
      loading: true,
      error: null,
      success: false
    }))

    try {
      const result = await asyncFunction()
      
      if (!isMountedRef.current) return

      setState(prev => ({
        ...prev,
        data: result,
        loading: false,
        success: true
      }))

      if (options.onSuccess) {
        options.onSuccess(result)
      }

      if (options.resetOnSuccess) {
        setTimeout(() => {
          if (isMountedRef.current) {
            setState(prev => ({ ...prev, success: false }))
          }
        }, 3000)
      }

      return result
    } catch (error) {
      if (!isMountedRef.current) return

      const errorMessage = error instanceof Error ? error.message : 'An error occurred'
      
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage
      }))

      if (options.onError) {
        options.onError(errorMessage)
      }

      if (options.resetOnError) {
        setTimeout(() => {
          if (isMountedRef.current) {
            setState(prev => ({ ...prev, error: null }))
          }
        }, 5000)
      }

      throw error
    }
  }, [options])

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      success: false
    })
  }, [])

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  const clearSuccess = useCallback(() => {
    setState(prev => ({ ...prev, success: false }))
  }, [])

  return {
    ...state,
    execute,
    reset,
    clearError,
    clearSuccess
  }
}

// Hook for handling multiple async operations
export function useAsyncOperations() {
  const [operations, setOperations] = useState<Record<string, AsyncOperationState<any>>>({})

  const execute = useCallback(async <T>(
    key: string,
    asyncFunction: () => Promise<T>,
    options: UseAsyncOperationOptions = {}
  ) => {
    setOperations(prev => ({
      ...prev,
      [key]: {
        data: null,
        loading: true,
        error: null,
        success: false
      }
    }))

    try {
      const result = await asyncFunction()
      
      setOperations(prev => ({
        ...prev,
        [key]: {
          data: result,
          loading: false,
          error: null,
          success: true
        }
      }))

      if (options.onSuccess) {
        options.onSuccess(result)
      }

      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred'
      
      setOperations(prev => ({
        ...prev,
        [key]: {
          data: null,
          loading: false,
          error: errorMessage,
          success: false
        }
      }))

      if (options.onError) {
        options.onError(errorMessage)
      }

      throw error
    }
  }, [])

  const getOperation = useCallback((key: string) => {
    return operations[key] || {
      data: null,
      loading: false,
      error: null,
      success: false
    }
  }, [operations])

  const resetOperation = useCallback((key: string) => {
    setOperations(prev => {
      const newOperations = { ...prev }
      delete newOperations[key]
      return newOperations
    })
  }, [])

  const resetAll = useCallback(() => {
    setOperations({})
  }, [])

  return {
    operations,
    execute,
    getOperation,
    resetOperation,
    resetAll
  }
}

// Hook for API calls with automatic error handling
export function useApiCall<T = any>() {
  const [state, setState] = useState<AsyncOperationState<T>>({
    data: null,
    loading: false,
    error: null,
    success: false
  })

  const call = useCallback(async (
    url: string,
    options: RequestInit = {},
    transform?: (data: any) => T
  ) => {
    setState(prev => ({
      ...prev,
      loading: true,
      error: null,
      success: false
    }))

    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      const transformedData = transform ? transform(data) : data

      setState({
        data: transformedData,
        loading: false,
        error: null,
        success: true
      })

      return transformedData
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Network error occurred'
      
      setState({
        data: null,
        loading: false,
        error: errorMessage,
        success: false
      })

      throw error
    }
  }, [])

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      success: false
    })
  }, [])

  return {
    ...state,
    call,
    reset
  }
}
