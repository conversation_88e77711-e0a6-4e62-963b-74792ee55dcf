import { Shield, Truck, Award, Headphones, CreditCard, RefreshCw } from "lucide-react"

const badges = [
  {
    icon: Shield,
    title: "<PERSON><PERSON><PERSON><PERSON> Alışveri<PERSON>",
    description: "SSL sertifikası ile korumalı ödeme",
  },
  {
    icon: Truck,
    title: "Hızlı Teslimat",
    description: "24 saat içinde kargo",
  },
  {
    icon: Award,
    title: "Kalite Garantisi",
    description: "Sertifikalı orijinal ürünler",
  },
  {
    icon: Headphones,
    title: "7/24 Destek",
    description: "Uzman müşteri hizmetleri",
  },
  {
    icon: CreditCard,
    title: "Kolay Ödeme",
    description: "Taksit ve havale seçenekleri",
  },
  {
    icon: RefreshCw,
    title: "Kolay İade",
    description: "14 gün içinde ücretsiz iade",
  },
]

export function TrustBadges() {
  return (
    <section className="py-16 bg-slate-900 text-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Neden Bizi Tercih Etmelisiniz?</h2>
          <p className="text-lg text-slate-300 max-w-2xl mx-auto">
            25 yıllık deneyimimiz ve müşteri memnuniyeti odaklı hizmet anlayışımızla yanınızdayız
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-8">
          {badges.map((badge, index) => {
            const IconComponent = badge.icon
            return (
              <div key={index} className="text-center group">
                <div className="bg-orange-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                  <IconComponent className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-lg font-semibold mb-2">{badge.title}</h3>
                <p className="text-slate-300 text-sm leading-relaxed">{badge.description}</p>
              </div>
            )
          })}
        </div>

        {/* Additional trust badges or testimonials can be added here */}

        <div className="mt-16 text-center">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div>
              <div className="text-3xl font-bold text-orange-400 mb-2">50,000+</div>
              <div className="text-slate-300">Mutlu Müşteri</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-orange-400 mb-2">10,000+</div>
              <div className="text-slate-300">Ürün Çeşidi</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-orange-400 mb-2">25</div>
              <div className="text-slate-300">Yıl Deneyim</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-orange-400 mb-2">99.8%</div>
              <div className="text-slate-300">Müşteri Memnuniyeti</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
