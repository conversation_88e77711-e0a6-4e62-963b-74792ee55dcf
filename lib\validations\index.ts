import { z } from 'zod'

// Common validation schemas
export const idSchema = z.string().min(1, 'ID gereklidir')

export const slugSchema = z
  .string()
  .min(1, 'Slug gereklidir')
  .regex(/^[a-z0-9-]+$/, 'Slug sadece küçük harf, rakam ve tire içerebilir')

export const emailSchema = z
  .string()
  .min(1, 'Email gereklidir')
  .email('Geçerli bir email adresi giriniz')

export const passwordSchema = z
  .string()
  .min(6, 'Şifre en az 6 karakter olmalıdır')
  .max(100, 'Şifre en fazla 100 karakter olabilir')

export const nameSchema = z
  .string()
  .min(1, 'İsim gereklidir')
  .max(100, 'İsim en fazla 100 karakter olabilir')
  .trim()

export const descriptionSchema = z
  .string()
  .min(1, 'Açıklama gereklidir')
  .max(2000, 'Açıklama en fazla 2000 karakter olabilir')
  .trim()

export const priceSchema = z
  .number()
  .min(0, 'Fiyat 0 veya daha büyük olmalıdır')
  .max(999999, 'Fiyat çok yüksek')

export const stockSchema = z
  .number()
  .int('Stok sayısı tam sayı olmalıdır')
  .min(0, 'Stok sayısı 0 veya daha büyük olmalıdır')

// User validation schemas
export const userRoleSchema = z.enum(['ADMIN', 'SUPER_ADMIN'], {
  errorMap: () => ({ message: 'Geçersiz kullanıcı rolü' })
})

export const createUserSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  name: nameSchema.optional(),
  role: userRoleSchema.default('ADMIN'),
  isActive: z.boolean().default(true)
})

export const updateUserSchema = z.object({
  email: emailSchema.optional(),
  password: passwordSchema.optional(),
  name: nameSchema.optional(),
  role: userRoleSchema.optional(),
  isActive: z.boolean().optional()
})

export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Şifre gereklidir')
})

// Category validation schemas
export const createCategorySchema = z.object({
  name: nameSchema,
  slug: slugSchema.optional(),
  description: descriptionSchema,
  icon: z.string().optional(),
  parentId: idSchema.optional(),
  isActive: z.boolean().default(true),
  sortOrder: z.number().int().min(0).default(0),
  seoTitle: z.string().max(60).optional(),
  seoDescription: z.string().max(160).optional()
})

export const updateCategorySchema = createCategorySchema.partial()

export const categoryQuerySchema = z.object({
  search: z.string().optional(),
  isActive: z.enum(['true', 'false']).optional(),
  parentId: z.string().optional(),
  sortBy: z.enum(['name', 'createdAt', 'updatedAt', 'sortOrder']).default('sortOrder'),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10)
})

// Product validation schemas
export const stockStatusSchema = z.enum(['IN_STOCK', 'OUT_OF_STOCK', 'LOW_STOCK'], {
  errorMap: () => ({ message: 'Geçersiz stok durumu' })
})

export const productImageSchema = z.object({
  id: z.string().optional(),
  url: z.string().url('Geçerli bir URL giriniz'),
  alt: z.string().min(1, 'Alt text gereklidir'),
  title: z.string().optional(),
  sortOrder: z.number().int().min(0).default(0),
  isMain: z.boolean().default(false),
  size: z.number().int().min(0).default(0),
  width: z.number().int().min(0).default(0),
  height: z.number().int().min(0).default(0),
  format: z.string().default('jpg')
})

export const productVideoSchema = z.object({
  id: z.string().optional(),
  url: z.string().url('Geçerli bir URL giriniz'),
  title: z.string().min(1, 'Video başlığı gereklidir'),
  description: z.string().optional(),
  thumbnail: z.string().url().optional(),
  duration: z.number().int().min(0).optional(),
  sortOrder: z.number().int().min(0).default(0)
})

export const productSpecificationSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Özellik adı gereklidir'),
  value: z.string().min(1, 'Özellik değeri gereklidir'),
  unit: z.string().optional(),
  sortOrder: z.number().int().min(0).default(0)
})

export const productCertificateSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, 'Sertifika adı gereklidir'),
  issuer: z.string().min(1, 'Sertifika veren kurum gereklidir'),
  number: z.string().optional(),
  validUntil: z.string().datetime().optional(),
  documentUrl: z.string().url().optional()
})

export const productDiscountSchema = z.object({
  name: z.string().min(1, 'İndirim adı gereklidir').max(100),
  code: z.string().max(50).optional(),
  description: z.string().max(500).optional(),
  type: z.enum(['PERCENTAGE', 'FIXED_AMOUNT', 'BUY_X_GET_Y', 'FREE_SHIPPING', 'BULK_DISCOUNT', 'TIERED_DISCOUNT']),
  value: z.number().min(0, 'İndirim değeri 0\'dan büyük olmalıdır'),
  maxDiscount: z.number().min(0).optional(),
  minOrderAmount: z.number().min(0).optional(),
  buyQuantity: z.number().int().min(1).optional(),
  getQuantity: z.number().int().min(1).optional(),
  getDiscountPercent: z.number().min(0).max(100).optional(),
  startDate: z.string().datetime(),
  endDate: z.string().datetime(),
  usageLimit: z.number().int().min(1).optional(),
  userLimit: z.number().int().min(1).optional(),
  conditions: z.any().optional(),
  customerGroups: z.array(z.string()).optional(),
  regions: z.array(z.string()).optional(),
  isActive: z.boolean().default(true),
  priority: z.number().int().min(0).default(0),
  stackable: z.boolean().default(false)
})

export const createProductSchema = z.object({
  name: nameSchema,
  slug: slugSchema.optional(),
  description: descriptionSchema,
  shortDescription: z.string().max(500).optional(),
  categoryId: idSchema,
  brand: z.string().min(1, 'Marka gereklidir').max(100),
  model: z.string().max(100).optional(),
  // Simplified pricing fields
  basePrice: priceSchema,
  baseCostPrice: priceSchema.optional(),
  zakatAmount: priceSchema.optional(),
  taxRate: z.number().min(0).max(100).default(0),
  currency: z.string().length(3).default('TRY'),
  // Stock management fields
  trackStock: z.boolean().default(true),
  stockQuantity: stockSchema.optional(),
  minStockThreshold: stockSchema.optional(),
  maxStockCapacity: stockSchema.optional(),
  stockLocation: z.string().max(200).optional(),
  reorderPoint: stockSchema.optional(),
  reorderQuantity: stockSchema.optional(),
  // Status fields
  isActive: z.boolean().default(true),
  isFeatured: z.boolean().default(false),
  isNew: z.boolean().default(false),
  isOnSale: z.boolean().default(false),
  // SEO fields - Comprehensive SEO optimization
  seoTitle: z.string().max(60).optional(),
  seoDescription: z.string().max(160).optional(),
  metaKeywords: z.array(z.string()).optional(),
  canonicalUrl: z.string().url().optional(),
  robotsDirective: z.enum(['index,follow', 'noindex,nofollow', 'index,nofollow', 'noindex,follow']).optional(),
  focusKeyword: z.string().max(100).optional(),
  seoScore: z.number().min(0).max(100).optional(),

  // Open Graph fields
  ogTitle: z.string().max(60).optional(),
  ogDescription: z.string().max(160).optional(),
  ogImage: z.string().url().optional(),
  ogType: z.enum(['website', 'product', 'article']).default('product').optional(),

  // Twitter Card fields
  twitterCard: z.enum(['summary', 'summary_large_image', 'app', 'player']).default('summary_large_image').optional(),
  twitterTitle: z.string().max(60).optional(),
  twitterDescription: z.string().max(160).optional(),
  twitterImage: z.string().url().optional(),

  // Structured Data
  structuredData: z.record(z.any()).optional(),

  // Additional SEO fields
  alternativeText: z.string().max(200).optional(),
  breadcrumbs: z.array(z.string()).optional(),
  schemaMarkup: z.string().optional(),
  hreflangTags: z.record(z.string()).optional(),

  // Content SEO
  readabilityScore: z.number().min(0).max(100).optional(),
  keywordDensity: z.number().min(0).max(100).optional(),
  internalLinks: z.array(z.string().url()).optional(),
  externalLinks: z.array(z.string().url()).optional(),
  // Physical properties
  weight: z.number().min(0).optional(),
  dimensions: z.object({
    length: z.number().min(0),
    width: z.number().min(0),
    height: z.number().min(0)
  }).optional(),
  // Related data
  images: z.array(productImageSchema).default([]),
  videos: z.array(productVideoSchema).default([]),
  specifications: z.array(productSpecificationSchema).default([]),
  certificates: z.array(productCertificateSchema).default([]),
  discounts: z.array(productDiscountSchema).default([])
})

export const updateProductSchema = createProductSchema.partial()

export const productQuerySchema = z.object({
  search: z.string().optional(),
  categoryId: z.string().optional(),
  category: z.string().optional(),
  brand: z.string().optional(),
  minPrice: z.coerce.number().min(0).default(0),
  maxPrice: z.coerce.number().min(0).default(999999),
  isActive: z.enum(['true', 'false']).optional(),
  isFeatured: z.enum(['true', 'false']).optional(),
  isNew: z.enum(['true', 'false']).optional(),
  isOnSale: z.enum(['true', 'false']).optional(),
  sortBy: z.enum(['name', 'price', 'createdAt', 'updatedAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10)
})

// File upload validation schemas
export const fileUploadSchema = z.object({
  files: z.array(z.instanceof(File)).min(1, 'En az bir dosya seçiniz').max(10, 'En fazla 10 dosya yükleyebilirsiniz')
})

export const imageUploadSchema = z.object({
  files: z.array(z.instanceof(File))
    .min(1, 'En az bir görsel seçiniz')
    .max(10, 'En fazla 10 görsel yükleyebilirsiniz')
    .refine(
      (files) => files.every(file => file.type.startsWith('image/')),
      'Sadece görsel dosyaları yükleyebilirsiniz'
    )
    .refine(
      (files) => files.every(file => file.size <= 5 * 1024 * 1024),
      'Dosya boyutu 5MB\'dan küçük olmalıdır'
    )
})

// API response schemas
export const apiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  data: z.any().optional(),
  error: z.string().optional(),
  errors: z.array(z.string()).optional()
})

export const paginationSchema = z.object({
  page: z.number().int().min(1),
  limit: z.number().int().min(1),
  total: z.number().int().min(0),
  totalPages: z.number().int().min(0),
  hasNext: z.boolean(),
  hasPrev: z.boolean()
})

export const paginatedResponseSchema = apiResponseSchema.extend({
  pagination: paginationSchema.optional()
})

// Export types
export type CreateUserInput = z.infer<typeof createUserSchema>
export type UpdateUserInput = z.infer<typeof updateUserSchema>
export type LoginInput = z.infer<typeof loginSchema>
export type CreateCategoryInput = z.infer<typeof createCategorySchema>
export type UpdateCategoryInput = z.infer<typeof updateCategorySchema>
export type CategoryQueryInput = z.infer<typeof categoryQuerySchema>
export type CreateProductInput = z.infer<typeof createProductSchema>
export type UpdateProductInput = z.infer<typeof updateProductSchema>
export type ProductQueryInput = z.infer<typeof productQuerySchema>
export type FileUploadInput = z.infer<typeof fileUploadSchema>
export type ImageUploadInput = z.infer<typeof imageUploadSchema>
export type ApiResponse = z.infer<typeof apiResponseSchema>
export type PaginatedResponse = z.infer<typeof paginatedResponseSchema>
