"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, AlertCircle, RefreshCw } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import ProductCard from "./products/product-card"
import { PopularProductsSkeleton } from "./skeletons/popular-products-skeleton"
import type { Product } from "@/types"

export function PopularProducts() {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [retryCount, setRetryCount] = useState(0)

  const maxRetries = 3

  const getFallbackProducts = (): Product[] => {
    return [
      {
        id: "popular-1",
        name: "3M Peltor X5A Kulaklık",
        slug: "3m-peltor-x5a-kulaklik",
        description: "Yüksek performanslı gürültü önleyici kulaklık. SNR 37 dB gürültü azaltma.",
        price: 289.9,
        originalPrice: 349.9,
        images: [
          {
            id: "img-1",
            url: "/placeholder.svg?height=300&width=300",
            alt: "3M Peltor X5A Kulaklık",
            isMain: true,
          },
        ],
        category: {
          id: "cat-1",
          name: "Kulak Koruma",
          slug: "kulak-koruma",
        },
        brand: "3M",
        sku: "3M-X5A-001",
        stockStatus: "in_stock",
        stockQuantity: 25,
        isNew: false,
        isFeatured: true,
        isOnSale: true,
        isActive: true,
        weight: 0.5,
        rating: 4.8,
        reviewCount: 156,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: "popular-2",
        name: "Uvex Pheos CX2 Güvenlik Gözlüğü",
        slug: "uvex-pheos-cx2-guvenlik-gozlugu",
        description: "Anti-fog özellikli güvenlik gözlüğü. UV koruması ve çizilmez lens.",
        price: 45.5,
        originalPrice: null,
        images: [
          {
            id: "img-2",
            url: "/placeholder.svg?height=300&width=300",
            alt: "Uvex Pheos CX2 Güvenlik Gözlüğü",
            isMain: true,
          },
        ],
        category: {
          id: "cat-2",
          name: "Göz Koruma",
          slug: "goz-koruma",
        },
        brand: "Uvex",
        sku: "UVEX-CX2-001",
        stockStatus: "in_stock",
        stockQuantity: 100,
        isNew: true,
        isFeatured: true,
        isOnSale: false,
        isActive: true,
        weight: 0.1,
        rating: 4.6,
        reviewCount: 89,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: "popular-3",
        name: "Ansell HyFlex 11-801 Eldiven",
        slug: "ansell-hyflex-11-801-eldiven",
        description: "Yüksek hassasiyet gerektiren işler için eldiven. Nitrile kaplama.",
        price: 12.75,
        originalPrice: 15.9,
        images: [
          {
            id: "img-3",
            url: "/placeholder.svg?height=300&width=300",
            alt: "Ansell HyFlex 11-801 Eldiven",
            isMain: true,
          },
        ],
        category: {
          id: "cat-3",
          name: "El Koruma",
          slug: "el-koruma",
        },
        brand: "Ansell",
        sku: "ANSELL-HF-801",
        stockStatus: "in_stock",
        stockQuantity: 200,
        isNew: false,
        isFeatured: true,
        isOnSale: true,
        isActive: true,
        weight: 0.05,
        rating: 4.7,
        reviewCount: 234,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: "popular-4",
        name: "Puma Safety Velocity Ayakkabı",
        slug: "puma-safety-velocity-ayakkabi",
        description: "S1P güvenlik ayakkabısı. Çelik burunlu ve anti-slip taban.",
        price: 425.0,
        originalPrice: 499.0,
        images: [
          {
            id: "img-4",
            url: "/placeholder.svg?height=300&width=300",
            alt: "Puma Safety Velocity Ayakkabı",
            isMain: true,
          },
        ],
        category: {
          id: "cat-4",
          name: "Ayak Koruma",
          slug: "ayak-koruma",
        },
        brand: "Puma",
        sku: "PUMA-VEL-001",
        stockStatus: "in_stock",
        stockQuantity: 30,
        isNew: false,
        isFeatured: true,
        isOnSale: true,
        isActive: true,
        weight: 1.2,
        rating: 4.5,
        reviewCount: 178,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: "popular-5",
        name: "MSA V-Gard Baret",
        slug: "msa-v-gard-baret",
        description: "Klasik güvenlik bareti. Darbe emici ve ayarlanabilir.",
        price: 89.9,
        originalPrice: null,
        images: [
          {
            id: "img-5",
            url: "/placeholder.svg?height=300&width=300",
            alt: "MSA V-Gard Baret",
            isMain: true,
          },
        ],
        category: {
          id: "cat-5",
          name: "Baş Koruma",
          slug: "bas-koruma",
        },
        brand: "MSA",
        sku: "MSA-VG-001",
        stockStatus: "low_stock",
        stockQuantity: 5,
        isNew: false,
        isFeatured: true,
        isOnSale: false,
        isActive: true,
        weight: 0.4,
        rating: 4.4,
        reviewCount: 92,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: "popular-6",
        name: "Tyvek Classic Xpert Tulum",
        slug: "tyvek-classic-xpert-tulum",
        description: "Kimyasal koruyucu tulum. Tek kullanımlık ve nefes alabilir.",
        price: 34.9,
        originalPrice: 42.5,
        images: [
          {
            id: "img-6",
            url: "/placeholder.svg?height=300&width=300",
            alt: "Tyvek Classic Xpert Tulum",
            isMain: true,
          },
        ],
        category: {
          id: "cat-6",
          name: "Vücut Koruma",
          slug: "vucut-koruma",
        },
        brand: "Tyvek",
        sku: "TYVEK-CX-001",
        stockStatus: "in_stock",
        stockQuantity: 75,
        isNew: false,
        isFeatured: true,
        isOnSale: true,
        isActive: true,
        weight: 0.3,
        rating: 4.3,
        reviewCount: 67,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: "popular-7",
        name: "Honeywell Miller Emniyet Kemeri",
        slug: "honeywell-miller-emniyet-kemeri",
        description: "Yüksekte çalışma emniyet kemeri. CE sertifikalı.",
        price: 189.9,
        originalPrice: 229.9,
        images: [
          {
            id: "img-7",
            url: "/placeholder.svg?height=300&width=300",
            alt: "Honeywell Miller Emniyet Kemeri",
            isMain: true,
          },
        ],
        category: {
          id: "cat-7",
          name: "Yüksekte Çalışma",
          slug: "yuksekte-calisma",
        },
        brand: "Honeywell",
        sku: "HON-MIL-001",
        stockStatus: "in_stock",
        stockQuantity: 40,
        isNew: false,
        isFeatured: true,
        isOnSale: true,
        isActive: true,
        weight: 1.8,
        rating: 4.9,
        reviewCount: 145,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: "popular-8",
        name: "3M 8210 N95 Maske",
        slug: "3m-8210-n95-maske",
        description: "N95 solunum koruyucu maske. 20'li paket.",
        price: 45.0,
        originalPrice: null,
        images: [
          {
            id: "img-8",
            url: "/placeholder.svg?height=300&width=300",
            alt: "3M 8210 N95 Maske",
            isMain: true,
          },
        ],
        category: {
          id: "cat-8",
          name: "Solunum Koruma",
          slug: "solunum-koruma",
        },
        brand: "3M",
        sku: "3M-8210-001",
        stockStatus: "in_stock",
        stockQuantity: 150,
        isNew: false,
        isFeatured: true,
        isOnSale: false,
        isActive: true,
        weight: 0.2,
        rating: 4.6,
        reviewCount: 203,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ]
  }

  const loadProducts = async () => {
    try {
      setLoading(true)
      setError(null)

      console.log("Loading popular products...")

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // For now, use fallback data
      const fallbackProducts = getFallbackProducts()
      setProducts(fallbackProducts)
      setRetryCount(0)

      console.log("Popular products loaded:", fallbackProducts.length)
    } catch (err) {
      console.error("Error loading popular products:", err)
      setError(err instanceof Error ? err.message : "Ürünler yüklenirken hata oluştu")

      // Use fallback data on error
      if (retryCount < maxRetries) {
        setProducts(getFallbackProducts())
      }
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadProducts()
  }, [retryCount])

  const handleRetry = () => {
    if (retryCount < maxRetries) {
      setRetryCount((prev) => prev + 1)
    }
  }

  const itemsPerView = {
    mobile: 1,
    tablet: 2,
    desktop: 4,
  }

  const maxIndex = Math.max(0, products.length - itemsPerView.desktop)

  const nextSlide = () => {
    setCurrentIndex((prev) => (prev >= maxIndex ? 0 : prev + 1))
  }

  const prevSlide = () => {
    setCurrentIndex((prev) => (prev <= 0 ? maxIndex : prev - 1))
  }

  if (loading) {
    return <PopularProductsSkeleton />
  }

  if (error && products.length === 0) {
    return (
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">Popüler Ürünler</h2>
            <p className="text-gray-600">En çok tercih edilen iş güvenliği ürünleri</p>
          </div>

          <Alert className="max-w-md mx-auto">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>Ürünler yüklenirken hata oluştu</span>
              {retryCount < maxRetries && (
                <Button variant="outline" size="sm" onClick={handleRetry} className="ml-2 bg-transparent">
                  <RefreshCw className="h-4 w-4 mr-1" />
                  Tekrar Dene
                </Button>
              )}
            </AlertDescription>
          </Alert>
        </div>
      </section>
    )
  }

  return (
    <section className="py-12 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">Popüler Ürünler</h2>
            <p className="text-gray-600">En çok tercih edilen iş güvenliği ürünleri</p>
          </div>

          <Link href="/urunler">
            <Button variant="outline" className="hidden sm:flex bg-transparent">
              Tümünü Gör
            </Button>
          </Link>
        </div>

        {error && (
          <Alert className="mb-6 max-w-2xl mx-auto">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>Bazı ürünler yüklenirken sorun yaşandı, mevcut ürünler gösteriliyor.</AlertDescription>
          </Alert>
        )}

        {products.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-600 mb-4">Şu anda popüler ürünler gösterilemiyor.</p>
            <Button onClick={handleRetry} variant="outline">
              Tekrar Dene
            </Button>
          </div>
        ) : (
          <>
            {/* Desktop Carousel */}
            <div className="hidden lg:block relative overflow-hidden">
              <div className="flex items-center gap-2 mb-4">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={prevSlide}
                  disabled={currentIndex === 0}
                  className="h-10 w-10 bg-transparent"
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={nextSlide}
                  disabled={currentIndex >= maxIndex}
                  className="h-10 w-10"
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>

              <div
                className="flex transition-transform duration-300 ease-in-out"
                style={{
                  transform: `translateX(-${currentIndex * (100 / itemsPerView.desktop)}%)`,
                }}
              >
                {products.map((product) => (
                  <div key={product.id} className="w-1/4 flex-shrink-0 px-2">
                    <ProductCard product={product} showAddToCart={true} />
                  </div>
                ))}
              </div>
            </div>

            {/* Tablet Grid */}
            <div className="hidden md:grid lg:hidden grid-cols-3 gap-4">
              {products.slice(0, 6).map((product) => (
                <ProductCard key={product.id} product={product} showAddToCart={true} />
              ))}
            </div>

            {/* Mobile Grid */}
            <div className="grid md:hidden grid-cols-1 sm:grid-cols-2 gap-4">
              {products.slice(0, 4).map((product) => (
                <ProductCard key={product.id} product={product} showAddToCart={true} />
              ))}
            </div>

            {/* Indicators */}
            {products.length > itemsPerView.desktop && (
              <div className="flex justify-center mt-6 gap-2">
                {Array.from({ length: Math.ceil(products.length / itemsPerView.desktop) }).map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentIndex(index)}
                    className={`w-2 h-2 rounded-full transition-colors ${
                      Math.floor(currentIndex / itemsPerView.desktop) === index ? "bg-blue-600" : "bg-gray-300"
                    }`}
                  />
                ))}
              </div>
            )}

            {/* Mobile "Tümünü Gör" Button */}
            <div className="flex sm:hidden justify-center mt-6">
              <Link href="/urunler">
                <Button variant="outline">Tümünü Gör</Button>
              </Link>
            </div>
          </>
        )}
      </div>
    </section>
  )
}
