'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, Package, TrendingUp, TrendingDown, RotateCcw } from 'lucide-react'
import { z } from 'zod'

// Types
interface Product {
  id: string
  name: string
  stockQuantity: number
  minStockThreshold: number
  maxStockCapacity: number
  stockStatus: string
}

interface StockMovementModalProps {
  isOpen: boolean
  onClose: () => void
  product: Product | null
  onSuccess?: () => void
}

// Validation schema
const StockMovementSchema = z.object({
  movementType: z.enum(['in', 'out', 'adjustment']),
  quantity: z.number().min(1, 'Miktar en az 1 olmalıdır'),
  reason: z.enum(['purchase', 'sale', 'damage', 'adjustment', 'return', 'transfer']).optional(),
  reference: z.string().optional(),
  unitCost: z.number().positive().optional(),
  supplier: z.string().optional(),
  notes: z.string().optional()
})

type StockMovementFormData = z.infer<typeof StockMovementSchema>

const StockMovementModal: React.FC<StockMovementModalProps> = ({
  isOpen,
  onClose,
  product,
  onSuccess
}) => {
  const [formData, setFormData] = useState<StockMovementFormData>({
    movementType: 'in',
    quantity: 1,
    reason: undefined,
    reference: '',
    unitCost: undefined,
    supplier: '',
    notes: ''
  })

  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setFormData({
        movementType: 'in',
        quantity: 1,
        reason: undefined,
        reference: '',
        unitCost: undefined,
        supplier: '',
        notes: ''
      })
      setError(null)
      setValidationErrors({})
    }
  }, [isOpen])

  // Calculate new stock based on movement
  const calculateNewStock = (): number => {
    if (!product) return 0
    
    const currentStock = product.stockQuantity || 0
    
    if (formData.movementType === 'in') {
      return currentStock + formData.quantity
    } else if (formData.movementType === 'out') {
      return Math.max(0, currentStock - formData.quantity)
    } else if (formData.movementType === 'adjustment') {
      return formData.quantity
    }
    
    return currentStock
  }

  // Get movement type icon and color
  const getMovementTypeInfo = (type: string) => {
    switch (type) {
      case 'in':
        return { icon: TrendingUp, color: 'text-green-600', bgColor: 'bg-green-50', label: 'Stok Girişi' }
      case 'out':
        return { icon: TrendingDown, color: 'text-red-600', bgColor: 'bg-red-50', label: 'Stok Çıkışı' }
      case 'adjustment':
        return { icon: RotateCcw, color: 'text-blue-600', bgColor: 'bg-blue-50', label: 'Stok Düzeltmesi' }
      default:
        return { icon: Package, color: 'text-gray-600', bgColor: 'bg-gray-50', label: 'Stok Hareketi' }
    }
  }

  // Get reason options based on movement type
  const getReasonOptions = (movementType: string) => {
    switch (movementType) {
      case 'in':
        return [
          { value: 'purchase', label: 'Satın Alma' },
          { value: 'return', label: 'İade' },
          { value: 'transfer', label: 'Transfer' }
        ]
      case 'out':
        return [
          { value: 'sale', label: 'Satış' },
          { value: 'damage', label: 'Hasar/Fire' },
          { value: 'transfer', label: 'Transfer' }
        ]
      case 'adjustment':
        return [
          { value: 'adjustment', label: 'Sayım Düzeltmesi' }
        ]
      default:
        return []
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!product) return

    setIsLoading(true)
    setError(null)
    setValidationErrors({})

    try {
      // Validate form data
      const validatedData = StockMovementSchema.parse(formData)

      // Additional validations
      if (formData.movementType === 'out' && formData.quantity > (product.stockQuantity || 0)) {
        throw new Error(`Yetersiz stok. Mevcut: ${product.stockQuantity}, İstenen: ${formData.quantity}`)
      }

      if (formData.movementType === 'in' && product.maxStockCapacity) {
        const newStock = calculateNewStock()
        if (newStock > product.maxStockCapacity) {
          throw new Error(`Maksimum kapasite aşılacak. Kapasite: ${product.maxStockCapacity}, Yeni Stok: ${newStock}`)
        }
      }

      // Submit to API
      const response = await fetch('/api/stock-movements', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          productId: product.id,
          ...validatedData,
          createdBy: 'admin' // TODO: Get from auth context
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.details || result.error || 'Stok hareketi oluşturulamadı')
      }

      // Success
      onSuccess?.()
      onClose()

    } catch (error: any) {
      console.error('Stock movement error:', error)
      
      if (error instanceof z.ZodError) {
        const errors: Record<string, string> = {}
        error.errors.forEach((err) => {
          if (err.path[0]) {
            errors[err.path[0] as string] = err.message
          }
        })
        setValidationErrors(errors)
      } else {
        setError(error.message || 'Bir hata oluştu')
      }
    } finally {
      setIsLoading(false)
    }
  }

  const handleInputChange = (field: keyof StockMovementFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  if (!product) return null

  const movementInfo = getMovementTypeInfo(formData.movementType)
  const MovementIcon = movementInfo.icon
  const newStock = calculateNewStock()
  const reasonOptions = getReasonOptions(formData.movementType)

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className={`p-2 rounded-lg ${movementInfo.bgColor}`}>
              <MovementIcon className={`h-5 w-5 ${movementInfo.color}`} />
            </div>
            {movementInfo.label}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Product Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium text-gray-900 mb-2">{product.name}</h3>
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Mevcut Stok:</span>
                <span className="ml-2 font-medium">{product.stockQuantity || 0}</span>
              </div>
              <div>
                <span className="text-gray-600">Min. Eşik:</span>
                <span className="ml-2 font-medium">{product.minStockThreshold || 0}</span>
              </div>
              <div>
                <span className="text-gray-600">Maks. Kapasite:</span>
                <span className="ml-2 font-medium">{product.maxStockCapacity || '-'}</span>
              </div>
            </div>
          </div>

          {/* Movement Type */}
          <div className="space-y-2">
            <Label htmlFor="movementType">Hareket Tipi *</Label>
            <Select
              value={formData.movementType}
              onValueChange={(value) => handleInputChange('movementType', value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="in">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-green-600" />
                    Stok Girişi
                  </div>
                </SelectItem>
                <SelectItem value="out">
                  <div className="flex items-center gap-2">
                    <TrendingDown className="h-4 w-4 text-red-600" />
                    Stok Çıkışı
                  </div>
                </SelectItem>
                <SelectItem value="adjustment">
                  <div className="flex items-center gap-2">
                    <RotateCcw className="h-4 w-4 text-blue-600" />
                    Stok Düzeltmesi
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            {validationErrors.movementType && (
              <p className="text-sm text-red-600">{validationErrors.movementType}</p>
            )}
          </div>

          {/* Quantity */}
          <div className="space-y-2">
            <Label htmlFor="quantity">
              {formData.movementType === 'adjustment' ? 'Yeni Stok Miktarı *' : 'Miktar *'}
            </Label>
            <Input
              id="quantity"
              type="number"
              min="1"
              value={formData.quantity}
              onChange={(e) => handleInputChange('quantity', parseInt(e.target.value) || 1)}
              className={validationErrors.quantity ? 'border-red-500' : ''}
            />
            {validationErrors.quantity && (
              <p className="text-sm text-red-600">{validationErrors.quantity}</p>
            )}
            
            {/* Stock Preview */}
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="flex justify-between items-center text-sm">
                <span className="text-gray-600">Mevcut Stok:</span>
                <span className="font-medium">{product.stockQuantity || 0}</span>
              </div>
              <div className="flex justify-between items-center text-sm mt-1">
                <span className="text-gray-600">Yeni Stok:</span>
                <span className={`font-medium ${newStock <= 0 ? 'text-red-600' : newStock <= (product.minStockThreshold || 0) ? 'text-orange-600' : 'text-green-600'}`}>
                  {newStock}
                </span>
              </div>
            </div>
          </div>

          {/* Reason */}
          {reasonOptions.length > 0 && (
            <div className="space-y-2">
              <Label htmlFor="reason">Sebep</Label>
              <Select
                value={formData.reason || ''}
                onValueChange={(value) => handleInputChange('reason', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sebep seçin..." />
                </SelectTrigger>
                <SelectContent>
                  {reasonOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Reference */}
          <div className="space-y-2">
            <Label htmlFor="reference">
              {formData.movementType === 'in' ? 'Fatura/Sipariş No' : 
               formData.movementType === 'out' ? 'Sipariş/Sevkiyat No' : 'Referans No'}
            </Label>
            <Input
              id="reference"
              value={formData.reference || ''}
              onChange={(e) => handleInputChange('reference', e.target.value)}
              placeholder={formData.movementType === 'in' ? 'Örn: FAT-2024-001' : 
                          formData.movementType === 'out' ? 'Örn: SIP-2024-001' : 'Referans numarası'}
            />
          </div>

          {/* Unit Cost (only for 'in' movements) */}
          {formData.movementType === 'in' && (
            <div className="space-y-2">
              <Label htmlFor="unitCost">Birim Maliyet (TL)</Label>
              <Input
                id="unitCost"
                type="number"
                step="0.01"
                min="0"
                value={formData.unitCost || ''}
                onChange={(e) => handleInputChange('unitCost', parseFloat(e.target.value) || undefined)}
                placeholder="0.00"
              />
            </div>
          )}

          {/* Supplier (only for 'in' movements) */}
          {formData.movementType === 'in' && (
            <div className="space-y-2">
              <Label htmlFor="supplier">Tedarikçi</Label>
              <Input
                id="supplier"
                value={formData.supplier || ''}
                onChange={(e) => handleInputChange('supplier', e.target.value)}
                placeholder="Tedarikçi adı"
              />
            </div>
          )}

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notlar</Label>
            <Textarea
              id="notes"
              value={formData.notes || ''}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Ek açıklamalar..."
              rows={3}
            />
          </div>

          {/* Error Alert */}
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              İptal
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className={movementInfo.color.replace('text-', 'bg-').replace('-600', '-600 hover:bg-').replace('600', '700')}
            >
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isLoading ? 'Kaydediliyor...' : 'Kaydet'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

export default StockMovementModal
