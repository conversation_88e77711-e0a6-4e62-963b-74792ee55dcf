const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkCurrentDatabase() {
  try {
    console.log('🔍 Checking current database state...')
    
    // Check categories
    const categories = await prisma.category.findMany({
      select: {
        id: true,
        name: true,
        productCount: true,
        isActive: true
      },
      orderBy: { sortOrder: 'asc' }
    })
    
    console.log(`\n📂 Categories (${categories.length}):`)
    categories.forEach(cat => {
      console.log(`  - ${cat.name}: ${cat.productCount} products (${cat.isActive ? 'Active' : 'Inactive'})`)
    })
    
    // Check products
    const productCount = await prisma.product.count()
    console.log(`\n📦 Total products: ${productCount}`)
    
    if (productCount > 0) {
      // Check stock fields
      try {
        const sampleProducts = await prisma.product.findMany({
          select: {
            id: true,
            name: true,
            stockQuantity: true,
            minStockThreshold: true,
            maxStockCapacity: true,
            stockStatus: true,
            brand: true
          },
          take: 5
        })
        
        console.log('\n📊 Sample products with stock data:')
        sampleProducts.forEach((product, index) => {
          console.log(`  ${index + 1}. ${product.name}`)
          console.log(`     Brand: ${product.brand || 'N/A'}`)
          console.log(`     Stock: ${product.stockQuantity || 'N/A'} units`)
          console.log(`     Min Threshold: ${product.minStockThreshold || 'N/A'}`)
          console.log(`     Status: ${product.stockStatus || 'N/A'}`)
          console.log('')
        })
        
        // Check for missing stock data
        const missingStockData = await prisma.product.count({
          where: {
            OR: [
              { stockQuantity: null },
              { minStockThreshold: null },
              { stockStatus: null }
            ]
          }
        })
        
        if (missingStockData > 0) {
          console.log(`⚠️  ${missingStockData} products have missing stock data`)
        } else {
          console.log('✅ All products have complete stock data')
        }
        
      } catch (error) {
        console.log('❌ Stock fields not accessible:', error.message)
        console.log('💡 Database migration may be needed')
      }
    }
    
    // Check if we need to apply migration
    console.log('\n🔧 Migration Status:')
    try {
      await prisma.$queryRaw`SELECT stockQuantity FROM "Product" LIMIT 1`
      console.log('✅ Stock management fields exist in database')
    } catch (error) {
      console.log('❌ Stock management fields missing - migration needed')
      console.log('💡 Run: npx prisma db push')
    }
    
  } catch (error) {
    console.error('❌ Database check failed:', error.message)
  } finally {
    await prisma.$disconnect()
  }
}

checkCurrentDatabase()
