import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { createSuccessResponse, createErrorResponse } from "@/lib/api-utils"

// GET /api/category-banners/category/[categoryId] - Kategoriye ait aktif banner'ları getir
export async function GET(request: Request, { params }: { params: { categoryId: string } }) {
  try {
    const { categoryId } = params
    const { searchParams } = new URL(request.url)
    const deviceType = searchParams.get('deviceType') || 'DESKTOP'
    const includeInactive = searchParams.get('includeInactive') === 'true'

    // Kategori var mı kontrol et
    const category = await prisma.category.findUnique({
      where: { id: categoryId }
    })

    if (!category) {
      return NextResponse.json(
        createErrorResponse("Kategori bulunamadı"),
        { status: 404 }
      )
    }

    const now = new Date()
    const where: any = {
      categoryId,
      deviceType: {
        has: deviceType
      }
    }

    // Aktif banner'lar için ek filtreler
    if (!includeInactive) {
      where.isActive = true
      where.OR = [
        { startDate: null, endDate: null },
        { startDate: { lte: now }, endDate: null },
        { startDate: null, endDate: { gte: now } },
        { startDate: { lte: now }, endDate: { gte: now } }
      ]
    }

    const banners = await prisma.categoryBanner.findMany({
      where,
      orderBy: [
        { priority: 'desc' },
        { displayOrder: 'asc' },
        { createdAt: 'desc' }
      ],
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        }
      }
    })

    // Analytics: Impression count artır
    if (banners.length > 0 && !includeInactive) {
      await prisma.categoryBanner.updateMany({
        where: {
          id: { in: banners.map(b => b.id) }
        },
        data: {
          impressionCount: { increment: 1 }
        }
      })

      // CTR hesapla ve güncelle
      for (const banner of banners) {
        if (banner.impressionCount > 0) {
          const newCtr = (banner.clickCount / (banner.impressionCount + 1)) * 100
          await prisma.categoryBanner.update({
            where: { id: banner.id },
            data: { ctr: newCtr }
          })
        }
      }
    }

    return NextResponse.json(createSuccessResponse({
      data: banners,
      meta: {
        categoryId,
        deviceType,
        totalBanners: banners.length,
        timestamp: now.toISOString()
      }
    }))
  } catch (error) {
    console.error("CategoryBanners by category GET error:", error)
    return NextResponse.json(
      createErrorResponse("Banner'lar yüklenirken hata oluştu"),
      { status: 500 }
    )
  }
}

// POST /api/category-banners/category/[categoryId]/click - Banner tıklama kaydı
export async function POST(request: Request, { params }: { params: { categoryId: string } }) {
  try {
    const { categoryId } = params
    const body = await request.json()
    const { bannerId, clickData } = body

    if (!bannerId) {
      return NextResponse.json(
        createErrorResponse("Banner ID gerekli"),
        { status: 400 }
      )
    }

    // Banner var mı ve bu kategoriye ait mi kontrol et
    const banner = await prisma.categoryBanner.findFirst({
      where: {
        id: bannerId,
        categoryId
      }
    })

    if (!banner) {
      return NextResponse.json(
        createErrorResponse("Banner bulunamadı"),
        { status: 404 }
      )
    }

    // Click count artır
    const updatedBanner = await prisma.categoryBanner.update({
      where: { id: bannerId },
      data: {
        clickCount: { increment: 1 }
      }
    })

    // CTR yeniden hesapla
    const newCtr = updatedBanner.impressionCount > 0 
      ? ((updatedBanner.clickCount + 1) / updatedBanner.impressionCount) * 100 
      : 0

    await prisma.categoryBanner.update({
      where: { id: bannerId },
      data: { ctr: newCtr }
    })

    // TODO: Detaylı analytics için ayrı bir tablo oluşturulabilir
    // ClickAnalytics tablosu: bannerId, timestamp, userAgent, ip, referrer, etc.

    return NextResponse.json(createSuccessResponse({
      message: "Tıklama kaydedildi",
      data: {
        bannerId,
        newClickCount: updatedBanner.clickCount + 1,
        newCtr: newCtr
      }
    }))
  } catch (error) {
    console.error("CategoryBanner click tracking error:", error)
    return NextResponse.json(
      createErrorResponse("Tıklama kaydedilirken hata oluştu"),
      { status: 500 }
    )
  }
}
