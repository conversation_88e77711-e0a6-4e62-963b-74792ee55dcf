// Test component to debug pricing calculation
import React from 'react'
import { calculateCurrentPrice, type ProductDiscount } from '@/types'

export function TestPricingDebug() {
  // Test case 1: No discounts
  const basePrice1 = 200
  const discounts1: ProductDiscount[] = []
  const result1 = calculateCurrentPrice(basePrice1, discounts1)
  
  console.log('Test 1 - No discounts:', {
    basePrice: basePrice1,
    discounts: discounts1,
    result: result1
  })

  // Test case 2: Active percentage discount
  const basePrice2 = 200
  const discounts2: ProductDiscount[] = [
    {
      id: 'test-1',
      productId: 'test-product',
      name: 'Test Discount',
      type: 'PERCENTAGE',
      value: 25,
      isActive: true,
      startDate: new Date('2024-01-01').toISOString(),
      endDate: new Date('2024-12-31').toISOString(),
      usageLimit: null,
      usageCount: 0,
      clickCount: 0,
      conversionCount: 0,
      totalSavings: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ]
  const result2 = calculateCurrentPrice(basePrice2, discounts2)
  
  console.log('Test 2 - 25% discount:', {
    basePrice: basePrice2,
    discounts: discounts2,
    result: result2
  })

  // Test case 3: Expired discount
  const basePrice3 = 200
  const discounts3: ProductDiscount[] = [
    {
      id: 'test-2',
      productId: 'test-product',
      name: 'Expired Discount',
      type: 'PERCENTAGE',
      value: 50,
      isActive: true,
      startDate: new Date('2023-01-01').toISOString(),
      endDate: new Date('2023-12-31').toISOString(),
      usageLimit: null,
      usageCount: 0,
      clickCount: 0,
      conversionCount: 0,
      totalSavings: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ]
  const result3 = calculateCurrentPrice(basePrice3, discounts3)
  
  console.log('Test 3 - Expired discount:', {
    basePrice: basePrice3,
    discounts: discounts3,
    result: result3
  })

  return (
    <div className="p-4 bg-gray-100 rounded-lg">
      <h3 className="font-bold mb-4">Pricing Calculation Test Results</h3>
      
      <div className="space-y-4">
        <div className="bg-white p-3 rounded">
          <h4 className="font-semibold">Test 1: No Discounts</h4>
          <p>Base Price: {basePrice1}₺</p>
          <p>Current Price: {result1.currentPrice}₺</p>
          <p>Discount: {result1.discountAmount}₺ ({result1.discountPercentage}%)</p>
        </div>

        <div className="bg-white p-3 rounded">
          <h4 className="font-semibold">Test 2: 25% Active Discount</h4>
          <p>Base Price: {basePrice2}₺</p>
          <p>Current Price: {result2.currentPrice}₺</p>
          <p>Discount: {result2.discountAmount}₺ ({result2.discountPercentage}%)</p>
        </div>

        <div className="bg-white p-3 rounded">
          <h4 className="font-semibold">Test 3: Expired Discount</h4>
          <p>Base Price: {basePrice3}₺</p>
          <p>Current Price: {result3.currentPrice}₺</p>
          <p>Discount: {result3.discountAmount}₺ ({result3.discountPercentage}%)</p>
        </div>
      </div>
    </div>
  )
}
