"use client"

import React, { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import {
  Plus,
  Search,
  Filter,
  Grid,
  List,
  Edit,
  Trash2,
  MoreHorizontal,
  RefreshCw,
  Download,
  Upload,
  Setting<PERSON>,
  Eye,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent } from "@/components/ui/tabs"
import { useProducts, useDeleteProduct } from "@/lib/hooks/use-products"
import { useMainCategories } from "@/lib/hooks/use-categories"
import { formatDate, debounce, formatCurrency, getStockStatusText, getStockStatusColor } from "@/lib/utils"
import { ProductFormModal } from "@/components/admin/product-form-modal"
import { ProductDetailModal } from "@/components/admin/product-detail-modal"
import { DeleteConfirmModal } from "@/components/admin/delete-confirm-modal"
import { ProductBulkActions } from "@/components/admin/product-bulk-actions"
import { ProductStats } from "@/components/admin/product-stats"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { ErrorMessage } from "@/components/ui/error-message"
import { SuccessMessage } from "@/components/ui/success-message"
import type { Product } from "@/types"
import Image from "next/image"

export default function ProductsPage() {
  // Get URL search params
  const searchParams = useSearchParams()

  // View and filter states
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [searchQuery, setSearchQuery] = useState("")
  const [debouncedSearch, setDebouncedSearch] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all") // 'all', 'active', 'inactive'
  const [stockStatusFilter, setStockStatusFilter] = useState<string>("all") // 'all', 'in_stock', 'out_of_stock', 'low_stock'
  const [categoryFilter, setCategoryFilter] = useState<string>("all") // 'all' or categoryId
  const [brandFilter, setBrandFilter] = useState<string>("all") // 'all' or brand name
  const [sortBy, setSortBy] = useState<string>("createdAt")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc")
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(12)

  // Modal states
  const [showAddModal, setShowAddModal] = useState(false)
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)
  const [viewingProduct, setViewingProduct] = useState<Product | null>(null)
  const [deletingProduct, setDeletingProduct] = useState<Product | null>(null)
  const [forceRenderKey, setForceRenderKey] = useState(0)

  // Selection states
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  const [showBulkActions, setShowBulkActions] = useState(false)

  // Feedback states
  const [successMessage, setSuccessMessage] = useState<string>("")
  const [errorMessage, setErrorMessage] = useState<string>("")

  // Debounced search
  const debouncedSearchHandler = debounce((query: string) => {
    setDebouncedSearch(query)
    setCurrentPage(1)
  }, 300)

  useEffect(() => {
    debouncedSearchHandler(searchQuery)
  }, [searchQuery])

  // Check URL params for category filter on page load
  useEffect(() => {
    const kategoriParam = searchParams.get('kategori')
    if (kategoriParam && kategoriParam !== categoryFilter) {
      console.log('🔗 Setting category filter from URL:', kategoriParam)
      setCategoryFilter(kategoriParam)
    }
  }, [searchParams, categoryFilter])

  // Reset dropdown states when modals close
  useEffect(() => {
    if (!showAddModal && !editingProduct && !viewingProduct && !deletingProduct) {
      // Force a small delay to ensure all modal cleanup is complete
      const timer = setTimeout(() => {
        setForceRenderKey(prev => prev + 1)
      }, 50)
      return () => clearTimeout(timer)
    }
  }, [showAddModal, editingProduct, viewingProduct, deletingProduct])

  // API calls
  const {
    data: productsData,
    loading,
    error,
    refetch,
  } = useProducts({
    search: debouncedSearch || undefined,
    isActive: statusFilter === "all" ? undefined : statusFilter === "active",
    stockStatus: stockStatusFilter === "all" ? undefined : (stockStatusFilter as any),
    categoryId: categoryFilter === "all" ? undefined : categoryFilter,
    brand: brandFilter === "all" ? undefined : brandFilter,
    sortBy: sortBy as any,
    sortOrder,
    page: currentPage,
    limit: pageSize,
  })

  const { data: categoriesList } = useMainCategories()
  const { mutate: deleteProduct, loading: deleting } = useDeleteProduct()

  const products = productsData?.data || []
  const pagination = productsData?.pagination
  const categories = categoriesList?.data || []

  // Event handlers
  const handleAddProduct = () => {
    setShowAddModal(true)
  }

  const handleEditProduct = (product: Product) => {
    setEditingProduct(product)
  }

  const handleViewProduct = (product: Product) => {
    setViewingProduct(product)
  }

  const handleDeleteProduct = (product: Product) => {
    setDeletingProduct(product)
  }

  const handleConfirmDelete = async () => {
    if (!deletingProduct) return

    try {
      await deleteProduct(deletingProduct.id)
      setSuccessMessage(`"${deletingProduct.name}" ürünü başarıyla silindi.`)
      setDeletingProduct(null)
      refetch()
    } catch (error) {
      setErrorMessage("Ürün silinirken bir hata oluştu.")
    }
  }

  const handleModalClose = () => {
    setShowAddModal(false)
    setEditingProduct(null)
    setViewingProduct(null)
    setDeletingProduct(null)

    // Force re-render to reset dropdown states
    setForceRenderKey(prev => prev + 1)
    setTimeout(() => {
      refetch()
    }, 100)
  }

  const handleProductSelect = (productId: string, selected: boolean) => {
    if (selected) {
      setSelectedProducts([...selectedProducts, productId])
    } else {
      setSelectedProducts(selectedProducts.filter((id) => id !== productId))
    }
  }

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedProducts(products.map((prod) => prod.id))
    } else {
      setSelectedProducts([])
    }
  }

  const handleBulkAction = () => {
    setShowBulkActions(true)
  }

  const handleRefresh = () => {
    refetch()
    setSuccessMessage("Ürünler yenilendi.")
  }

  const clearMessages = () => {
    setSuccessMessage("")
    setErrorMessage("")
  }

  // Clear messages after 5 seconds
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => setSuccessMessage(""), 5000)
      return () => clearTimeout(timer)
    }
  }, [successMessage])

  useEffect(() => {
    if (errorMessage) {
      const timer = setTimeout(() => setErrorMessage(""), 5000)
      return () => clearTimeout(timer)
    }
  }, [errorMessage])

  // Loading state
  if (loading && !products.length) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" message="Ürünler yükleniyor..." />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Success/Error Messages */}
      {successMessage && <SuccessMessage message={successMessage} onClose={clearMessages} />}
      {errorMessage && <ErrorMessage message={errorMessage} onClose={clearMessages} />}

      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Ürün Yönetimi</h1>
          <p className="text-gray-600 mt-2">E-ticaret ürünlerinizi oluşturun, düzenleyin ve yönetin</p>
        </div>
        <div className="flex items-center gap-3">
          <Button variant="outline" onClick={handleRefresh} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`} />
            Yenile
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Settings className="h-4 w-4 mr-2" />
                İşlemler
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Download className="h-4 w-4 mr-2" />
                Dışa Aktar
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Upload className="h-4 w-4 mr-2" />
                İçe Aktar
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button onClick={handleAddProduct} className="bg-orange-500 hover:bg-orange-600">
            <Plus className="h-4 w-4 mr-2" />
            Yeni Ürün
          </Button>
        </div>
      </div>

      {/* Product Stats */}
      <ProductStats products={products} />

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Search and Quick Filters */}
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Ürün adı, SKU, marka veya açıklama ile ara..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="flex flex-wrap gap-2">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Durum" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tüm Durumlar</SelectItem>
                    <SelectItem value="active">Aktif</SelectItem>
                    <SelectItem value="inactive">Pasif</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={stockStatusFilter} onValueChange={setStockStatusFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Stok Durumu" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tüm Stoklar</SelectItem>
                    <SelectItem value="in_stock">Stokta</SelectItem>
                    <SelectItem value="low_stock">Az Stok</SelectItem>
                    <SelectItem value="out_of_stock">Stokta Yok</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Kategori" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tüm Kategoriler</SelectItem>
                    {categories.map((cat) => (
                      <SelectItem key={cat.id} value={cat.id}>
                        {cat.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Sırala" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">İsim</SelectItem>
                    <SelectItem value="price">Fiyat</SelectItem>
                    <SelectItem value="stock">Stok</SelectItem>
                    <SelectItem value="createdAt">Oluşturma Tarihi</SelectItem>
                    <SelectItem value="updatedAt">Güncelleme Tarihi</SelectItem>
                  </SelectContent>
                </Select>

                <Button variant="outline" onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}>
                  {sortOrder === "asc" ? "A-Z" : "Z-A"}
                </Button>
              </div>
            </div>

            {/* View Mode and Bulk Actions */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                {selectedProducts.length > 0 && (
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">{selectedProducts.length} ürün seçildi</Badge>
                    <Button size="sm" variant="outline" onClick={handleBulkAction}>
                      Toplu İşlem
                    </Button>
                  </div>
                )}
              </div>

              <div className="flex items-center gap-2">
                <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="12">12</SelectItem>
                    <SelectItem value="24">24</SelectItem>
                    <SelectItem value="48">48</SelectItem>
                    <SelectItem value="96">96</SelectItem>
                  </SelectContent>
                </Select>

                <div className="flex border rounded-lg">
                  <Button
                    variant={viewMode === "grid" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("grid")}
                    className="rounded-r-none"
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("list")}
                    className="rounded-l-none"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Products Display */}
      {error ? (
        <ErrorMessage message={`Ürünler yüklenirken hata oluştu: ${error}`} onRetry={refetch} />
      ) : products.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <div className="text-gray-400 mb-4">
              <Filter className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchQuery || statusFilter !== "all" || categoryFilter !== "all" || brandFilter !== "all"
                ? "Arama kriterlerinize uygun ürün bulunamadı"
                : "Henüz ürün eklenmemiş"}
            </h3>
            <p className="text-gray-600 mb-4">
              {searchQuery || statusFilter !== "all" || categoryFilter !== "all" || brandFilter !== "all"
                ? "Farklı filtreler deneyebilir veya yeni ürün ekleyebilirsiniz."
                : "İlk ürünü ekleyerek başlayın."}
            </p>
            <div className="flex items-center justify-center gap-3">
              {(searchQuery || statusFilter !== "all" || categoryFilter !== "all" || brandFilter !== "all") && (
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchQuery("")
                    setStatusFilter("all")
                    setCategoryFilter("all")
                    setBrandFilter("all")
                  }}
                >
                  Filtreleri Temizle
                </Button>
              )}
              <Button onClick={handleAddProduct} className="bg-orange-500 hover:bg-orange-600">
                <Plus className="h-4 w-4 mr-2" />
                {products.length === 0 ? "İlk Ürünü Ekle" : "Yeni Ürün Ekle"}
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as "grid" | "list")}>
          <TabsContent value="grid" className="mt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {products.map((product) => (
                <ProductGridItem
                  key={`${product.id}-${forceRenderKey}`}
                  product={product}
                  selected={selectedProducts.includes(product.id)}
                  onSelect={(selected) => handleProductSelect(product.id, selected)}
                  onView={() => handleViewProduct(product)}
                  onEdit={() => handleEditProduct(product)}
                  onDelete={() => handleDeleteProduct(product)}
                />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="list" className="mt-0">
            <ProductListView
              key={forceRenderKey}
              products={products}
              selectedProducts={selectedProducts}
              onSelectAll={handleSelectAll}
              onSelect={handleProductSelect}
              onView={handleViewProduct}
              onEdit={handleEditProduct}
              onDelete={handleDeleteProduct}
            />
          </TabsContent>
        </Tabs>
      )}

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <ProductPagination pagination={pagination} currentPage={currentPage} onPageChange={setCurrentPage} />
      )}

      {/* Modals */}
      <ProductFormModal
        open={showAddModal}
        onClose={handleModalClose}
        product={null}
        onSuccess={(message) => setSuccessMessage(message)}
        onError={(message) => setErrorMessage(message)}
        preselectedCategoryId={categoryFilter || undefined}
      />

      <ProductFormModal
        open={!!editingProduct}
        onClose={handleModalClose}
        product={editingProduct}
        onSuccess={(message) => setSuccessMessage(message)}
        onError={(message) => setErrorMessage(message)}
      />

      <ProductDetailModal
        open={!!viewingProduct}
        onClose={() => setViewingProduct(null)}
        product={viewingProduct}
        onEdit={() => {
          setEditingProduct(viewingProduct)
          setViewingProduct(null)
        }}
        onDelete={() => {
          setDeletingProduct(viewingProduct)
          setViewingProduct(null)
        }}
      />

      <DeleteConfirmModal
        open={!!deletingProduct}
        onClose={() => setDeletingProduct(null)}
        title="Ürünü Sil"
        description={`"${deletingProduct?.name}" ürününü silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`}
        onConfirm={handleConfirmDelete}
        loading={deleting}
        destructive
      />

      <ProductBulkActions
        open={showBulkActions}
        onClose={() => setShowBulkActions(false)}
        selectedProducts={selectedProducts}
        onSuccess={(message) => {
          setSuccessMessage(message)
          setSelectedProducts([])
          refetch()
        }}
        onError={(message) => setErrorMessage(message)}
      />
    </div>
  )
}

// Grid Item Component
function ProductGridItem({
  product,
  selected,
  onSelect,
  onView,
  onEdit,
  onDelete,
}: {
  product: Product
  selected: boolean
  onSelect: (selected: boolean) => void
  onView: () => void
  onEdit: () => void
  onDelete: () => void
}) {
  const [dropdownOpen, setDropdownOpen] = React.useState(false)
  return (
    <Card
      className={`hover:shadow-lg transition-all duration-200 cursor-pointer ${
        selected ? "ring-2 ring-orange-500 bg-orange-50" : ""
      }`}
    >
      <CardContent className="p-0">
        <div className="relative">
          <Image
            src={product.images[0]?.url || "/placeholder.svg?height=300&width=300"}
            alt={product.name}
            width={300}
            height={300}
            className="w-full h-48 object-cover rounded-t-lg"
          />
          <input
            type="checkbox"
            checked={selected}
            onChange={(e) => onSelect(e.target.checked)}
            className="absolute top-3 left-3 rounded border-gray-300 text-orange-600 focus:ring-orange-500 z-10"
            onClick={(e) => e.stopPropagation()}
          />
          <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
              <Button variant="ghost" size="icon" className="absolute top-2 right-2 bg-white/80 hover:bg-white">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation()
                  setDropdownOpen(false)
                  onView()
                }}
              >
                <Eye className="h-4 w-4 mr-2" />
                Görüntüle
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation()
                  setDropdownOpen(false)
                  onEdit()
                }}
              >
                <Edit className="h-4 w-4 mr-2" />
                Düzenle
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation()
                  setDropdownOpen(false)
                  onDelete()
                }}
                className="text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Sil
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="p-4 space-y-2" onClick={onView}>
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-gray-900 line-clamp-1">{product.name}</h3>
            <Badge variant="secondary" className="text-xs">
              {product.sku}
            </Badge>
          </div>
          <p className="text-sm text-gray-600 line-clamp-2">{product.shortDescription}</p>

          <div className="flex items-center justify-between text-sm">
            <span className="font-bold text-lg text-orange-600">{formatCurrency(product.price, product.currency)}</span>
            <Badge className={getStockStatusColor(product.stockStatus)}>
              {getStockStatusText(product.stockStatus)}
            </Badge>
          </div>

          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>Kategori: {product.category?.name || "N/A"}</span>
            <span>Marka: {typeof product.brand === 'string' ? product.brand : 'N/A'}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// List View Component
function ProductListView({
  products,
  selectedProducts,
  onSelectAll,
  onSelect,
  onView,
  onEdit,
  onDelete,
}: {
  products: Product[]
  selectedProducts: string[]
  onSelectAll: (selected: boolean) => void
  onSelect: (productId: string, selected: boolean) => void
  onView: (product: Product) => void
  onEdit: (product: Product) => void
  onDelete: (product: Product) => void
}) {
  const allSelected = products.length > 0 && selectedProducts.length === products.length
  const someSelected = selectedProducts.length > 0 && selectedProducts.length < products.length

  return (
    <Card>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b">
              <tr>
                <th className="text-left p-4 w-12">
                  <input
                    type="checkbox"
                    checked={allSelected}
                    ref={(el) => {
                      if (el) el.indeterminate = someSelected
                    }}
                    onChange={(e) => onSelectAll(e.target.checked)}
                    className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                  />
                </th>
                <th className="text-left p-4 font-medium text-gray-900">Ürün</th>
                <th className="text-left p-4 font-medium text-gray-900">Kategori</th>
                <th className="text-left p-4 font-medium text-gray-900">Marka</th>
                <th className="text-left p-4 font-medium text-gray-900">Fiyat</th>
                <th className="text-left p-4 font-medium text-gray-900">Stok</th>
                <th className="text-left p-4 font-medium text-gray-900">Durum</th>
                <th className="text-left p-4 font-medium text-gray-900">Oluşturma Tarihi</th>
                <th className="text-right p-4 font-medium text-gray-900">İşlemler</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {products.map((product) => (
                <tr
                  key={product.id}
                  className={`hover:bg-gray-50 cursor-pointer ${
                    selectedProducts.includes(product.id) ? "bg-orange-50" : ""
                  }`}
                  onClick={() => onView(product)}
                >
                  <td className="p-4" onClick={(e) => e.stopPropagation()}>
                    <input
                      type="checkbox"
                      checked={selectedProducts.includes(product.id)}
                      onChange={(e) => onSelect(product.id, e.target.checked)}
                      className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                    />
                  </td>
                  <td className="p-4">
                    <div className="flex items-center gap-3">
                      <Image
                        src={product.images[0]?.url || "/placeholder.svg?height=40&width=40"}
                        alt={product.name}
                        width={40}
                        height={40}
                        className="w-10 h-10 object-cover rounded-md"
                      />
                      <div>
                        <div className="font-medium text-gray-900">{product.name}</div>
                        <div className="text-sm text-gray-500">SKU: {product.sku}</div>
                      </div>
                    </div>
                  </td>
                  <td className="p-4">
                    <div className="max-w-[150px] truncate text-gray-600">{product.category?.name || "N/A"}</div>
                  </td>
                  <td className="p-4 text-gray-600">{typeof product.brand === 'string' ? product.brand : 'N/A'}</td>
                  <td className="p-4 text-gray-900 font-medium">{formatCurrency(product.price, product.currency)}</td>
                  <td className="p-4 text-gray-600">{product.stock}</td>
                  <td className="p-4">
                    <Badge variant={product.isActive ? "success" : "secondary"}>
                      {product.isActive ? "Aktif" : "Pasif"}
                    </Badge>
                    <Badge className={`ml-2 ${getStockStatusColor(product.stockStatus)}`}>
                      {getStockStatusText(product.stockStatus)}
                    </Badge>
                  </td>
                  <td className="p-4 text-gray-600">{formatDate(product.createdAt)}</td>
                  <td className="p-4" onClick={(e) => e.stopPropagation()}>
                    <div className="flex items-center justify-end gap-2">
                      <Button variant="ghost" size="icon" onClick={() => onView(product)}>
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => onEdit(product)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onDelete(product)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  )
}

// Pagination Component
function ProductPagination({
  pagination,
  currentPage,
  onPageChange,
}: {
  pagination: any
  currentPage: number
  onPageChange: (page: number) => void
}) {
  const { totalPages, hasNext, hasPrev, total } = pagination

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">Toplam {total} ürün</div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={() => onPageChange(currentPage - 1)} disabled={!hasPrev}>
              Önceki
            </Button>
            <span className="text-sm text-gray-600">
              {currentPage} / {totalPages}
            </span>
            <Button variant="outline" size="sm" onClick={() => onPageChange(currentPage + 1)} disabled={!hasNext}>
              Sonraki
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
