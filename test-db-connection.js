const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testConnection() {
  try {
    console.log('🔍 Testing database connection...')
    
    // Test basic connection
    await prisma.$connect()
    console.log('✅ Database connection successful')
    
    // Check if categories exist
    const categoryCount = await prisma.category.count()
    console.log(`📊 Found ${categoryCount} categories`)
    
    // Check if products exist
    const productCount = await prisma.product.count()
    console.log(`📦 Found ${productCount} products`)
    
    // Check if we have the new stock fields
    const sampleProduct = await prisma.product.findFirst({
      select: {
        id: true,
        name: true,
        stockQuantity: true,
        minStockThreshold: true,
        maxStockCapacity: true,
        stockStatus: true
      }
    })
    
    if (sampleProduct) {
      console.log('✅ New stock fields are available:')
      console.log(`  - stockQuantity: ${sampleProduct.stockQuantity}`)
      console.log(`  - minStockThreshold: ${sampleProduct.minStockThreshold}`)
      console.log(`  - maxStockCapacity: ${sampleProduct.maxStockCapacity}`)
      console.log(`  - stockStatus: ${sampleProduct.stockStatus}`)
    } else {
      console.log('⚠️  No products found to test stock fields')
    }
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message)
    
    if (error.message.includes('column') || error.message.includes('does not exist')) {
      console.log('💡 This suggests the database schema needs to be updated')
      console.log('   Run: npx prisma db push')
    }
  } finally {
    await prisma.$disconnect()
  }
}

testConnection()
