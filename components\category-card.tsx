"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Shield, Eye, Hand, Footprints, HardHat, Shirt, Wrench, AlertTriangle, Package, Zap } from "lucide-react"
import { getCategoryImageUrl } from "@/lib/category-image-utils"
import type { Category } from "@/types"

// Icon mapping for fallback
const iconMap = {
  "kulak-koruma": Shield,
  "goz-koruma": Eye,
  "el-koruma": Hand,
  "ayak-koruma": Footprints,
  "bas-koruma": HardHat,
  "vucut-koruma": Shirt,
  "is-elbiseleri": Shirt,
  aletler: Wrench,
  "guvenlik-ekipmanlari": AlertTriangle,
  "ilk-yardim": Package,
  "elektrik-guvenlik": Zap,
}

interface CategoryCardProps {
  category: Category
}

export function CategoryCard({ category }: CategoryCardProps) {
  const [isMobile, setIsMobile] = useState(false)
  const [imageError, setImageError] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  const IconComponent = iconMap[category.slug as keyof typeof iconMap] || Package

  const handleImageError = () => {
    setImageError(true)
  }

  const renderIcon = () => {
    // Mobil ikon varsa ve mobil ekranda göster
    if (category.mobileIcon && isMobile && !imageError) {
      return (
        <div className="w-20 h-20 md:w-24 md:h-24 rounded-full overflow-hidden border-2 border-orange-200 group-hover:border-orange-300 transition-colors">
          <img
            src={getCategoryImageUrl(category.mobileIcon, 'thumbnail')}
            alt={category.name}
            className="w-full h-full object-cover"
            onError={handleImageError}
          />
        </div>
      )
    }

    // Banner resmi varsa thumbnail olarak göster
    if (category.bannerImage && !imageError) {
      return (
        <div className="w-20 h-20 md:w-24 md:h-24 rounded-full overflow-hidden border-2 border-orange-200 group-hover:border-orange-300 transition-colors">
          <img
            src={getCategoryImageUrl(category.bannerImage, 'thumbnail')}
            alt={category.name}
            className="w-full h-full object-cover"
            onError={handleImageError}
          />
        </div>
      )
    }

    // Fallback: Lucide icon
    return (
      <div className="w-20 h-20 md:w-24 md:h-24 bg-orange-100 rounded-full flex items-center justify-center group-hover:bg-orange-200 transition-colors">
        <IconComponent className="w-10 h-10 md:w-12 md:h-12 text-orange-600" />
      </div>
    )
  }

  return (
    <Link href={`/urunler/kategori/${category.slug}`}>
      <Card className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 cursor-pointer h-full">
        <CardContent className="p-4 md:p-6 text-center h-full flex flex-col justify-between min-h-[180px] md:min-h-[200px]">
          <div className="flex flex-col items-center flex-1">
            <div className="mb-4 flex justify-center">
              {renderIcon()}
            </div>
            <div className="flex-1 flex flex-col justify-center">
              <h3 className="font-semibold text-slate-900 mb-2 group-hover:text-orange-600 transition-colors line-clamp-2 min-h-[2.5rem] md:min-h-[3rem] flex items-center justify-center text-sm md:text-base">
                {category.name}
              </h3>
              <p className="text-xs md:text-sm text-slate-600 mb-3 line-clamp-2 min-h-[2rem] md:min-h-[2.5rem] flex items-center justify-center">
                {category.description}
              </p>
            </div>
          </div>
          <div className="mt-auto">
            <span className="text-xs text-slate-500 font-medium">{category.productCount || 0} ürün</span>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}

// Grid layout component
interface CategoryGridClientProps {
  categories: Category[]
}

export function CategoryGridClient({ categories }: CategoryGridClientProps) {
  // Ensure categories is an array
  const categoryList = Array.isArray(categories) ? categories : []

  return (
    <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 md:gap-6">
      {categoryList.map((category) => (
        <CategoryCard key={category.id} category={category} />
      ))}
    </div>
  )
}
