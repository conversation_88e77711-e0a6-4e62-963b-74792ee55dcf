/**
 * SEO Generation Service
 * Uses LLM to automatically generate comprehensive SEO data for products
 */

import fs from 'fs'
import path from 'path'
import { z } from 'zod'

export interface SEOGenerationInput {
  productName: string
  productDescription: string
  category?: string
  brand?: string
  price?: number
  storeName?: string
}

export interface SEOGenerationOutput {
  seoTitle: string
  seoDescription: string
  metaKeywords: string[]
  focusKeyword: string
  canonicalUrl: string
  robotsDirective: 'index,follow' | 'noindex,nofollow' | 'index,nofollow' | 'noindex,follow'
  ogTitle: string
  ogDescription: string
  ogType: 'product' | 'website' | 'article'
  twitterCard: 'summary' | 'summary_large_image' | 'app' | 'player'
  twitterTitle: string
  twitterDescription: string
  alternativeText: string
  breadcrumbs: string[]
  structuredData: Record<string, any>
  schemaMarkup: string
}

// Validation schema for LLM response
const seoResponseSchema = z.object({
  seoTitle: z.string().min(1).max(60),
  seoDescription: z.string().min(1).max(160),
  metaKeywords: z.array(z.string()).max(10),
  focusKeyword: z.string().min(1),
  canonicalUrl: z.string().min(1),
  robotsDirective: z.enum(['index,follow', 'noindex,nofollow', 'index,nofollow', 'noindex,follow']),
  ogTitle: z.string().min(1).max(60),
  ogDescription: z.string().min(1).max(160),
  ogType: z.enum(['product', 'website', 'article']),
  twitterCard: z.enum(['summary', 'summary_large_image', 'app', 'player']),
  twitterTitle: z.string().min(1).max(60),
  twitterDescription: z.string().min(1).max(160),
  alternativeText: z.string().min(1),
  breadcrumbs: z.array(z.string()),
  structuredData: z.record(z.any()),
  schemaMarkup: z.string().min(1)
})

export type LLMApiCall = (prompt: string) => Promise<string>

export class SEOGenerationService {
  private promptTemplate: string
  private llmApiCall: LLMApiCall
  private enableFallback: boolean

  constructor(config: {
    llmApiCall: LLMApiCall
    enableFallback?: boolean
  }) {
    this.llmApiCall = config.llmApiCall
    this.enableFallback = config.enableFallback ?? true
    this.promptTemplate = this.loadPromptTemplate()
  }

  private loadPromptTemplate(): string {
    try {
      const promptPath = path.join(process.cwd(), 'prompts', 'seo-generation-prompt.md')
      return fs.readFileSync(promptPath, 'utf-8')
    } catch (error) {
      console.error('Failed to load SEO generation prompt template:', error)
      return this.getDefaultPrompt()
    }
  }

  private getDefaultPrompt(): string {
    return `
You are an expert SEO specialist for Turkish e-commerce. Generate comprehensive SEO data for the given product.

Product Name: {PRODUCT_NAME}
Product Description: {PRODUCT_DESCRIPTION}
Category: {CATEGORY}
Brand: {BRAND}
Price: {PRICE}

Return a JSON object with all SEO fields optimized for Turkish market:
- seoTitle (50-60 chars)
- seoDescription (120-160 chars)
- metaKeywords (array, max 10)
- focusKeyword
- canonicalUrl
- robotsDirective
- ogTitle, ogDescription, ogType
- twitterCard, twitterTitle, twitterDescription
- alternativeText
- breadcrumbs (array)
- structuredData (schema.org Product)
- schemaMarkup (JSON-LD as string)

Use Turkish language, optimize for Turkish search engines, and follow SEO best practices.
    `.trim()
  }

  private buildPrompt(input: SEOGenerationInput): string {
    let prompt = this.promptTemplate

    // Replace placeholders with actual values
    const replacements = {
      '{PRODUCT_NAME}': input.productName,
      '{PRODUCT_DESCRIPTION}': input.productDescription,
      '{CATEGORY}': input.category || 'Genel',
      '{BRAND}': input.brand || 'Bilinmiyor',
      '{PRICE}': input.price ? `${input.price.toLocaleString('tr-TR')} TL` : 'Belirtilmemiş'
    }

    Object.entries(replacements).forEach(([placeholder, value]) => {
      prompt = prompt.replace(new RegExp(placeholder, 'g'), value)
    })

    return prompt
  }

  async generateSEO(input: SEOGenerationInput): Promise<SEOGenerationOutput> {
    // Validate input
    this.validateInput(input)

    try {
      console.log('🚀 Starting SEO generation for:', input.productName)

      const prompt = this.buildPrompt(input)
      console.log('📝 Prompt built, length:', prompt.length)

      // Call LLM API using the provided function
      const llmResponse = await this.llmApiCall(prompt)
      console.log('🤖 LLM response received, length:', llmResponse.length)

      // Parse and validate JSON response
      const seoData = this.parseAndValidateSEOResponse(llmResponse)
      console.log('✅ SEO data parsed and validated')

      // Post-process and enhance the data
      const enhancedData = this.enhanceSEOData(seoData, input)
      console.log('🎯 SEO data enhanced and ready')

      return enhancedData

    } catch (error) {
      console.error('❌ SEO generation failed:', error)

      if (this.enableFallback) {
        console.log('🔄 Falling back to local generation')
        return this.generateFallbackSEO(input)
      } else {
        throw new Error(`SEO generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }
  }

  private validateInput(input: SEOGenerationInput): void {
    if (!input.productName?.trim()) {
      throw new Error('Product name is required')
    }
    if (!input.productDescription?.trim()) {
      throw new Error('Product description is required')
    }
    if (input.productName.length > 200) {
      throw new Error('Product name is too long (max 200 characters)')
    }
    if (input.productDescription.length > 2000) {
      throw new Error('Product description is too long (max 2000 characters)')
    }
  }

  private parseAndValidateSEOResponse(content: string): SEOGenerationOutput {
    try {
      // Clean the response - remove markdown code blocks if present
      let cleanContent = content.trim()

      // Remove markdown code blocks
      cleanContent = cleanContent.replace(/```json\s*/, '').replace(/```\s*$/, '')
      cleanContent = cleanContent.replace(/```\s*/, '')

      // Extract JSON from response (find the largest JSON object)
      const jsonMatches = cleanContent.match(/\{[\s\S]*\}/g)
      if (!jsonMatches || jsonMatches.length === 0) {
        throw new Error('No JSON found in LLM response')
      }

      // Use the largest JSON match (most likely to be complete)
      const jsonString = jsonMatches.reduce((a, b) => a.length > b.length ? a : b)

      // Parse JSON
      const rawData = JSON.parse(jsonString)
      console.log('📊 Raw LLM data keys:', Object.keys(rawData))

      // Validate using Zod schema
      const validatedData = seoResponseSchema.parse(rawData)
      console.log('✅ Data validation successful')

      return validatedData

    } catch (error) {
      console.error('❌ Failed to parse SEO response:', error)
      console.error('📄 Raw content:', content.substring(0, 500) + '...')

      if (error instanceof z.ZodError) {
        const fieldErrors = error.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ')
        throw new Error(`Invalid SEO data structure: ${fieldErrors}`)
      }

      throw new Error(`Failed to parse LLM response: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private enhanceSEOData(seoData: SEOGenerationOutput, input: SEOGenerationInput): SEOGenerationOutput {
    // The data is already validated, but we can enhance it further
    const enhanced = { ...seoData }

    // Ensure character limits are respected
    enhanced.seoTitle = this.truncateText(enhanced.seoTitle, 60)
    enhanced.seoDescription = this.truncateText(enhanced.seoDescription, 160)
    enhanced.ogTitle = this.truncateText(enhanced.ogTitle, 60)
    enhanced.ogDescription = this.truncateText(enhanced.ogDescription, 160)
    enhanced.twitterTitle = this.truncateText(enhanced.twitterTitle, 60)
    enhanced.twitterDescription = this.truncateText(enhanced.twitterDescription, 160)

    // Ensure canonical URL starts with /urun/
    if (!enhanced.canonicalUrl.startsWith('/urun/')) {
      enhanced.canonicalUrl = `/urun/${enhanced.canonicalUrl.replace(/^\/+/, '')}`
    }

    // Ensure structured data has required fields
    if (!enhanced.structuredData['@context']) {
      enhanced.structuredData['@context'] = 'https://schema.org/'
    }
    if (!enhanced.structuredData['@type']) {
      enhanced.structuredData['@type'] = 'Product'
    }

    // Update schema markup to match structured data
    enhanced.schemaMarkup = JSON.stringify(enhanced.structuredData)

    // Log enhancement results
    console.log('🎯 Enhanced SEO data:', {
      titleLength: enhanced.seoTitle.length,
      descriptionLength: enhanced.seoDescription.length,
      keywordCount: enhanced.metaKeywords.length,
      breadcrumbCount: enhanced.breadcrumbs.length
    })

    return enhanced
  }

  private generateFallbackSEO(input: SEOGenerationInput): SEOGenerationOutput {
    const title = this.generateDefaultTitle(input)
    const description = this.generateDefaultDescription(input)
    
    return {
      seoTitle: title,
      seoDescription: description,
      metaKeywords: this.generateDefaultKeywords(input),
      focusKeyword: input.productName.toLowerCase(),
      canonicalUrl: this.generateCanonicalUrl(input.productName),
      robotsDirective: 'index,follow',
      ogTitle: title,
      ogDescription: description,
      ogType: 'product',
      twitterCard: 'summary_large_image',
      twitterTitle: title,
      twitterDescription: description,
      alternativeText: `${input.productName} ürün görseli`,
      breadcrumbs: ['Ana Sayfa', input.category || 'Ürünler', input.productName],
      structuredData: this.generateDefaultStructuredData(input),
      schemaMarkup: JSON.stringify(this.generateDefaultStructuredData(input))
    }
  }

  private generateDefaultTitle(input: SEOGenerationInput): string {
    const parts = []
    
    if (input.brand) parts.push(input.brand)
    parts.push(input.productName)
    if (input.price) parts.push(`${input.price} TL`)
    if (input.category) parts.push(input.category)
    
    const title = parts.join(' - ')
    return this.truncateText(title, 60)
  }

  private generateDefaultDescription(input: SEOGenerationInput): string {
    const description = `${input.productName} ile ihtiyaçlarınızı karşılayın. ${input.productDescription.substring(0, 80)}... Hızlı teslimat ve güvenilir hizmet.`
    return this.truncateText(description, 160)
  }

  private generateDefaultKeywords(input: SEOGenerationInput): string[] {
    const keywords = []
    
    keywords.push(input.productName.toLowerCase())
    if (input.brand) keywords.push(input.brand.toLowerCase())
    if (input.category) keywords.push(input.category.toLowerCase())
    
    // Add common Turkish e-commerce keywords
    keywords.push('online alışveriş', 'hızlı teslimat', 'uygun fiyat')
    
    return keywords.slice(0, 10)
  }

  private generateCanonicalUrl(productName: string): string {
    const slug = productName
      .toLowerCase()
      .replace(/[çğıöşüÇĞIİÖŞÜ]/g, (char) => {
        const map: Record<string, string> = {
          'ç': 'c', 'ğ': 'g', 'ı': 'i', 'ö': 'o', 'ş': 's', 'ü': 'u',
          'Ç': 'c', 'Ğ': 'g', 'I': 'i', 'İ': 'i', 'Ö': 'o', 'Ş': 's', 'Ü': 'u'
        }
        return map[char] || char
      })
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim()

    return `/urun/${slug}`
  }

  private generateDefaultStructuredData(input: SEOGenerationInput): Record<string, any> {
    return {
      "@context": "https://schema.org/",
      "@type": "Product",
      "name": input.productName,
      "description": input.productDescription,
      "brand": input.brand ? {
        "@type": "Brand",
        "name": input.brand
      } : undefined,
      "offers": {
        "@type": "Offer",
        "price": input.price?.toString() || "0",
        "priceCurrency": "TRY",
        "availability": "https://schema.org/InStock",
        "seller": {
          "@type": "Organization",
          "name": input.storeName || "Online Mağaza"
        }
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.5",
        "reviewCount": "10"
      }
    }
  }

  private truncateText(text: string, maxLength: number): string {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength - 3) + '...'
  }
}

// Factory function for easy instantiation
export function createSEOGenerationService(config: {
  llmApiCall: LLMApiCall
  enableFallback?: boolean
}) {
  return new SEOGenerationService(config)
}

// Utility function to create a simple LLM API call function
export function createSimpleLLMApiCall(apiCall: (prompt: string) => Promise<string>): LLMApiCall {
  return apiCall
}
