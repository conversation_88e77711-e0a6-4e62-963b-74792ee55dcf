'use client'

import React from 'react'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { useStock, useStockDisplay } from '@/hooks/useStock'
import { StockInfo } from '@/lib/services/UnifiedStockService'
import { 
  Package, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  TrendingUp,
  TrendingDown
} from 'lucide-react'

interface StockDisplayProps {
  productId?: string
  stockInfo?: StockInfo
  variant?: 'minimal' | 'detailed' | 'card'
  showProgress?: boolean
  showAlert?: boolean
  className?: string
}

/**
 * Unified Stock Display Component
 * Consistent stock information display across all pages
 */
export function StockDisplay({
  productId,
  stockInfo: providedStockInfo,
  variant = 'minimal',
  showProgress = false,
  showAlert = false,
  className = ''
}: StockDisplayProps) {
  // Use hook if stockInfo not provided
  const { stockInfo: fetchedStockInfo, isLoading } = useStock({
    productId: providedStockInfo ? undefined : productId
  })
  
  const { getStockStatusText, getStockStatusColor, formatStockQuantity, getStockAlert } = useStockDisplay()

  const stockInfo = providedStockInfo || fetchedStockInfo

  if (isLoading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-4 bg-gray-200 rounded w-20"></div>
      </div>
    )
  }

  if (!stockInfo) {
    return (
      <div className={className}>
        <Badge variant="secondary">Stok bilgisi yok</Badge>
      </div>
    )
  }

  if (!stockInfo.trackStock) {
    return (
      <div className={className}>
        <Badge variant="secondary">Stok takibi yok</Badge>
      </div>
    )
  }

  const statusText = getStockStatusText(stockInfo.stockStatus)
  const statusColor = getStockStatusColor(stockInfo.stockStatus)
  const alert = getStockAlert(stockInfo)

  // Get status icon
  const getStatusIcon = () => {
    switch (stockInfo.stockStatus) {
      case 'IN_STOCK':
        return <CheckCircle className="h-4 w-4" />
      case 'LOW_STOCK':
        return <AlertTriangle className="h-4 w-4" />
      case 'OUT_OF_STOCK':
        return <XCircle className="h-4 w-4" />
      default:
        return <Package className="h-4 w-4" />
    }
  }

  // Minimal variant - just badge
  if (variant === 'minimal') {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Badge className={statusColor}>
          {getStatusIcon()}
          <span className="ml-1">{statusText}</span>
        </Badge>
        {stockInfo.stockQuantity <= 10 && stockInfo.stockStatus !== 'OUT_OF_STOCK' && (
          <span className="text-xs text-orange-600">
            Son {formatStockQuantity(stockInfo.stockQuantity)}
          </span>
        )}
      </div>
    )
  }

  // Card variant - comprehensive display
  if (variant === 'card') {
    return (
      <div className={`p-4 border rounded-lg space-y-3 ${className}`}>
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <span className="font-medium">Stok Durumu</span>
          </div>
          <Badge className={statusColor}>{statusText}</Badge>
        </div>

        {/* Stock Numbers */}
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {formatStockQuantity(stockInfo.stockQuantity, false)}
            </div>
            <div className="text-gray-600">Toplam</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {formatStockQuantity(stockInfo.availableStock, false)}
            </div>
            <div className="text-gray-600">Mevcut</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {formatStockQuantity(stockInfo.minStockThreshold, false)}
            </div>
            <div className="text-gray-600">Min. Eşik</div>
          </div>
        </div>

        {/* Progress Bar */}
        {showProgress && stockInfo.maxStockCapacity > 0 && (
          <div className="space-y-2">
            <div className="flex justify-between text-xs text-gray-500">
              <span>0</span>
              <span>{stockInfo.maxStockCapacity}</span>
            </div>
            <Progress 
              value={stockInfo.stockIndicators.stockPercentage || 0} 
              className="h-2"
            />
            <div className="text-center text-xs text-gray-500">
              {stockInfo.stockIndicators.stockPercentage?.toFixed(1)}% dolu
            </div>
          </div>
        )}

        {/* Alert */}
        {showAlert && alert && (
          <Alert variant={alert.type === 'error' ? 'destructive' : 'default'}>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{alert.message}</AlertDescription>
          </Alert>
        )}
      </div>
    )
  }

  // Detailed variant - inline detailed info
  return (
    <div className={`space-y-2 ${className}`}>
      {/* Status Badge */}
      <div className="flex items-center gap-2">
        <Badge className={statusColor}>
          {getStatusIcon()}
          <span className="ml-1">{statusText}</span>
        </Badge>
        <span className="text-sm text-gray-600">
          {formatStockQuantity(stockInfo.availableStock)} mevcut
        </span>
      </div>

      {/* Stock Details */}
      <div className="flex items-center gap-4 text-sm text-gray-600">
        <div className="flex items-center gap-1">
          <Package className="h-3 w-3" />
          <span>Toplam: {formatStockQuantity(stockInfo.stockQuantity)}</span>
        </div>
        {stockInfo.reservedStock > 0 && (
          <div className="flex items-center gap-1">
            <TrendingDown className="h-3 w-3" />
            <span>Rezerve: {formatStockQuantity(stockInfo.reservedStock)}</span>
          </div>
        )}
        {stockInfo.minStockThreshold > 0 && (
          <div className="flex items-center gap-1">
            <TrendingUp className="h-3 w-3" />
            <span>Min: {formatStockQuantity(stockInfo.minStockThreshold)}</span>
          </div>
        )}
      </div>

      {/* Progress Bar */}
      {showProgress && stockInfo.maxStockCapacity > 0 && (
        <div className="space-y-1">
          <Progress 
            value={stockInfo.stockIndicators.stockPercentage || 0} 
            className="h-1"
          />
          <div className="text-xs text-gray-500">
            {stockInfo.stockIndicators.stockPercentage?.toFixed(1)}% kapasite
          </div>
        </div>
      )}

      {/* Alert */}
      {showAlert && alert && (
        <Alert variant={alert.type === 'error' ? 'destructive' : 'default'} className="py-2">
          <AlertTriangle className="h-3 w-3" />
          <AlertDescription className="text-xs">{alert.message}</AlertDescription>
        </Alert>
      )}
    </div>
  )
}

/**
 * Quick Stock Status Badge - for use in lists
 */
export function StockBadge({ 
  stockInfo, 
  showQuantity = true,
  className = '' 
}: { 
  stockInfo: StockInfo
  showQuantity?: boolean
  className?: string 
}) {
  const { getStockStatusText, getStockStatusColor, formatStockQuantity } = useStockDisplay()

  if (!stockInfo.trackStock) {
    return <Badge variant="secondary" className={className}>Takip yok</Badge>
  }

  const statusText = getStockStatusText(stockInfo.stockStatus)
  const statusColor = getStockStatusColor(stockInfo.stockStatus)

  return (
    <Badge className={`${statusColor} ${className}`}>
      {statusText}
      {showQuantity && stockInfo.stockStatus !== 'OUT_OF_STOCK' && (
        <span className="ml-1">({stockInfo.availableStock})</span>
      )}
    </Badge>
  )
}

export default StockDisplay
