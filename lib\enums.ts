/**
 * Merkezi Enum Yönetim Servisi
 * Tüm enum değerleri ve Türkçe etiketleri bu dosyada yönetilir
 */

// ============================================================================
// STOK DURUMU ENUM - KALDIRILDI
// ============================================================================
// StockStatus enum'ı kaldırıldı - stok yönetimi Product seviyesinde yapılacak

// ============================================================================
// ONAY DURUMU ENUM
// ============================================================================
export enum ApprovalStatus {
  TASLAK = 'TASLAK',
  BEKLEMEDE = 'BEKLEMEDE',
  ONAYLANDI = 'ONAYLANDI',
  REDDEDILDI = 'REDDEDILDI'
}

export const ApprovalStatusLabels: Record<ApprovalStatus, string> = {
  [ApprovalStatus.TASLAK]: 'Taslak',
  [ApprovalStatus.BEKLEMEDE]: 'Beklemede',
  [ApprovalStatus.ONAYLANDI]: 'Onaylandı',
  [ApprovalStatus.REDDEDILDI]: 'Reddedildi'
}

export const ApprovalStatusColors: Record<ApprovalStatus, string> = {
  [ApprovalStatus.TASLAK]: 'gray',
  [ApprovalStatus.BEKLEMEDE]: 'yellow',
  [ApprovalStatus.ONAYLANDI]: 'green',
  [ApprovalStatus.REDDEDILDI]: 'red'
}

// ============================================================================
// SENKRONIZASYON DURUMU ENUM
// ============================================================================
export enum SyncStatus {
  SENKRONIZE = 'SENKRONIZE',
  BEKLEMEDE = 'BEKLEMEDE',
  BASARISIZ = 'BASARISIZ',
  DEVRE_DISI = 'DEVRE_DISI'
}

export const SyncStatusLabels: Record<SyncStatus, string> = {
  [SyncStatus.SENKRONIZE]: 'Senkronize',
  [SyncStatus.BEKLEMEDE]: 'Beklemede',
  [SyncStatus.BASARISIZ]: 'Başarısız',
  [SyncStatus.DEVRE_DISI]: 'Devre Dışı'
}

export const SyncStatusColors: Record<SyncStatus, string> = {
  [SyncStatus.SENKRONIZE]: 'green',
  [SyncStatus.BEKLEMEDE]: 'yellow',
  [SyncStatus.BASARISIZ]: 'red',
  [SyncStatus.DEVRE_DISI]: 'gray'
}

// ============================================================================
// ICON TİPİ ENUM
// ============================================================================
export enum IconType {
  SVG = 'SVG',
  PNG = 'PNG',
  FONT_ICON = 'FONT_ICON',
  EMOJI = 'EMOJI'
}

export const IconTypeLabels: Record<IconType, string> = {
  [IconType.SVG]: 'SVG',
  [IconType.PNG]: 'PNG',
  [IconType.FONT_ICON]: 'Font Icon',
  [IconType.EMOJI]: 'Emoji'
}

// ============================================================================
// MOBİL ŞABLON ENUM
// ============================================================================
export enum MobileTemplate {
  VARSAYILAN = 'VARSAYILAN',
  GRID = 'GRID',
  LISTE = 'LISTE',
  KART = 'KART',
  BANNER = 'BANNER'
}

export const MobileTemplateLabels: Record<MobileTemplate, string> = {
  [MobileTemplate.VARSAYILAN]: 'Varsayılan',
  [MobileTemplate.GRID]: 'Grid',
  [MobileTemplate.LISTE]: 'Liste',
  [MobileTemplate.KART]: 'Kart',
  [MobileTemplate.BANNER]: 'Banner'
}

// ============================================================================
// GEÇİŞ TİPİ ENUM
// ============================================================================
export enum TransitionType {
  FADE = 'FADE',
  SLIDE = 'SLIDE',
  ZOOM = 'ZOOM',
  FLIP = 'FLIP',
  CUBE = 'CUBE',
  COVERFLOW = 'COVERFLOW'
}

export const TransitionTypeLabels: Record<TransitionType, string> = {
  [TransitionType.FADE]: 'Solma',
  [TransitionType.SLIDE]: 'Kaydırma',
  [TransitionType.ZOOM]: 'Yakınlaştırma',
  [TransitionType.FLIP]: 'Çevirme',
  [TransitionType.CUBE]: 'Küp',
  [TransitionType.COVERFLOW]: 'Coverflow'
}

// ============================================================================
// CİHAZ TİPİ ENUM
// ============================================================================
export enum DeviceType {
  DESKTOP = 'DESKTOP',
  MOBILE = 'MOBILE',
  TABLET = 'TABLET'
}

export const DeviceTypeLabels: Record<DeviceType, string> = {
  [DeviceType.DESKTOP]: 'Masaüstü',
  [DeviceType.MOBILE]: 'Mobil',
  [DeviceType.TABLET]: 'Tablet'
}

// ============================================================================
// YARDIMCI FONKSİYONLAR
// ============================================================================

/**
 * Enum değeri için Türkçe etiket döndürür
 */
export function getEnumLabel<T extends Record<string, string>>(
  enumValue: string,
  labelsMap: T
): string {
  return labelsMap[enumValue as keyof T] || enumValue
}

/**
 * Enum değeri için renk döndürür
 */
export function getEnumColor<T extends Record<string, string>>(
  enumValue: string,
  colorsMap: T
): string {
  return colorsMap[enumValue as keyof T] || 'gray'
}

/**
 * Enum için seçenek listesi döndürür (Select component'ler için)
 */
export function getEnumOptions<T extends Record<string, string>>(
  enumObject: T,
  labelsMap: Record<string, string>
): Array<{ value: string; label: string }> {
  return Object.values(enumObject).map(value => ({
    value,
    label: labelsMap[value] || value
  }))
}

/**
 * Tüm enum'ları tek bir objede toplar (debugging için)
 */
export const AllEnums = {
  ApprovalStatus,
  SyncStatus,
  IconType,
  MobileTemplate,
  TransitionType,
  DeviceType
} as const

/**
 * Tüm enum etiketlerini tek bir objede toplar
 */
export const AllEnumLabels = {
  ApprovalStatus: ApprovalStatusLabels,
  SyncStatus: SyncStatusLabels,
  IconType: IconTypeLabels,
  MobileTemplate: MobileTemplateLabels,
  TransitionType: TransitionTypeLabels,
  DeviceType: DeviceTypeLabels
} as const

/**
 * Tüm enum renklerini tek bir objede toplar
 */
export const AllEnumColors = {
  ApprovalStatus: ApprovalStatusColors,
  SyncStatus: SyncStatusColors
} as const


