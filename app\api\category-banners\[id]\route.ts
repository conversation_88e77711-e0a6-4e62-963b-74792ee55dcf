import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { createSuccessResponse, createErrorResponse } from "@/lib/api-utils"

// GET /api/category-banners/[id] - Tek banner getir
export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const { id } = params

    const banner = await prisma.categoryBanner.findUnique({
      where: { id },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        }
      }
    })

    if (!banner) {
      return NextResponse.json(
        createErrorResponse("Banner bulunamadı"),
        { status: 404 }
      )
    }

    return NextResponse.json(createSuccessResponse({ data: banner }))
  } catch (error) {
    console.error("CategoryBanner GET error:", error)
    return NextResponse.json(
      createErrorResponse("Banner yüklenirken hata oluştu"),
      { status: 500 }
    )
  }
}

// PUT /api/category-banners/[id] - Banner güncelle
export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const { id } = params
    const body = await request.json()

    // Banner var mı kontrol et
    const existingBanner = await prisma.categoryBanner.findUnique({
      where: { id }
    })

    if (!existingBanner) {
      return NextResponse.json(
        createErrorResponse("Banner bulunamadı"),
        { status: 404 }
      )
    }

    // Kategori değişiyorsa kontrol et
    if (body.categoryId && body.categoryId !== existingBanner.categoryId) {
      const category = await prisma.category.findUnique({
        where: { id: body.categoryId }
      })

      if (!category) {
        return NextResponse.json(
          createErrorResponse("Kategori bulunamadı"),
          { status: 404 }
        )
      }
    }

    // DisplayOrder değişiyorsa sıralama kontrolü
    if (body.displayOrder !== undefined && body.displayOrder !== existingBanner.displayOrder) {
      const categoryId = body.categoryId || existingBanner.categoryId
      
      // Eski pozisyondaki boşluğu kapat
      await prisma.categoryBanner.updateMany({
        where: {
          categoryId: existingBanner.categoryId,
          displayOrder: { gt: existingBanner.displayOrder }
        },
        data: {
          displayOrder: { decrement: 1 }
        }
      })

      // Yeni pozisyonda yer aç
      await prisma.categoryBanner.updateMany({
        where: {
          categoryId,
          displayOrder: { gte: body.displayOrder },
          id: { not: id }
        },
        data: {
          displayOrder: { increment: 1 }
        }
      })
    }

    const banner = await prisma.categoryBanner.update({
      where: { id },
      data: {
        categoryId: body.categoryId,
        imageUrl: body.imageUrl,
        imageAlt: body.imageAlt,
        mobileImageUrl: body.mobileImageUrl,
        title: body.title,
        subtitle: body.subtitle,
        description: body.description,
        ctaText: body.ctaText,
        ctaUrl: body.ctaUrl,
        displayOrder: body.displayOrder,
        isActive: body.isActive,
        startDate: body.startDate ? new Date(body.startDate) : null,
        endDate: body.endDate ? new Date(body.endDate) : null,
        displayDuration: body.displayDuration,
        transitionType: body.transitionType,
        backgroundColor: body.backgroundColor,
        textColor: body.textColor,
        priority: body.priority,
        targetAudience: body.targetAudience,
        deviceType: body.deviceType,
        geoLocation: body.geoLocation,
        seasonalTags: body.seasonalTags,
        conversionGoal: body.conversionGoal,
        budgetAllocation: body.budgetAllocation,
        updatedAt: new Date()
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        }
      }
    })

    return NextResponse.json(createSuccessResponse({
      data: banner,
      message: "Banner başarıyla güncellendi"
    }))
  } catch (error) {
    console.error("CategoryBanner PUT error:", error)
    return NextResponse.json(
      createErrorResponse("Banner güncellenirken hata oluştu"),
      { status: 500 }
    )
  }
}

// DELETE /api/category-banners/[id] - Banner sil
export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    const { id } = params

    const existingBanner = await prisma.categoryBanner.findUnique({
      where: { id }
    })

    if (!existingBanner) {
      return NextResponse.json(
        createErrorResponse("Banner bulunamadı"),
        { status: 404 }
      )
    }

    // Banner'ı sil
    await prisma.categoryBanner.delete({
      where: { id }
    })

    // Silinen banner'dan sonraki banner'ların sırasını düzelt
    await prisma.categoryBanner.updateMany({
      where: {
        categoryId: existingBanner.categoryId,
        displayOrder: { gt: existingBanner.displayOrder }
      },
      data: {
        displayOrder: { decrement: 1 }
      }
    })

    return NextResponse.json(createSuccessResponse({
      message: "Banner başarıyla silindi"
    }))
  } catch (error) {
    console.error("CategoryBanner DELETE error:", error)
    return NextResponse.json(
      createErrorResponse("Banner silinirken hata oluştu"),
      { status: 500 }
    )
  }
}
