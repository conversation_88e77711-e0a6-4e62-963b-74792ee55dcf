-- Migration: Add missing stock management fields to Product model
-- This migration adds the missing stock fields while maintaining backward compatibility

BEGIN;

-- Add missing stock management fields to Product table
ALTER TABLE "Product" ADD COLUMN IF NOT EXISTS "stockQuantity" INTEGER DEFAULT 0;
ALTER TABLE "Product" ADD COLUMN IF NOT EXISTS "minStockThreshold" INTEGER DEFAULT 0;
ALTER TABLE "Product" ADD COLUMN IF NOT EXISTS "maxStockCapacity" INTEGER DEFAULT 100;

-- Migrate existing stock data from legacy fields if they exist
-- Update stockQuantity from existing 'stock' field (from variants or legacy)
UPDATE "Product" 
SET "stockQuantity" = COALESCE(
  (SELECT SUM("stock") FROM "ProductVariant" WHERE "productId" = "Product"."id"),
  0
)
WHERE "stockQuantity" = 0;

-- Set reasonable defaults for minStockThreshold based on current stock
UPDATE "Product" 
SET "minStockThreshold" = CASE 
  WHEN "stockQuantity" > 50 THEN 10
  WHEN "stockQuantity" > 20 THEN 5
  WHEN "stockQuantity" > 0 THEN 2
  ELSE 0
END
WHERE "minStockThreshold" = 0;

-- Set maxStockCapacity based on current stock levels
UPDATE "Product" 
SET "maxStockCapacity" = CASE 
  WHEN "stockQuantity" > 100 THEN "stockQuantity" * 2
  WHEN "stockQuantity" > 0 THEN GREATEST("stockQuantity" + 50, 100)
  ELSE 100
END
WHERE "maxStockCapacity" = 100;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS "Product_stockQuantity_idx" ON "Product"("stockQuantity");
CREATE INDEX IF NOT EXISTS "Product_minStockThreshold_idx" ON "Product"("minStockThreshold");
CREATE INDEX IF NOT EXISTS "Product_trackStock_idx" ON "Product"("trackStock");

-- Add computed stock status function (PostgreSQL)
CREATE OR REPLACE FUNCTION calculate_stock_status(
  stock_qty INTEGER,
  min_threshold INTEGER,
  reserved_stock INTEGER DEFAULT 0
) RETURNS TEXT AS $$
BEGIN
  DECLARE available_stock INTEGER;
  BEGIN
    available_stock := stock_qty - reserved_stock;
    
    IF available_stock <= 0 THEN
      RETURN 'OUT_OF_STOCK';
    ELSIF available_stock <= min_threshold THEN
      RETURN 'LOW_STOCK';
    ELSE
      RETURN 'IN_STOCK';
    END IF;
  END;
END;
$$ LANGUAGE plpgsql;

-- Update existing products with calculated stock status
UPDATE "Product" 
SET "stockStatus" = calculate_stock_status("stockQuantity", "minStockThreshold", "reservedStock")::ProductStockStatus
WHERE "stockStatus" IS NULL OR "stockStatus" = 'IN_STOCK';

COMMIT;
