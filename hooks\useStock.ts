'use client'

import { useState, useEffect, useCallback } from 'react'
import { UnifiedStockService, StockInfo } from '@/lib/services/UnifiedStockService'

interface UseStockOptions {
  productId?: string
  productIds?: string[]
  autoRefresh?: boolean
  refreshInterval?: number
}

interface UseStockReturn {
  stockInfo: StockInfo | null
  stockInfoMap: Record<string, StockInfo>
  isLoading: boolean
  error: string | null
  refresh: () => Promise<void>
  updateStockSettings: (productId: string, settings: any) => Promise<void>
}

/**
 * Custom hook for unified stock management
 * Provides real-time stock information with automatic refresh
 */
export function useStock(options: UseStockOptions = {}): UseStockReturn {
  const { productId, productIds, autoRefresh = false, refreshInterval = 30000 } = options

  const [stockInfo, setStockInfo] = useState<StockInfo | null>(null)
  const [stockInfoMap, setStockInfoMap] = useState<Record<string, StockInfo>>({})
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch stock information
  const fetchStock = useCallback(async () => {
    if (!productId && !productIds?.length) return

    setIsLoading(true)
    setError(null)

    try {
      if (productId) {
        // Single product
        const response = await fetch(`/api/stock?productId=${productId}`)
        const result = await response.json()

        if (!response.ok) {
          throw new Error(result.error || 'Failed to fetch stock information')
        }

        setStockInfo(result.data)
      } else if (productIds?.length) {
        // Multiple products
        const response = await fetch(`/api/stock?productIds=${productIds.join(',')}`)
        const result = await response.json()

        if (!response.ok) {
          throw new Error(result.error || 'Failed to fetch stock information')
        }

        setStockInfoMap(result.data)
      }
    } catch (err: any) {
      console.error('Stock fetch error:', err)
      setError(err.message)
    } finally {
      setIsLoading(false)
    }
  }, [productId, productIds])

  // Update stock settings
  const updateStockSettings = useCallback(async (productId: string, settings: any) => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/stock', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          productId,
          ...settings
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to update stock settings')
      }

      // Refresh stock information after update
      await fetchStock()
    } catch (err: any) {
      console.error('Stock update error:', err)
      setError(err.message)
      throw err
    } finally {
      setIsLoading(false)
    }
  }, [fetchStock])

  // Initial fetch
  useEffect(() => {
    fetchStock()
  }, [fetchStock])

  // Auto refresh
  useEffect(() => {
    if (!autoRefresh) return

    const interval = setInterval(fetchStock, refreshInterval)
    return () => clearInterval(interval)
  }, [autoRefresh, refreshInterval, fetchStock])

  return {
    stockInfo,
    stockInfoMap,
    isLoading,
    error,
    refresh: fetchStock,
    updateStockSettings
  }
}

/**
 * Hook for getting stock status text and colors
 */
export function useStockDisplay() {
  const getStockStatusText = useCallback((status: string) => {
    return UnifiedStockService.getStockStatusText(status)
  }, [])

  const getStockStatusColor = useCallback((status: string) => {
    return UnifiedStockService.getStockStatusColor(status)
  }, [])

  const formatStockQuantity = useCallback((quantity: number, showUnit: boolean = true) => {
    const unit = showUnit ? ' adet' : ''
    return `${quantity}${unit}`
  }, [])

  const getStockAlert = useCallback((stockInfo: StockInfo) => {
    if (stockInfo.stockIndicators.isOutOfStock) {
      return {
        type: 'error' as const,
        message: 'Stok tükendi!'
      }
    } else if (stockInfo.stockIndicators.isLowStock) {
      return {
        type: 'warning' as const,
        message: 'Stok seviyesi düşük'
      }
    } else if (stockInfo.stockIndicators.isOverCapacity) {
      return {
        type: 'warning' as const,
        message: 'Kapasite aşıldı!'
      }
    }
    return null
  }, [])

  return {
    getStockStatusText,
    getStockStatusColor,
    formatStockQuantity,
    getStockAlert
  }
}

/**
 * Hook for stock validation
 */
export function useStockValidation() {
  const validateStockOperation = useCallback((
    currentStock: number,
    requestedQuantity: number,
    operation: 'add' | 'subtract' | 'set'
  ) => {
    return UnifiedStockService.validateStockOperation(currentStock, requestedQuantity, operation)
  }, [])

  const canAddToCart = useCallback((stockInfo: StockInfo, requestedQuantity: number) => {
    if (!stockInfo.trackStock) return true
    return stockInfo.availableStock >= requestedQuantity
  }, [])

  const getMaxCartQuantity = useCallback((stockInfo: StockInfo) => {
    if (!stockInfo.trackStock) return 999
    return Math.max(0, stockInfo.availableStock)
  }, [])

  return {
    validateStockOperation,
    canAddToCart,
    getMaxCartQuantity
  }
}
