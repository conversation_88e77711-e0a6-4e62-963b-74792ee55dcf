"use client"

import type React from "react"

import { She<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/sheet"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { useCartStore } from "@/lib/stores/cart-store"
import { CartItem } from "./cart-item"
import { formatCurrency } from "@/lib/utils"
import { ShoppingBag, ArrowRight } from "lucide-react"
import Link from "next/link"

interface CartSheetProps {
  children: React.ReactNode
}

export function CartSheet({ children }: CartSheetProps) {
  const { items, isOpen, setIsOpen, getTotalItems, getTotalPrice } = useCartStore()

  // Ensure items is always an array to prevent undefined errors
  const safeItems = items || []
  const totalItems = getTotalItems()
  const totalPrice = getTotalPrice()
  const freeShippingThreshold = 500
  const remainingForFreeShipping = Math.max(0, freeShippingThreshold - totalPrice)

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>{children}</SheetTrigger>
      <SheetContent className="w-full sm:max-w-lg">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <ShoppingBag className="w-5 h-5" />
            Sepetim ({totalItems} ürün)
          </SheetTitle>
        </SheetHeader>

        {safeItems.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center py-12">
            <ShoppingBag className="w-16 h-16 text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Sepetiniz boş</h3>
            <p className="text-gray-500 mb-6">Alışverişe başlamak için ürün ekleyin</p>
            <Button onClick={() => setIsOpen(false)} asChild>
              <Link href="/urunler">Ürünleri İncele</Link>
            </Button>
          </div>
        ) : (
          <div className="flex flex-col h-full">
            {/* Free Shipping Progress */}
            {remainingForFreeShipping > 0 && (
              <div className="bg-blue-50 p-3 rounded-lg mb-4">
                <p className="text-sm text-blue-800">
                  <span className="font-medium">{formatCurrency(remainingForFreeShipping)}</span> daha alışveriş yapın,{" "}
                  <span className="font-medium">ücretsiz kargo</span> kazanın!
                </p>
                <div className="w-full bg-blue-200 rounded-full h-2 mt-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${Math.min(100, (totalPrice / freeShippingThreshold) * 100)}%`,
                    }}
                  />
                </div>
              </div>
            )}

            {remainingForFreeShipping === 0 && (
              <div className="bg-green-50 p-3 rounded-lg mb-4">
                <p className="text-sm text-green-800 font-medium">🎉 Ücretsiz kargo kazandınız!</p>
              </div>
            )}

            {/* Cart Items */}
            <ScrollArea className="flex-1 -mx-6 px-6">
              <div className="space-y-4">
                {safeItems.map((item) => (
                  <CartItem key={item.id} item={item} />
                ))}
              </div>
            </ScrollArea>

            <Separator className="my-4" />

            {/* Cart Summary */}
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span>Ara Toplam</span>
                <span>{formatCurrency(totalPrice)}</span>
              </div>

              <div className="flex justify-between text-sm">
                <span>Kargo</span>
                <span className={remainingForFreeShipping === 0 ? "text-green-600 font-medium" : ""}>
                  {remainingForFreeShipping === 0 ? "Ücretsiz" : formatCurrency(29.99)}
                </span>
              </div>

              <Separator />

              <div className="flex justify-between font-medium text-lg">
                <span>Toplam</span>
                <span>{formatCurrency(totalPrice + (remainingForFreeShipping === 0 ? 0 : 29.99))}</span>
              </div>

              <div className="space-y-2 pt-2">
                <Button className="w-full" size="lg" asChild>
                  <Link href="/sepet" onClick={() => setIsOpen(false)}>
                    Sepeti Görüntüle
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Link>
                </Button>

                <Button variant="outline" className="w-full bg-transparent" size="lg" asChild>
                  <Link href="/odeme" onClick={() => setIsOpen(false)}>
                    Hızlı Ödeme
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        )}
      </SheetContent>
    </Sheet>
  )
}
