"use client"

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>fresh<PERSON><PERSON>, X } from "lucide-react"
import { Button } from "./button"
import { Card, CardContent } from "./card"

interface ErrorMessageProps {
  message: string
  onRetry?: () => void
  onClose?: () => void
}

export function ErrorMessage({ message, onRetry, onClose }: ErrorMessageProps) {
  return (
    <Card className="border-red-200 bg-red-50">
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
          <div className="flex-1">
            <p className="text-red-800 font-medium">Hata</p>
            <p className="text-red-700 text-sm mt-1">{message}</p>
          </div>
          <div className="flex items-center gap-2">
            {onRetry && (
              <Button variant="outline" size="sm" onClick={onRetry}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Tekrar Dene
              </Button>
            )}
            {onClose && (
              <Button variant="ghost" size="icon" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
