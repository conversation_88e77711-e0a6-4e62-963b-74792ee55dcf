const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkStockConsistency() {
  console.log('🔍 Stok tutarlılığı kontrolü başlatılıyor...\n')

  try {
    // Tüm ürünleri al
    const products = await prisma.product.findMany({
      select: {
        id: true,
        name: true,
        slug: true,
        // Stok alanları
        stockQuantity: true,
        minStockThreshold: true,
        maxStockCapacity: true,
        reservedStock: true,
        stockStatus: true,
        trackStock: true,
      },
      take: 20 // İlk 20 ürünü kontrol et
    })

    console.log(`📊 Toplam ${products.length} ürün kontrol ediliyor...\n`)

    let lowStockCount = 0
    let outOfStockCount = 0
    let inStockCount = 0
    const stockIssues = []

    for (const product of products) {
      // Stok durumu kontrolü
      const availableStock = product.stockQuantity - (product.reservedStock || 0)
      const calculatedStatus = availableStock <= 0 ? 'OUT_OF_STOCK' :
                              availableStock <= product.minStockThreshold ? 'LOW_STOCK' : 'IN_STOCK'

      const hasStockIssue = product.stockStatus !== calculatedStatus

      if (hasStockIssue) {
        stockIssues.push({
          id: product.id,
          name: product.name,
          slug: product.slug,
          stockQuantity: product.stockQuantity,
          reservedStock: product.reservedStock || 0,
          availableStock: availableStock,
          minStockThreshold: product.minStockThreshold,
          currentStatus: product.stockStatus,
          calculatedStatus: calculatedStatus,
          trackStock: product.trackStock
        })
      }

      // Stok durumu sayıları
      switch (product.stockStatus) {
        case 'IN_STOCK':
          inStockCount++
          break
        case 'LOW_STOCK':
          lowStockCount++
          break
        case 'OUT_OF_STOCK':
          outOfStockCount++
          break
      }
    }

    console.log('📈 SONUÇLAR:')
    console.log(`✅ Stokta ürünler: ${inStockCount}`)
    console.log(`⚠️  Düşük stoklu ürünler: ${lowStockCount}`)
    console.log(`❌ Stokta olmayan ürünler: ${outOfStockCount}`)
    console.log(`🔍 Stok durumu tutarsız ürünler: ${stockIssues.length}`)
    console.log(`📊 Toplam kontrol edilen: ${products.length}\n`)

    if (stockIssues.length > 0) {
      console.log('🚨 STOK DURUMU TUTARSIZ ÜRÜNLER:')
      console.log('─'.repeat(140))
      console.log('ID'.padEnd(25) + 'ÜRÜN ADI'.padEnd(30) + 'STOK'.padEnd(8) + 'REZERVE'.padEnd(8) + 'MEVCUT'.padEnd(8) + 'MIN'.padEnd(8) + 'MEVCUT DURUM'.padEnd(15) + 'HESAPLANAN DURUM')
      console.log('─'.repeat(140))

      for (const product of stockIssues.slice(0, 10)) { // İlk 10 tutarsız ürünü göster
        console.log(
          product.id.slice(0, 24).padEnd(25) +
          product.name.slice(0, 29).padEnd(30) +
          product.stockQuantity.toString().padEnd(8) +
          product.reservedStock.toString().padEnd(8) +
          product.availableStock.toString().padEnd(8) +
          product.minStockThreshold.toString().padEnd(8) +
          product.currentStatus.padEnd(15) +
          product.calculatedStatus
        )
      }

      if (stockIssues.length > 10) {
        console.log(`... ve ${stockIssues.length - 10} ürün daha`)
      }
    }

    // Özet istatistikler
    console.log('\n📊 DETAYLI İSTATİSTİKLER:')

    const stockStats = await prisma.product.aggregate({
      _count: { id: true },
      _sum: {
        stockQuantity: true,
        reservedStock: true,
        minStockThreshold: true
      },
      _avg: {
        stockQuantity: true,
        minStockThreshold: true
      }
    })

    console.log(`Toplam ürün sayısı: ${stockStats._count.id}`)
    console.log(`Toplam stok miktarı: ${stockStats._sum.stockQuantity || 0}`)
    console.log(`Toplam rezerve stok: ${stockStats._sum.reservedStock || 0}`)
    console.log(`Toplam minimum eşik: ${stockStats._sum.minStockThreshold || 0}`)
    console.log(`Ortalama stok miktarı: ${(stockStats._avg.stockQuantity || 0).toFixed(2)}`)
    console.log(`Ortalama minimum eşik: ${(stockStats._avg.minStockThreshold || 0).toFixed(2)}`)

    // Stok durumu dağılımı
    const statusDistribution = await prisma.product.groupBy({
      by: ['stockStatus'],
      _count: { stockStatus: true }
    })

    console.log('\n📈 STOK DURUMU DAĞILIMI:')
    for (const status of statusDistribution) {
      console.log(`${status.stockStatus}: ${status._count.stockStatus} ürün`)
    }

    return {
      totalProducts: products.length,
      inStockCount,
      lowStockCount,
      outOfStockCount,
      stockIssuesCount: stockIssues.length,
      stockIssues: stockIssues.slice(0, 5) // İlk 5 sorunlu ürün
    }

  } catch (error) {
    console.error('❌ Hata oluştu:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Script'i çalıştır
if (require.main === module) {
  checkStockConsistency()
    .then((result) => {
      console.log('\n✅ Stok durumu kontrolü tamamlandı!')
      if (result.stockIssuesCount > 0) {
        console.log(`\n⚠️  ${result.stockIssuesCount} ürünün stok durumu tutarsız!`)
        console.log('Bu ürünlerin stok durumları yeniden hesaplanmalı.')
      } else {
        console.log('\n🎉 Tüm ürünlerin stok durumları tutarlı!')
      }
    })
    .catch((error) => {
      console.error('❌ Script çalıştırılırken hata oluştu:', error)
      process.exit(1)
    })
}

module.exports = { checkStockConsistency }
