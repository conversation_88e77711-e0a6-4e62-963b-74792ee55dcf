"use client"

import React, { useState } from "react"
import { Plus, Trash2, Edit, Calendar, Percent, TrendingDown, Clock, Users, Tag } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"
import { format } from "date-fns"
import { tr } from "date-fns/locale"
import type { ProductDiscount, DiscountType } from "@/types"

interface ProductDiscountManagerProps {
  discounts: Omit<ProductDiscount, "id" | "productId" | "usageCount" | "clickCount" | "conversionCount" | "totalSavings" | "createdAt" | "updatedAt">[]
  onDiscountsChange: (discounts: Omit<ProductDiscount, "id" | "productId" | "usageCount" | "clickCount" | "conversionCount" | "totalSavings" | "createdAt" | "updatedAt">[]) => void
  productPrice: number
}

const discountTypes = [
  { value: "PERCENTAGE", label: "Yüzde İndirim", icon: Percent },
  { value: "FIXED_AMOUNT", label: "Sabit Tutar", icon: TrendingDown },
  { value: "BUY_X_GET_Y", label: "Al X Ver Y", icon: Users },
  { value: "FREE_SHIPPING", label: "Ücretsiz Kargo", icon: Tag },
]

export function ProductDiscountManager({ discounts, onDiscountsChange, productPrice }: ProductDiscountManagerProps) {
  const [showAddModal, setShowAddModal] = useState(false)
  const [editingDiscount, setEditingDiscount] = useState<number | null>(null)
  const [newDiscount, setNewDiscount] = useState<Omit<ProductDiscount, "id" | "productId" | "usageCount" | "clickCount" | "conversionCount" | "totalSavings" | "createdAt" | "updatedAt">>({
    name: "",
    description: "",
    type: "PERCENTAGE",
    value: 0,
    startDate: new Date(),
    endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 1 week from now
    isActive: true,
    priority: 0,
    stackable: false,
  })

  const handleAddDiscount = () => {
    if (newDiscount.name && newDiscount.value > 0) {
      onDiscountsChange([...discounts, { ...newDiscount }])
      setNewDiscount({
        name: "",
        description: "",
        type: "PERCENTAGE",
        value: 0,
        startDate: new Date(),
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        isActive: true,
        priority: 0,
        stackable: false,
      })
      setShowAddModal(false)
    }
  }

  const handleEditDiscount = (index: number) => {
    setEditingDiscount(index)
    setNewDiscount(discounts[index])
    setShowAddModal(true)
  }

  const handleUpdateDiscount = () => {
    if (editingDiscount !== null && newDiscount.name && newDiscount.value > 0) {
      const updatedDiscounts = [...discounts]
      updatedDiscounts[editingDiscount] = { ...newDiscount }
      onDiscountsChange(updatedDiscounts)
      setEditingDiscount(null)
      setNewDiscount({
        name: "",
        description: "",
        type: "PERCENTAGE",
        value: 0,
        startDate: new Date(),
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        isActive: true,
        priority: 0,
        stackable: false,
      })
      setShowAddModal(false)
    }
  }

  const handleDeleteDiscount = (index: number) => {
    const updatedDiscounts = discounts.filter((_, i) => i !== index)
    onDiscountsChange(updatedDiscounts)
  }

  const calculateDiscountAmount = (discount: typeof newDiscount) => {
    switch (discount.type) {
      case "PERCENTAGE":
        const percentAmount = (productPrice * discount.value) / 100
        return discount.maxDiscount ? Math.min(percentAmount, discount.maxDiscount) : percentAmount
      case "FIXED_AMOUNT":
        return Math.min(discount.value, productPrice)
      default:
        return 0
    }
  }

  const getDiscountStatusColor = (discount: typeof newDiscount) => {
    const now = new Date()
    if (!discount.isActive) return "bg-gray-500"
    if (discount.startDate > now) return "bg-blue-500"
    if (discount.endDate < now) return "bg-red-500"
    return "bg-green-500"
  }

  const getDiscountStatusText = (discount: typeof newDiscount) => {
    const now = new Date()
    if (!discount.isActive) return "Pasif"
    if (discount.startDate > now) return "Beklemede"
    if (discount.endDate < now) return "Süresi Dolmuş"
    return "Aktif"
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">İndirim Yönetimi</h3>
          <p className="text-sm text-gray-500">Bu ürün için özel indirimler oluşturun ve yönetin</p>
        </div>
        <Dialog open={showAddModal} onOpenChange={setShowAddModal}>
          <DialogTrigger asChild>
            <Button onClick={() => {
              setEditingDiscount(null)
              setNewDiscount({
                name: "",
                description: "",
                type: "PERCENTAGE",
                value: 0,
                startDate: new Date(),
                endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
                isActive: true,
                priority: 0,
                stackable: false,
              })
            }}>
              <Plus className="h-4 w-4 mr-2" />
              İndirim Ekle
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingDiscount !== null ? "İndirim Düzenle" : "Yeni İndirim Ekle"}
              </DialogTitle>
            </DialogHeader>
            
            <div className="space-y-4">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="discount-name">İndirim Adı *</Label>
                  <Input
                    id="discount-name"
                    value={newDiscount.name}
                    onChange={(e) => setNewDiscount(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Örn: Yaz İndirimi"
                  />
                </div>
                <div>
                  <Label htmlFor="discount-code">İndirim Kodu</Label>
                  <Input
                    id="discount-code"
                    value={newDiscount.code || ""}
                    onChange={(e) => setNewDiscount(prev => ({ ...prev, code: e.target.value }))}
                    placeholder="Örn: SUMMER2024"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="discount-description">Açıklama</Label>
                <Textarea
                  id="discount-description"
                  value={newDiscount.description || ""}
                  onChange={(e) => setNewDiscount(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="İndirim açıklaması..."
                  rows={2}
                />
              </div>

              {/* Discount Type & Value */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="discount-type">İndirim Türü *</Label>
                  <Select
                    value={newDiscount.type}
                    onValueChange={(value: DiscountType) => setNewDiscount(prev => ({ ...prev, type: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {discountTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          <div className="flex items-center gap-2">
                            <type.icon className="h-4 w-4" />
                            {type.label}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="discount-value">
                    İndirim Değeri * {newDiscount.type === "PERCENTAGE" ? "(%)" : "(₺)"}
                  </Label>
                  <Input
                    id="discount-value"
                    type="number"
                    value={newDiscount.value}
                    onChange={(e) => setNewDiscount(prev => ({ ...prev, value: parseFloat(e.target.value) || 0 }))}
                    min="0"
                    max={newDiscount.type === "PERCENTAGE" ? "100" : undefined}
                    step={newDiscount.type === "PERCENTAGE" ? "1" : "0.01"}
                  />
                </div>
                {newDiscount.type === "PERCENTAGE" && (
                  <div>
                    <Label htmlFor="max-discount">Maksimum İndirim (₺)</Label>
                    <Input
                      id="max-discount"
                      type="number"
                      value={newDiscount.maxDiscount || ""}
                      onChange={(e) => setNewDiscount(prev => ({ ...prev, maxDiscount: parseFloat(e.target.value) || undefined }))}
                      min="0"
                      step="0.01"
                      placeholder="Sınırsız"
                    />
                  </div>
                )}
              </div>

              {/* Preview */}
              {newDiscount.value > 0 && (
                <Card className="bg-blue-50 border-blue-200">
                  <CardContent className="pt-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Önizleme:</span>
                      <div className="text-right">
                        <div className="text-sm text-gray-600">
                          Orijinal Fiyat: ₺{productPrice.toFixed(2)}
                        </div>
                        <div className="text-sm text-green-600 font-medium">
                          İndirim: -₺{calculateDiscountAmount(newDiscount).toFixed(2)}
                        </div>
                        <div className="text-lg font-bold text-blue-600">
                          Yeni Fiyat: ₺{(productPrice - calculateDiscountAmount(newDiscount)).toFixed(2)}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Date Range */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>Başlangıç Tarihi *</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start text-left font-normal">
                        <Calendar className="mr-2 h-4 w-4" />
                        {format(newDiscount.startDate, "dd MMMM yyyy", { locale: tr })}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <CalendarComponent
                        mode="single"
                        selected={newDiscount.startDate}
                        onSelect={(date) => date && setNewDiscount(prev => ({ ...prev, startDate: date }))}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                <div>
                  <Label>Bitiş Tarihi *</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start text-left font-normal">
                        <Calendar className="mr-2 h-4 w-4" />
                        {format(newDiscount.endDate, "dd MMMM yyyy", { locale: tr })}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <CalendarComponent
                        mode="single"
                        selected={newDiscount.endDate}
                        onSelect={(date) => date && setNewDiscount(prev => ({ ...prev, endDate: date }))}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              {/* Advanced Settings */}
              <div className="space-y-4">
                <Separator />
                <h4 className="font-medium">Gelişmiş Ayarlar</h4>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="min-order">Minimum Sipariş Tutarı (₺)</Label>
                    <Input
                      id="min-order"
                      type="number"
                      value={newDiscount.minOrderAmount || ""}
                      onChange={(e) => setNewDiscount(prev => ({ ...prev, minOrderAmount: parseFloat(e.target.value) || undefined }))}
                      min="0"
                      step="0.01"
                      placeholder="Sınır yok"
                    />
                  </div>
                  <div>
                    <Label htmlFor="usage-limit">Kullanım Limiti</Label>
                    <Input
                      id="usage-limit"
                      type="number"
                      value={newDiscount.usageLimit || ""}
                      onChange={(e) => setNewDiscount(prev => ({ ...prev, usageLimit: parseInt(e.target.value) || undefined }))}
                      min="1"
                      placeholder="Sınırsız"
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label>İndirim Aktif</Label>
                    <p className="text-sm text-gray-500">İndirimin şu anda kullanılabilir olup olmadığı</p>
                  </div>
                  <Switch
                    checked={newDiscount.isActive}
                    onCheckedChange={(checked) => setNewDiscount(prev => ({ ...prev, isActive: checked }))}
                  />
                </div>
              </div>

              <div className="flex justify-end gap-2 pt-4">
                <Button variant="outline" onClick={() => setShowAddModal(false)}>
                  İptal
                </Button>
                <Button onClick={editingDiscount !== null ? handleUpdateDiscount : handleAddDiscount}>
                  {editingDiscount !== null ? "Güncelle" : "Ekle"}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Discounts List */}
      {discounts.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-gray-500">
              <TrendingDown className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Henüz indirim eklenmemiş</p>
              <p className="text-sm">Bu ürün için özel indirimler oluşturmak için yukarıdaki butonu kullanın</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {discounts.map((discount, index) => (
            <Card key={index} className="relative">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <CardTitle className="text-base">{discount.name}</CardTitle>
                      <Badge 
                        variant="secondary" 
                        className={`text-white ${getDiscountStatusColor(discount)}`}
                      >
                        {getDiscountStatusText(discount)}
                      </Badge>
                      {discount.code && (
                        <Badge variant="outline">
                          <Tag className="h-3 w-3 mr-1" />
                          {discount.code}
                        </Badge>
                      )}
                    </div>
                    {discount.description && (
                      <CardDescription>{discount.description}</CardDescription>
                    )}
                  </div>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditDiscount(index)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteDiscount(index)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Tür:</span>
                    <p className="font-medium">
                      {discountTypes.find(t => t.value === discount.type)?.label}
                    </p>
                  </div>
                  <div>
                    <span className="text-gray-500">Değer:</span>
                    <p className="font-medium">
                      {discount.type === "PERCENTAGE" ? `%${discount.value}` : `₺${discount.value}`}
                    </p>
                  </div>
                  <div>
                    <span className="text-gray-500">Başlangıç:</span>
                    <p className="font-medium">
                      {format(discount.startDate, "dd.MM.yyyy")}
                    </p>
                  </div>
                  <div>
                    <span className="text-gray-500">Bitiş:</span>
                    <p className="font-medium">
                      {format(discount.endDate, "dd.MM.yyyy")}
                    </p>
                  </div>
                </div>
                
                {/* Discount Preview */}
                <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between text-sm">
                    <span>İndirim Önizlemesi:</span>
                    <div className="text-right">
                      <span className="text-gray-600">₺{productPrice.toFixed(2)}</span>
                      <span className="mx-2 text-green-600">-₺{calculateDiscountAmount(discount).toFixed(2)}</span>
                      <span className="font-bold text-blue-600">
                        = ₺{(productPrice - calculateDiscountAmount(discount)).toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
