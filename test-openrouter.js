// Simple OpenRouter API Test
async function testOpenRouterAPI() {
  console.log('🚀 Testing OpenRouter SEO Generation API...\n')
  
  try {
    // Test API status
    console.log('1. Testing API Status...')
    const statusResponse = await fetch('http://localhost:3000/api/seo/generate')
    const statusData = await statusResponse.json()
    
    console.log('✅ API Status:', {
      running: statusData.success,
      provider: statusData.status?.llmProvider,
      model: statusData.status?.model,
      configured: statusData.status?.llmConfigured
    })
    
    if (!statusData.status?.llmConfigured) {
      console.log('❌ OpenRouter not configured!')
      return
    }
    
    // Test SEO generation
    console.log('\n2. Testing SEO Generation...')
    const testProduct = {
      productName: "Onvec Smart Tag Akıllı Takip Cihazı 4 adet (Apple uyumlu)",
      productDescription: "Onvec Smart Tag Akıllı Takip Cihazı, anahtarlarınızdan değerli eşyalarınıza ve hatta evcil hayvanlarınıza kadar birçok farklı şeyi takip etmek için kullanabileceğiniz kullanışlı bir cihazdır.",
      category: "Telefonlar & Aksesuarlar",
      brand: "Onvec",
      price: 899
    }
    
    const startTime = Date.now()
    const response = await fetch('http://localhost:3000/api/seo/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testProduct)
    })
    
    const duration = Date.now() - startTime
    const result = await response.json()
    
    console.log(`⏱️  Request completed in ${duration}ms`)
    
    if (result.success) {
      console.log('✅ SEO Generation Successful!')
      console.log('\n📊 Generated Data:')
      console.log(`   SEO Title: "${result.data.seoTitle}"`)
      console.log(`   SEO Description: "${result.data.seoDescription}"`)
      console.log(`   Focus Keyword: "${result.data.focusKeyword}"`)
      console.log(`   Meta Keywords: ${result.data.metaKeywords?.length} items`)
      console.log(`   Canonical URL: "${result.data.canonicalUrl}"`)
      console.log(`   Structured Data: ${result.data.structuredData ? 'Present' : 'Missing'}`)
      
      console.log('\n🎉 OpenRouter integration working perfectly!')
    } else {
      console.log('❌ SEO Generation Failed:', result.error)
      console.log('Message:', result.message)
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

testOpenRouterAPI()
