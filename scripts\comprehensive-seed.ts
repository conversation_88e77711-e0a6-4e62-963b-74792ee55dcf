#!/usr/bin/env tsx

/**
 * Comprehensive Database Seeding Script
 * 
 * This script creates a complete product catalog with:
 * - 8-12 products per category (evenly distributed)
 * - Realistic stock management data using new fields
 * - Complete product information with Turkish content
 * - Proper stock status calculations
 * - Database migration verification
 * 
 * Prerequisites:
 * - Database migration for stock fields must be applied
 * - Categories must exist in database
 * 
 * Usage:
 * npx tsx scripts/comprehensive-seed.ts
 */

import { PrismaClient, ProductStockStatus } from '@prisma/client'

const prisma = new PrismaClient()

// Stock scenarios for realistic distribution
const stockScenarios = [
  { name: "High Stock", stockQuantity: 85, minStockThreshold: 15, maxStockCapacity: 120, status: "IN_STOCK" },
  { name: "Normal Stock", stockQuantity: 45, minStockThreshold: 10, maxStockCapacity: 80, status: "IN_STOCK" },
  { name: "Low Stock", stockQuantity: 8, minStockThreshold: 10, maxStockCapacity: 60, status: "LOW_STOCK" },
  { name: "Critical Stock", stockQuantity: 3, minStockThreshold: 5, maxStockCapacity: 40, status: "LOW_STOCK" },
  { name: "Out of Stock", stockQuantity: 0, minStockThreshold: 5, maxStockCapacity: 50, status: "OUT_OF_STOCK" }
]

// Turkish brands for realistic product names
const turkishBrands = [
  "3M", "MSA", "Uvex", "Honeywell", "DuPont", "Ansell", "Kimberly-Clark", "Moldex",
  "Delta Plus", "JSP", "Portwest", "Cofra", "Sioen", "Helly Hansen", "Carhartt"
]

// Product templates for each category
const productTemplates = {
  "Baş Koruma": [
    {
      name: "Profesyonel Güvenlik Bareti",
      description: "Yüksek darbe dayanımı ve UV koruması olan güvenlik bareti. İnşaat ve endüstriyel kullanım için ideal.",
      shortDescription: "Darbe dayanımlı güvenlik bareti",
      models: ["H-700", "V-Gard", "EVO3", "Pheos", "JSP MK8"],
      priceRange: { min: 45, max: 120 },
      specifications: [
        { name: "Malzeme", value: "ABS Plastik" },
        { name: "Ağırlık", value: "350-400g" },
        { name: "Ayarlama", value: "Ratchet Sistem" },
        { name: "Standart", value: "EN 397" }
      ]
    },
    {
      name: "Elektrikçi Güvenlik Kaskı",
      description: "Elektriksel izolasyon özellikli güvenlik kaskı. 1000V'a kadar koruma sağlar.",
      shortDescription: "Elektriksel izolasyonlu kask",
      models: ["E-Man", "Electrical", "Volt-Guard", "Iso-Safe"],
      priceRange: { min: 85, max: 180 },
      specifications: [
        { name: "İzolasyon", value: "1000V" },
        { name: "Malzeme", value: "HDPE" },
        { name: "Test Standardı", value: "EN 50365" },
        { name: "Renk", value: "Sarı/Turuncu" }
      ]
    }
  ],
  "Göz Koruma": [
    {
      name: "Güvenlik Gözlüğü",
      description: "Çarpma ve kimyasal sıçramaya karşı koruma sağlayan güvenlik gözlüğü.",
      shortDescription: "Çok amaçlı güvenlik gözlüğü",
      models: ["CX2", "Sportstyle", "Pheos", "I-Vo", "Genesis"],
      priceRange: { min: 25, max: 85 },
      specifications: [
        { name: "Lens", value: "Polikarbonat" },
        { name: "UV Koruma", value: "99.9%" },
        { name: "Anti-fog", value: "Evet" },
        { name: "Standart", value: "EN 166" }
      ]
    },
    {
      name: "Kaynak Gözlüğü",
      description: "Kaynak işlemleri için özel tasarlanmış koruyucu gözlük. Yüksek sıcaklık dayanımı.",
      shortDescription: "Kaynak işleri için gözlük",
      models: ["Weld-Pro", "Arc-Shield", "Fusion", "Flame-Guard"],
      priceRange: { min: 45, max: 150 },
      specifications: [
        { name: "Filtre", value: "DIN 5-13" },
        { name: "Sıcaklık", value: "-40°C +120°C" },
        { name: "Malzeme", value: "Nylon" },
        { name: "Standart", value: "EN 175" }
      ]
    }
  ],
  "Kulak Koruma": [
    {
      name: "Gürültü Önleyici Kulaklık",
      description: "Yüksek gürültü seviyelerine karşı etkili koruma sağlayan kulaklık. Ergonomik tasarım.",
      shortDescription: "Profesyonel gürültü koruması",
      models: ["X5A", "Optime III", "H540A", "Clarity", "Thunder"],
      priceRange: { min: 35, max: 150 },
      specifications: [
        { name: "SNR Değeri", value: "25-37 dB" },
        { name: "Frekans", value: "125-8000 Hz" },
        { name: "Ağırlık", value: "200-500g" },
        { name: "Standart", value: "EN 352-1" }
      ]
    },
    {
      name: "Kulak Tıkacı",
      description: "Tek kullanımlık veya yeniden kullanılabilir kulak tıkacı. Farklı boyut seçenekleri.",
      shortDescription: "Konforlu kulak tıkacı",
      models: ["Soft", "Classic", "Laser Lite", "E-A-R", "Foam"],
      priceRange: { min: 5, max: 25 },
      specifications: [
        { name: "SNR Değeri", value: "28-39 dB" },
        { name: "Malzeme", value: "PU Köpük" },
        { name: "Kullanım", value: "Tek/Çoklu" },
        { name: "Standart", value: "EN 352-2" }
      ]
    }
  ],
  "Solunum Koruma": [
    {
      name: "Toz Maskesi",
      description: "Zararlı toz ve partiküllere karşı koruma sağlayan filtreli maske.",
      shortDescription: "Partikül filtreli maske",
      models: ["FFP2", "FFP3", "N95", "P2", "P3"],
      priceRange: { min: 8, max: 45 },
      specifications: [
        { name: "Filtre Sınıfı", value: "FFP1/FFP2/FFP3" },
        { name: "Verimlilik", value: "%94-99" },
        { name: "Kullanım", value: "Tek kullanımlık" },
        { name: "Standart", value: "EN 149" }
      ]
    },
    {
      name: "Yarım Yüz Maskesi",
      description: "Değiştirilebilir filtreli yarım yüz maskesi. Kimyasal ve gaz koruması.",
      shortDescription: "Değiştirilebilir filtreli maske",
      models: ["6200", "7500", "Advantage", "Force8", "Twin"],
      priceRange: { min: 85, max: 250 },
      specifications: [
        { name: "Filtre Tipi", value: "A1, A2, P3" },
        { name: "Malzeme", value: "Silikon" },
        { name: "Boyut", value: "S, M, L" },
        { name: "Standart", value: "EN 140" }
      ]
    }
  ],
  "El Koruma": [
    {
      name: "İş Eldiveni",
      description: "Genel amaçlı iş eldiveni. Kesik ve aşınma koruması sağlar.",
      shortDescription: "Çok amaçlı iş eldiveni",
      models: ["HyFlex", "PowerFlex", "Sensilite", "Activarmr", "Grip"],
      priceRange: { min: 12, max: 65 },
      specifications: [
        { name: "Koruma Seviyesi", value: "Level 1-5" },
        { name: "Malzeme", value: "Nitrile/Latex" },
        { name: "Boyut", value: "6-11" },
        { name: "Standart", value: "EN 388" }
      ]
    },
    {
      name: "Kimyasal Eldiven",
      description: "Kimyasal maddelere karşı koruma sağlayan eldiven. Uzun kullanım ömrü.",
      shortDescription: "Kimyasal dayanımlı eldiven",
      models: ["AlphaTec", "Solvex", "Neotop", "Barrier", "Chem"],
      priceRange: { min: 25, max: 120 },
      specifications: [
        { name: "Kimyasal", value: "A-K-L Sınıfı" },
        { name: "Kalınlık", value: "0.4-0.8mm" },
        { name: "Uzunluk", value: "30-60cm" },
        { name: "Standart", value: "EN 374" }
      ]
    }
  ],
  "Ayak Koruma": [
    {
      name: "Güvenlik Ayakkabısı",
      description: "Çelik burunlu güvenlik ayakkabısı. Su geçirmez ve anti-slip taban.",
      shortDescription: "Çelik burunlu iş ayakkabısı",
      models: ["S3", "S1P", "S2", "Safety", "Work"],
      priceRange: { min: 120, max: 350 },
      specifications: [
        { name: "Koruma", value: "200J Çelik Burun" },
        { name: "Taban", value: "Anti-slip" },
        { name: "Su Geçirmez", value: "Evet" },
        { name: "Standart", value: "EN ISO 20345" }
      ]
    },
    {
      name: "Güvenlik Botu",
      description: "Yüksek bilek korumalı güvenlik botu. Zorlu arazi koşulları için ideal.",
      shortDescription: "Yüksek korumalı güvenlik botu",
      models: ["Ranger", "Tactical", "Combat", "Field", "Heavy"],
      priceRange: { min: 180, max: 450 },
      specifications: [
        { name: "Yükseklik", value: "15-20cm" },
        { name: "Malzeme", value: "Deri/Tekstil" },
        { name: "Taban", value: "Vibram" },
        { name: "Standart", value: "EN ISO 20345" }
      ]
    }
  ],
  "Vücut Koruma": [
    {
      name: "Koruyucu Tulum",
      description: "Kimyasal ve partikül koruması sağlayan tek kullanımlık tulum.",
      shortDescription: "Kimyasal koruyucu tulum",
      models: ["Tyvek", "Tychem", "Microgard", "ProShield", "Barrier"],
      priceRange: { min: 15, max: 85 },
      specifications: [
        { name: "Tip", value: "3, 4, 5, 6" },
        { name: "Malzeme", value: "PE/PP" },
        { name: "Boyut", value: "S-XXXL" },
        { name: "Standart", value: "EN 14126" }
      ]
    },
    {
      name: "Reflektörlü Yelek",
      description: "Yüksek görünürlük sağlayan reflektörlü güvenlik yeleği.",
      shortDescription: "Yüksek görünürlük yeleği",
      models: ["Hi-Vis", "Reflective", "Safety", "Visibility", "Bright"],
      priceRange: { min: 25, max: 75 },
      specifications: [
        { name: "Sınıf", value: "Class 2/3" },
        { name: "Renk", value: "Turuncu/Sarı" },
        { name: "Reflektör", value: "3M Scotchlite" },
        { name: "Standart", value: "EN ISO 20471" }
      ]
    }
  ],
  "Yüksekte Çalışma": [
    {
      name: "Emniyet Kemeri",
      description: "Yüksekte çalışma için tam vücut emniyet kemeri. Çoklu bağlantı noktası.",
      shortDescription: "Tam vücut emniyet kemeri",
      models: ["Miller", "Titan", "ExoFit", "Form", "Revolution"],
      priceRange: { min: 150, max: 400 },
      specifications: [
        { name: "Bağlantı", value: "2-4 nokta" },
        { name: "Ağırlık", value: "1.5-2.5kg" },
        { name: "Boyut", value: "S-XXL" },
        { name: "Standart", value: "EN 361" }
      ]
    },
    {
      name: "Düşme Durdurucusu",
      description: "Otomatik düşme durdurma sistemi. Retraktör mekanizmalı.",
      shortDescription: "Otomatik düşme durdurucusu",
      models: ["Rebel", "Nano-Lok", "Talon", "Sealed-Blok", "Ultra"],
      priceRange: { min: 200, max: 600 },
      specifications: [
        { name: "Uzunluk", value: "1.8-3.5m" },
        { name: "Kapasite", value: "140kg" },
        { name: "Tip", value: "Retraktör" },
        { name: "Standart", value: "EN 360" }
      ]
    }
  ]
}

// Utility functions
function getRandomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

function getRandomPrice(min: number, max: number): number {
  return Math.round((Math.random() * (max - min) + min) * 100) / 100
}

function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)]
}

function generateSKU(brand: string, model: string): string {
  const brandCode = brand.substring(0, 3).toUpperCase()
  const modelCode = model.substring(0, 3).toUpperCase()
  const randomNum = getRandomInt(100, 999)
  return `${brandCode}-${modelCode}-${randomNum}`
}

function calculateStockStatus(stockQuantity: number, minThreshold: number): ProductStockStatus {
  if (stockQuantity <= 0) return "OUT_OF_STOCK"
  if (stockQuantity <= minThreshold) return "LOW_STOCK"
  return "IN_STOCK"
}

// Main seeding function
async function main() {
  console.log('🌱 Starting comprehensive database seeding...')
  console.log('')

  try {
    // Step 1: Verify database migration
    console.log('🔍 Step 1: Verifying database schema...')
    await verifyDatabaseSchema()

    // Step 2: Get existing categories
    console.log('📂 Step 2: Loading categories...')
    const categories = await loadCategories()

    // Step 3: Clear existing products (optional)
    console.log('🧹 Step 3: Cleaning existing products...')
    await clearExistingProducts()

    // Step 4: Seed products for each category
    console.log('🛍️  Step 4: Creating products...')
    const productStats = await seedProducts(categories)

    // Step 5: Update category product counts
    console.log('📊 Step 5: Updating category counts...')
    await updateCategoryProductCounts()

    // Step 6: Display summary
    console.log('')
    console.log('✅ Seeding completed successfully!')
    console.log('')
    console.log('📊 Summary:')
    console.log(`  Categories: ${categories.length}`)
    console.log(`  Products created: ${productStats.total}`)
    console.log(`  Products per category: ${Math.round(productStats.total / categories.length)}`)
    console.log('')
    console.log('🎯 Stock Distribution:')
    productStats.stockDistribution.forEach(stat => {
      console.log(`  ${stat.status}: ${stat.count} products`)
    })

  } catch (error) {
    console.error('❌ Seeding failed:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Verification functions
async function verifyDatabaseSchema() {
  try {
    // Test if new stock fields exist by trying to select them
    await prisma.product.findFirst({
      select: {
        stockQuantity: true,
        minStockThreshold: true,
        maxStockCapacity: true,
        stockStatus: true
      }
    })
    console.log('✅ Database schema verified - stock management fields exist')
  } catch (error) {
    console.error('❌ Database schema verification failed!')
    console.error('💡 Please run the stock management migration first:')
    console.error('   npx prisma db push')
    console.error('   OR')
    console.error('   Execute: prisma/migrations/add_stock_management_fields.sql')
    throw new Error('Missing stock management fields in database')
  }
}

async function loadCategories() {
  const categories = await prisma.category.findMany({
    where: { isActive: true },
    orderBy: { sortOrder: 'asc' }
  })

  if (categories.length === 0) {
    throw new Error('No categories found. Please seed categories first.')
  }

  console.log(`✅ Found ${categories.length} categories`)
  categories.forEach(cat => {
    console.log(`   - ${cat.name}`)
  })

  return categories
}

async function clearExistingProducts() {
  const existingCount = await prisma.product.count()

  if (existingCount > 0) {
    console.log(`⚠️  Found ${existingCount} existing products`)
    console.log('🗑️  Clearing existing products...')

    // Delete related records first
    await prisma.productImage.deleteMany()
    await prisma.productSpecification.deleteMany()
    await prisma.productCertificate.deleteMany()
    await prisma.product.deleteMany()

    console.log('✅ Existing products cleared')
  } else {
    console.log('✅ No existing products found')
  }
}

async function seedProducts(categories: any[]) {
  const stats = {
    total: 0,
    stockDistribution: [] as { status: string; count: number }[]
  }

  const stockStatusCounts = {
    "IN_STOCK": 0,
    "LOW_STOCK": 0,
    "OUT_OF_STOCK": 0
  }

  for (const category of categories) {
    console.log(`\n📦 Creating products for: ${category.name}`)

    const templates = productTemplates[category.name as keyof typeof productTemplates]
    if (!templates) {
      console.log(`⚠️  No templates found for category: ${category.name}`)
      continue
    }

    // Create 10-12 products per category
    const productsToCreate = getRandomInt(10, 12)
    let categoryProductCount = 0

    for (let i = 0; i < productsToCreate; i++) {
      try {
        const template = getRandomElement(templates)
        const brand = getRandomElement(turkishBrands)
        const model = getRandomElement(template.models)
        const stockScenario = getRandomElement(stockScenarios)

        // Generate product data
        const basePrice = getRandomPrice(template.priceRange.min, template.priceRange.max)
        const costPrice = basePrice * 0.6 // 40% margin
        const productName = `${brand} ${template.name} ${model}`
        const slug = productName.toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .trim()

        // Create product
        const product = await prisma.product.create({
          data: {
            name: productName,
            slug: `${slug}-${getRandomInt(100, 999)}`,
            description: template.description,
            shortDescription: template.shortDescription,
            categoryId: category.id,
            brand: brand,
            model: model,

            // Pricing
            basePrice: basePrice,
            baseCostPrice: costPrice,
            taxRate: 20,
            currency: "TRY",

            // Stock Management - Using new fields
            trackStock: true,
            stockQuantity: stockScenario.stockQuantity,
            minStockThreshold: stockScenario.minStockThreshold,
            maxStockCapacity: stockScenario.maxStockCapacity,
            stockStatus: stockScenario.status as ProductStockStatus,
            reservedStock: 0,

            // Product status
            isActive: true,
            isFeatured: Math.random() < 0.3, // 30% featured
            isNew: Math.random() < 0.2, // 20% new
            isOnSale: Math.random() < 0.25, // 25% on sale

            // SEO
            seoTitle: `${productName} - ${brand} | İş Güvenliği`,
            seoDescription: `${template.shortDescription}. ${brand} kalitesi ile güvenilir koruma. Hızlı teslimat ve uygun fiyat.`,
            metaKeywords: JSON.stringify([
              brand.toLowerCase(),
              template.name.toLowerCase(),
              category.name.toLowerCase(),
              "iş güvenliği",
              "koruyucu ekipman"
            ]),

            // Physical properties
            weight: getRandomPrice(0.1, 3.0),
            dimensions: JSON.stringify({
              length: getRandomInt(10, 50),
              width: getRandomInt(10, 40),
              height: getRandomInt(5, 30)
            }),

            publishedAt: new Date()
          }
        })

        // Create product images
        await createProductImages(product.id, productName)

        // Create product specifications
        await createProductSpecifications(product.id, template.specifications)

        // Create product certificates
        await createProductCertificates(product.id, category.name)

        // Update statistics
        stats.total++
        categoryProductCount++
        stockStatusCounts[stockScenario.status as keyof typeof stockStatusCounts]++

        console.log(`  ✅ ${categoryProductCount}. ${productName} (${stockScenario.name})`)

      } catch (error) {
        console.error(`  ❌ Failed to create product ${i + 1}:`, error)
      }
    }

    console.log(`✅ Created ${categoryProductCount} products for ${category.name}`)
  }

  // Prepare stock distribution stats
  stats.stockDistribution = [
    { status: "In Stock", count: stockStatusCounts.IN_STOCK },
    { status: "Low Stock", count: stockStatusCounts.LOW_STOCK },
    { status: "Out of Stock", count: stockStatusCounts.OUT_OF_STOCK }
  ]

  return stats
}

async function createProductImages(productId: string, productName: string) {
  const imageCount = getRandomInt(2, 4)

  for (let i = 0; i < imageCount; i++) {
    await prisma.productImage.create({
      data: {
        productId: productId,
        url: `/placeholder.svg?height=400&width=400&text=${encodeURIComponent(productName)}`,
        alt: `${productName} - Görünüm ${i + 1}`,
        title: `${productName}`,
        sortOrder: i,
        isMain: i === 0,
        width: 400,
        height: 400,
        format: "SVG"
      }
    })
  }
}

async function createProductSpecifications(productId: string, specifications: any[]) {
  for (let i = 0; i < specifications.length; i++) {
    const spec = specifications[i]
    await prisma.productSpecification.create({
      data: {
        productId: productId,
        name: spec.name,
        value: spec.value,
        unit: spec.unit || null,
        sortOrder: i
      }
    })
  }
}

async function createProductCertificates(productId: string, categoryName: string) {
  const certificates = [
    { name: "CE", issuer: "Avrupa Birliği", description: "Avrupa Uygunluk Belgesi" },
    { name: "TSE", issuer: "Türk Standardları Enstitüsü", description: "Türk Standardı Belgesi" },
    { name: "ISO 9001", issuer: "ISO", description: "Kalite Yönetim Sistemi" }
  ]

  // Add 1-2 certificates per product
  const certCount = getRandomInt(1, 2)
  const selectedCerts = certificates.slice(0, certCount)

  for (const cert of selectedCerts) {
    await prisma.productCertificate.create({
      data: {
        productId: productId,
        name: cert.name,
        issuer: cert.issuer,
        number: `${cert.name}-${getRandomInt(10000, 99999)}`,
        validUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        documentUrl: null
      }
    })
  }
}

async function updateCategoryProductCounts() {
  const categories = await prisma.category.findMany()

  for (const category of categories) {
    const productCount = await prisma.product.count({
      where: { categoryId: category.id }
    })

    await prisma.category.update({
      where: { id: category.id },
      data: { productCount }
    })
  }

  console.log('✅ Category product counts updated')
}

// Export for use in other scripts
export { main as comprehensiveSeed }

// Run if called directly
if (require.main === module) {
  main()
    .catch((error) => {
      console.error(error)
      process.exit(1)
    })
}
