"use client"

import { useState } from "react"
import { ArrowL<PERSON>t, RotateCcw } from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { SortableCategoryList } from "@/components/admin/sortable-category-list"
import { CategoryFormModal } from "@/components/admin/category-form-modal"
import { DeleteConfirmModal } from "@/components/admin/delete-confirm-modal"
import { useCategories } from "@/lib/hooks/use-categories"
import type { Category } from "@/types"

export default function CategorySortingPage() {
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [deletingCategory, setDeletingCategory] = useState<Category | null>(null)
  const [isOrderSaved, setIsOrderSaved] = useState(true)

  const {
    data: categoriesData,
    loading,
    error,
    refetch,
    updateOrder,
  } = useCategories({
    sortBy: "sortOrder",
    sortOrder: "asc",
    limit: 100, // Get all categories for sorting
  })

  const categories = categoriesData?.data || []

  const handleEditCategory = (category: Category) => {
    setEditingCategory(category)
  }

  const handleDeleteCategory = (category: Category) => {
    setDeletingCategory(category)
  }

  const handleModalClose = () => {
    setEditingCategory(null)
    setDeletingCategory(null)
    refetch()
  }

  const handleOrderUpdate = async (newOrder: Category[]) => {
    await updateOrder(newOrder)
    setIsOrderSaved(false)
  }

  const handleSaveOrder = () => {
    setIsOrderSaved(true)
  }

  const handleResetOrder = () => {
    refetch()
    setIsOrderSaved(true)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto"></div>
          <p className="mt-2 text-gray-600">Kategoriler yükleniyor...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">Hata: {error}</p>
        <Button onClick={refetch} className="mt-4">
          Tekrar Dene
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/admin/kategoriler">
            <Button variant="outline" size="icon">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Kategori Sıralaması</h1>
            <p className="text-gray-600 mt-2">Kategorileri sürükleyip bırakarak sırasını değiştirin</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={refetch} className="flex items-center gap-2 bg-transparent">
            <RotateCcw className="h-4 w-4" />
            Yenile
          </Button>
          {!isOrderSaved && (
            <Button onClick={handleSaveOrder} className="ml-2">
              Sıralamayı Kaydet
            </Button>
          )}
          <Button onClick={handleResetOrder} className="ml-2">
            Sıfırla
          </Button>
        </div>
      </div>

      {/* Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <div className="bg-blue-500 rounded-full p-1">
            <div className="w-4 h-4 text-white text-xs flex items-center justify-center font-bold">i</div>
          </div>
          <div>
            <h3 className="font-medium text-blue-900 mb-1">Nasıl Kullanılır?</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Kategorileri sürükleyip bırakarak sırasını değiştirin</li>
              <li>• Değişiklikleri kaydetmek için "Sıralamayı Kaydet" butonuna tıklayın</li>
              <li>• Değişiklikleri iptal etmek için "Sıfırla" butonunu kullanın</li>
              <li>• Sıralama ana sayfada ve müşteri arayüzünde yansıtılacaktır</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Sortable Category List */}
      <SortableCategoryList
        categories={categories}
        onOrderUpdate={handleOrderUpdate}
        onEdit={handleEditCategory}
        onDelete={handleDeleteCategory}
      />

      {/* Modals */}
      <CategoryFormModal open={!!editingCategory} onClose={handleModalClose} category={editingCategory} />

      <DeleteConfirmModal
        open={!!deletingCategory}
        onClose={handleModalClose}
        title="Kategoriyi Sil"
        description={`"${deletingCategory?.name}" kategorisini silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.`}
        onConfirm={async () => {
          console.log("Delete category:", deletingCategory?.id)
          handleModalClose()
        }}
      />
    </div>
  )
}
