#!/usr/bin/env node

/**
 * SEO Integration Test Script
 * Tests the complete SEO generation pipeline
 */

const testProducts = [
  {
    name: "Onvec Smart Tag Test",
    data: {
      productName: "Onvec Smart Tag Akıllı Takip Cihazı 4 adet (Apple uyumlu)",
      productDescription: "Onvec Smart Tag Akıllı Takip Cihazı, anahtarlarınızdan değerli eşyalarınıza ve hatta evcil hayvanlarınıza kadar birçok farklı şeyi takip etmek için kullanabileceğiniz kullanışlı bir cihazdır. Eşyalarınızı kaybetme endişesi olmadan güvenle kullanabilirsiniz.",
      category: "Telefonlar & Aksesuarlar",
      brand: "Onvec",
      price: 899
    }
  },
  {
    name: "Safety Equipment Test",
    data: {
      productName: "3M H-700 Güvenlik Bareti",
      productDescription: "3M H-700 serisi güvenlik bareti, yü<PERSON><PERSON> darbe dayanımı ve konfor sağlar. İnşaat, endüstri ve genel iş güvenliği için idealdir. UV koruması ve ayarlanabilir kafa bandı ile uzun süreli kullanım için tasarlanmıştır.",
      category: "Baş Koruma",
      brand: "3M",
      price: 65
    }
  },
  {
    name: "Electronics Test",
    data: {
      productName: "Samsung Galaxy Buds Pro",
      productDescription: "Samsung Galaxy Buds Pro kablosuz kulaklık ile müziğin tadını çıkarın. Aktif gürültü engelleme, uzun pil ömrü ve kristal berraklığında ses kalitesi. Spor ve günlük kullanım için ideal.",
      category: "Kulaklık",
      brand: "Samsung",
      price: 899
    }
  }
]

async function testAPIStatus() {
  console.log('🔍 Testing API Status...')
  
  try {
    const response = await fetch('http://localhost:3000/api/seo/generate')
    const data = await response.json()
    
    console.log('✅ API Status:', {
      running: data.success,
      llmConfigured: data.status?.llmConfigured,
      fallbackEnabled: data.status?.fallbackEnabled,
      environment: data.status?.environment
    })
    
    return data.status
  } catch (error) {
    console.error('❌ API Status Check Failed:', error.message)
    return null
  }
}

async function testSEOGeneration(productData, productName) {
  console.log(`\n📦 Testing: ${productName}`)
  console.log('─'.repeat(50))
  
  const startTime = Date.now()
  
  try {
    const response = await fetch('http://localhost:3000/api/seo/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(productData)
    })
    
    const duration = Date.now() - startTime
    const result = await response.json()
    
    if (!response.ok) {
      console.error(`❌ HTTP ${response.status}:`, result.message)
      return { success: false, error: result.message, duration }
    }
    
    if (result.success && result.data) {
      const seo = result.data
      
      console.log(`✅ Generation successful (${duration}ms)`)
      console.log(`   SEO Title: "${seo.seoTitle}" (${seo.seoTitle?.length}/60)`)
      console.log(`   SEO Description: "${seo.seoDescription}" (${seo.seoDescription?.length}/160)`)
      console.log(`   Focus Keyword: "${seo.focusKeyword}"`)
      console.log(`   Meta Keywords: ${seo.metaKeywords?.length} items`)
      console.log(`   Canonical URL: "${seo.canonicalUrl}"`)
      console.log(`   Structured Data: ${seo.structuredData ? 'Present' : 'Missing'}`)
      
      // Validation
      const issues = []
      if (!seo.seoTitle || seo.seoTitle.length > 60) issues.push('SEO Title')
      if (!seo.seoDescription || seo.seoDescription.length > 160) issues.push('SEO Description')
      if (!seo.metaKeywords || seo.metaKeywords.length === 0) issues.push('Meta Keywords')
      if (!seo.focusKeyword) issues.push('Focus Keyword')
      if (!seo.canonicalUrl) issues.push('Canonical URL')
      if (!seo.structuredData) issues.push('Structured Data')
      
      if (issues.length > 0) {
        console.log(`⚠️  Issues: ${issues.join(', ')}`)
      } else {
        console.log('✅ All validations passed')
      }
      
      return { success: true, data: seo, duration, issues }
    } else {
      console.error('❌ Generation failed:', result.message)
      return { success: false, error: result.message, duration }
    }
    
  } catch (error) {
    const duration = Date.now() - startTime
    console.error('❌ Request failed:', error.message)
    return { success: false, error: error.message, duration }
  }
}

async function runIntegrationTests() {
  console.log('🚀 SEO Integration Test Suite')
  console.log('=' .repeat(60))
  
  // Test API status
  const apiStatus = await testAPIStatus()
  
  if (!apiStatus) {
    console.log('\n❌ Cannot proceed - API is not accessible')
    console.log('💡 Make sure the development server is running: npm run dev')
    return
  }
  
  // Test SEO generation for each product
  const results = []
  
  for (const product of testProducts) {
    const result = await testSEOGeneration(product.data, product.name)
    results.push({ name: product.name, ...result })
    
    // Wait between requests to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  
  // Summary
  console.log('\n📊 Test Summary')
  console.log('=' .repeat(60))
  
  const successful = results.filter(r => r.success).length
  const failed = results.filter(r => !r.success).length
  const totalDuration = results.reduce((sum, r) => sum + r.duration, 0)
  const avgDuration = totalDuration / results.length
  
  console.log(`✅ Successful: ${successful}/${results.length}`)
  console.log(`❌ Failed: ${failed}/${results.length}`)
  console.log(`⏱️  Average Duration: ${Math.round(avgDuration)}ms`)
  console.log(`📊 Total Duration: ${totalDuration}ms`)
  
  if (apiStatus.llmConfigured) {
    console.log('🤖 LLM: Configured and used')
  } else {
    console.log('🔄 LLM: Not configured, using fallback')
  }
  
  // Detailed results
  console.log('\n📋 Detailed Results:')
  results.forEach(result => {
    const status = result.success ? '✅' : '❌'
    const issues = result.issues?.length ? ` (${result.issues.length} issues)` : ''
    console.log(`   ${status} ${result.name}: ${result.duration}ms${issues}`)
    if (result.error) {
      console.log(`      Error: ${result.error}`)
    }
  })
  
  // Recommendations
  console.log('\n💡 Recommendations:')
  if (!apiStatus.llmConfigured) {
    console.log('   - Configure an LLM API key for better SEO generation')
    console.log('   - See docs/LLM_INTEGRATION_GUIDE.md for setup instructions')
  }
  
  if (failed > 0) {
    console.log('   - Check server logs for detailed error information')
    console.log('   - Verify API endpoints are accessible')
  }
  
  if (avgDuration > 5000) {
    console.log('   - Consider optimizing LLM response time')
    console.log('   - Implement caching for better performance')
  }
  
  console.log('\n🎉 Integration test completed!')
}

// Handle command line arguments
const args = process.argv.slice(2)

if (args.includes('--help') || args.includes('-h')) {
  console.log(`
SEO Integration Test Script

Usage:
  node scripts/test-seo-integration.js [options]

Options:
  --help, -h     Show this help message
  --status       Check API status only
  --single       Test single product only

Examples:
  node scripts/test-seo-integration.js
  node scripts/test-seo-integration.js --status
  node scripts/test-seo-integration.js --single
`)
  process.exit(0)
}

if (args.includes('--status')) {
  testAPIStatus().then(() => process.exit(0))
} else if (args.includes('--single')) {
  testAPIStatus().then(async (status) => {
    if (status) {
      await testSEOGeneration(testProducts[0].data, testProducts[0].name)
    }
    process.exit(0)
  })
} else {
  runIntegrationTests().then(() => process.exit(0))
}
