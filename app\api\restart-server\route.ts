import { NextRequest, NextResponse } from 'next/server'
import { exec } from 'child_process'
import { promisify } from 'util'

const execAsync = promisify(exec)

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Attempting to restart development server...')

    // First, try to generate Prisma client with a clean environment
    console.log('🔧 Attempting Prisma generate with clean environment...')
    
    try {
      const { stdout, stderr } = await execAsync('npx prisma generate', {
        cwd: process.cwd(),
        timeout: 30000,
        env: {
          ...process.env,
          PRISMA_GENERATE_SKIP_AUTOINSTALL: 'true'
        }
      })
      
      console.log('✅ Prisma generate successful!')
      console.log('STDOUT:', stdout)
      if (stderr) console.log('STDERR:', stderr)
      
      // Test the new fields
      console.log('🔍 Testing stock field access...')
      
      // Import fresh Prisma client
      delete require.cache[require.resolve('@/lib/prisma')]
      const { prisma } = require('@/lib/prisma')
      
      const testProduct = await prisma.product.findFirst({
        select: {
          id: true,
          name: true,
          stockQuantity: true,
          minStockThreshold: true,
          maxStockCapacity: true,
          trackStock: true,
          basePrice: true
        }
      })

      console.log('✅ Stock fields are now accessible!')
      console.log('Test product:', testProduct)

      return NextResponse.json({
        success: true,
        message: 'Prisma client regenerated and stock fields are working',
        testProduct: testProduct,
        nextSteps: [
          'Prisma client has been regenerated successfully',
          'Stock fields are now accessible',
          'Admin panel should work correctly now',
          'You can test the product edit form'
        ]
      })
      
    } catch (generateError: any) {
      console.log('❌ Prisma generate failed, trying alternative approach...')
      console.log('Generate error:', generateError.message)
      
      // Alternative: Try to clear Prisma cache and restart
      console.log('🧹 Clearing Prisma cache...')
      
      try {
        await execAsync('npx prisma generate --force-reset', {
          cwd: process.cwd(),
          timeout: 30000
        })
        
        console.log('✅ Force reset successful!')
        
        return NextResponse.json({
          success: true,
          message: 'Prisma client force reset completed',
          note: 'Please restart the development server manually for changes to take effect',
          instructions: [
            'Stop the development server (Ctrl+C)',
            'Run: npm run dev',
            'Test the admin panel'
          ]
        })
        
      } catch (resetError: any) {
        console.log('❌ Force reset also failed:', resetError.message)
        
        return NextResponse.json({
          success: false,
          error: 'Prisma client regeneration failed',
          details: {
            generateError: generateError.message,
            resetError: resetError.message
          },
          solution: {
            message: 'Manual restart required',
            steps: [
              'The database migration was successful',
              'Stock fields exist in the database',
              'Stop the development server (Ctrl+C)',
              'Run: npx prisma generate',
              'Run: npm run dev',
              'Test the admin panel'
            ]
          }
        }, { status: 500 })
      }
    }

  } catch (error: any) {
    console.error('❌ Server restart failed:', error)
    return NextResponse.json({
      success: false,
      error: error.message,
      solution: 'Please restart the development server manually'
    }, { status: 500 })
  }
}
