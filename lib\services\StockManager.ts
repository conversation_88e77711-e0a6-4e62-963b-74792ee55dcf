import { prisma } from '@/lib/prisma'
import { calculateStockStatus, type StockInfo } from '@/types'

export interface StockReservationResult {
  success: boolean
  reservationId?: string
  availableStock?: number
  error?: string
}

export interface StockValidationResult {
  isValid: boolean
  availableStock: number
  requestedQuantity: number
  error?: string
}

/**
 * Enhanced Stock Manager with atomic operations and reservation system
 * Integrates with our existing enhanced pricing and stock management
 */
export class StockManager {
  
  /**
   * Get enhanced stock information for a product
   */
  static async getStockInfo(productId: string): Promise<StockInfo & { availableStock: number }> {
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: {
        stockQuantity: true,
        minStockThreshold: true,
        reservedStock: true,
        trackStock: true,
        stockStatus: true
      }
    })
    
    if (!product) {
      throw new Error('Product not found')
    }
    
    const availableStock = product.stockQuantity - product.reservedStock
    const status = calculateStockStatus(availableStock, product.minStockThreshold)
    
    return {
      quantity: availableStock,
      status,
      isLowStock: status === 'LOW_STOCK',
      isOutOfStock: status === 'OUT_OF_STOCK',
      availableStock
    }
  }
  
  /**
   * Validate stock availability before reservation
   */
  static async validateStock(productId: string, quantity: number): Promise<StockValidationResult> {
    const stockInfo = await this.getStockInfo(productId)
    
    return {
      isValid: quantity <= stockInfo.availableStock,
      availableStock: stockInfo.availableStock,
      requestedQuantity: quantity,
      error: quantity > stockInfo.availableStock 
        ? `Insufficient stock. Available: ${stockInfo.availableStock}, Requested: ${quantity}`
        : undefined
    }
  }
  
  /**
   * Reserve stock atomically for cart items
   */
  static async reserveStock(
    productId: string, 
    quantity: number, 
    sessionId: string,
    cartItemId?: string,
    expirationHours: number = 24
  ): Promise<StockReservationResult> {
    try {
      return await prisma.$transaction(async (tx) => {
        // 1. Lock and validate product stock
        const product = await tx.product.findUnique({
          where: { id: productId },
          select: { 
            stockQuantity: true, 
            reservedStock: true, 
            trackStock: true,
            minStockThreshold: true
          }
        })
        
        if (!product) {
          return { success: false, error: 'Product not found' }
        }
        
        if (!product.trackStock) {
          return { success: true, availableStock: 999999 } // No tracking needed
        }
        
        const availableStock = product.stockQuantity - product.reservedStock
        
        if (quantity > availableStock) {
          return { 
            success: false, 
            error: `Insufficient stock. Available: ${availableStock}`,
            availableStock 
          }
        }
        
        // 2. Create stock reservation
        const reservation = await tx.stockReservation.create({
          data: {
            productId,
            quantity,
            sessionId,
            cartItemId,
            status: 'ACTIVE',
            reason: 'cart',
            expiresAt: new Date(Date.now() + expirationHours * 60 * 60 * 1000)
          }
        })
        
        // 3. Update reserved stock atomically
        await tx.product.update({
          where: { id: productId },
          data: { 
            reservedStock: { increment: quantity },
            stockStatus: calculateStockStatus(
              availableStock - quantity, 
              product.minStockThreshold
            )
          }
        })
        
        // 4. Log inventory movement
        await tx.productInventoryLog.create({
          data: {
            productId,
            changeType: 'reserved',
            quantity: -quantity,
            previousStock: product.stockQuantity,
            newStock: product.stockQuantity, // Physical stock unchanged
            reason: 'cart_reservation',
            reference: reservation.id,
            notes: `Reserved for session ${sessionId}`
          }
        })
        
        return { 
          success: true, 
          reservationId: reservation.id,
          availableStock: availableStock - quantity
        }
      })
    } catch (error) {
      console.error('Stock reservation error:', error)
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }
  
  /**
   * Release stock reservation (cart item removed or expired)
   */
  static async releaseReservation(reservationId: string): Promise<boolean> {
    try {
      return await prisma.$transaction(async (tx) => {
        const reservation = await tx.stockReservation.findUnique({
          where: { id: reservationId },
          include: { product: true }
        })
        
        if (!reservation || reservation.status !== 'ACTIVE') {
          return false
        }
        
        // 1. Update reservation status
        await tx.stockReservation.update({
          where: { id: reservationId },
          data: { 
            status: 'RELEASED',
            releasedAt: new Date()
          }
        })
        
        // 2. Release reserved stock
        const updatedProduct = await tx.product.update({
          where: { id: reservation.productId },
          data: { reservedStock: { decrement: reservation.quantity } },
          select: { stockQuantity: true, reservedStock: true, minStockThreshold: true }
        })
        
        // 3. Update stock status
        const newAvailableStock = updatedProduct.stockQuantity - updatedProduct.reservedStock
        await tx.product.update({
          where: { id: reservation.productId },
          data: { 
            stockStatus: calculateStockStatus(newAvailableStock, updatedProduct.minStockThreshold)
          }
        })
        
        // 4. Log inventory movement
        await tx.productInventoryLog.create({
          data: {
            productId: reservation.productId,
            changeType: 'released',
            quantity: reservation.quantity,
            previousStock: updatedProduct.stockQuantity,
            newStock: updatedProduct.stockQuantity,
            reason: 'reservation_released',
            reference: reservationId,
            notes: `Released reservation for session ${reservation.sessionId}`
          }
        })
        
        return true
      })
    } catch (error) {
      console.error('Release reservation error:', error)
      return false
    }
  }
  
  /**
   * Convert reservation to order (deduct actual stock)
   */
  static async convertReservationToOrder(
    reservationId: string, 
    orderId: string
  ): Promise<boolean> {
    try {
      return await prisma.$transaction(async (tx) => {
        const reservation = await tx.stockReservation.findUnique({
          where: { id: reservationId }
        })
        
        if (!reservation || reservation.status !== 'ACTIVE') {
          return false
        }
        
        // 1. Update reservation status
        await tx.stockReservation.update({
          where: { id: reservationId },
          data: { 
            status: 'CONVERTED',
            orderId
          }
        })
        
        // 2. Deduct actual stock and release reservation
        const updatedProduct = await tx.product.update({
          where: { id: reservation.productId },
          data: { 
            stockQuantity: { decrement: reservation.quantity },
            reservedStock: { decrement: reservation.quantity }
          },
          select: { stockQuantity: true, reservedStock: true, minStockThreshold: true }
        })
        
        // 3. Update stock status
        const newAvailableStock = updatedProduct.stockQuantity - updatedProduct.reservedStock
        await tx.product.update({
          where: { id: reservation.productId },
          data: { 
            stockStatus: calculateStockStatus(newAvailableStock, updatedProduct.minStockThreshold)
          }
        })
        
        // 4. Log inventory movement
        await tx.productInventoryLog.create({
          data: {
            productId: reservation.productId,
            changeType: 'out',
            quantity: -reservation.quantity,
            previousStock: updatedProduct.stockQuantity + reservation.quantity,
            newStock: updatedProduct.stockQuantity,
            reason: 'order_confirmed',
            reference: orderId,
            notes: `Stock deducted for order ${orderId}`
          }
        })
        
        return true
      })
    } catch (error) {
      console.error('Convert reservation error:', error)
      return false
    }
  }
  
  /**
   * Cleanup expired reservations (run as scheduled job)
   */
  static async cleanupExpiredReservations(): Promise<number> {
    try {
      const expiredReservations = await prisma.stockReservation.findMany({
        where: {
          status: 'ACTIVE',
          expiresAt: { lt: new Date() }
        }
      })
      
      let cleanedCount = 0
      
      for (const reservation of expiredReservations) {
        const released = await this.releaseReservation(reservation.id)
        if (released) {
          cleanedCount++
        }
      }
      
      return cleanedCount
    } catch (error) {
      console.error('Cleanup expired reservations error:', error)
      return 0
    }
  }
  
  /**
   * Get low stock products for reorder alerts
   */
  static async getLowStockProducts(): Promise<any[]> {
    return await prisma.product.findMany({
      where: {
        trackStock: true,
        stockStatus: 'LOW_STOCK'
      },
      select: {
        id: true,
        name: true,
        stockQuantity: true,
        minStockThreshold: true,
        reservedStock: true,
        reorderPoint: true,
        reorderQuantity: true,
        stockLocation: true
      }
    })
  }
}
