import { fetcher } from "@/lib/utils"
import type { CategoryBanner, DeviceType } from "@/types"

const BASE_URL = "/api/category-banners"

export interface CategoryBannerFilter {
  categoryId?: string
  isActive?: boolean
  deviceType?: DeviceType
  includeInactive?: boolean
  page?: number
  limit?: number
}

export interface CategoryBannerFormData {
  categoryId: string
  imageUrl: string
  imageAlt?: string
  mobileImageUrl?: string
  title?: string
  subtitle?: string
  description?: string
  displayOrder?: number
  isActive?: boolean
  startDate?: string
  endDate?: string
  displayDuration?: number
  transitionType?: string
  backgroundColor?: string
  textColor?: string
  priority?: number
  targetAudience?: string
  deviceType?: DeviceType[]
  createdBy?: string
}

class CategoryBannerServiceClass {
  // Tüm banner'ları getir (admin için)
  async getAll(filter?: CategoryBannerFilter): Promise<{
    data: CategoryBanner[]
    pagination?: {
      total: number
      page: number
      limit: number
      totalPages: number
      hasNext: boolean
      hasPrev: boolean
    }
  }> {
    let url = BASE_URL
    if (filter) {
      const params = new URLSearchParams()
      if (filter.categoryId) params.append('categoryId', filter.categoryId)
      if (filter.isActive !== undefined) params.append('isActive', filter.isActive.toString())
      if (filter.page) params.append('page', filter.page.toString())
      if (filter.limit) params.append('limit', filter.limit.toString())

      const queryString = params.toString()
      if (queryString) url += `?${queryString}`
    }

    const response = await fetcher(url)
    return response
  }

  // Kategoriye ait aktif banner'ları getir
  async getByCategoryId(
    categoryId: string, 
    deviceType: DeviceType = 'DESKTOP',
    includeInactive: boolean = false
  ): Promise<CategoryBanner[]> {
    const params = new URLSearchParams()
    params.append('deviceType', deviceType)
    if (includeInactive) params.append('includeInactive', 'true')

    const url = `${BASE_URL}/category/${categoryId}?${params.toString()}`
    const response = await fetcher(url)
    return response.data || []
  }

  // Tek banner getir
  async getById(id: string): Promise<CategoryBanner> {
    const response = await fetcher(`${BASE_URL}/${id}`)
    return response.data
  }

  // Yeni banner oluştur
  async create(data: CategoryBannerFormData): Promise<{
    success: boolean
    message: string
    data?: CategoryBanner
  }> {
    const response = await fetcher(BASE_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })
    return response
  }

  // Banner güncelle
  async update(id: string, data: Partial<CategoryBannerFormData>): Promise<{
    success: boolean
    message: string
    data?: CategoryBanner
  }> {
    const response = await fetcher(`${BASE_URL}/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })
    return response
  }

  // Banner sil
  async delete(id: string): Promise<{
    success: boolean
    message: string
  }> {
    const response = await fetcher(`${BASE_URL}/${id}`, {
      method: 'DELETE',
    })
    return response
  }

  // Banner tıklama kaydı
  async trackClick(categoryId: string, bannerId: string, clickData?: any): Promise<{
    success: boolean
    message: string
    data?: any
  }> {
    const response = await fetcher(`${BASE_URL}/category/${categoryId}/click`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ bannerId, clickData }),
    })
    return response
  }

  // Banner sıralama güncelle (drag & drop için)
  async updateOrder(categoryId: string, bannerOrders: { id: string; displayOrder: number }[]): Promise<{
    success: boolean
    message: string
  }> {
    try {
      const updatePromises = bannerOrders.map(({ id, displayOrder }) =>
        this.update(id, { displayOrder })
      )
      
      await Promise.all(updatePromises)
      
      return {
        success: true,
        message: "Banner sıralaması güncellendi"
      }
    } catch (error) {
      console.error("Banner order update error:", error)
      return {
        success: false,
        message: "Banner sıralaması güncellenirken hata oluştu"
      }
    }
  }

  // Aktif banner'ları cihaz tipine göre getir (frontend için optimize edilmiş)
  async getActiveBanners(
    categoryId: string,
    options: {
      deviceType?: DeviceType
      limit?: number
      prioritySort?: boolean
    } = {}
  ): Promise<CategoryBanner[]> {
    const { deviceType = 'DESKTOP', limit, prioritySort = true } = options
    
    let banners = await this.getByCategoryId(categoryId, deviceType, false)
    
    if (prioritySort) {
      banners = banners.sort((a, b) => {
        // Önce priority'ye göre sırala
        if (a.priority !== b.priority) {
          return b.priority - a.priority
        }
        // Sonra displayOrder'a göre sırala
        return a.displayOrder - b.displayOrder
      })
    }
    
    if (limit && limit > 0) {
      banners = banners.slice(0, limit)
    }
    
    return banners
  }
}

// Singleton instance
const categoryBannerServiceInstance = new CategoryBannerServiceClass()

// Convenience functions
export const getCategoryBanners = (filter?: CategoryBannerFilter) => 
  categoryBannerServiceInstance.getAll(filter)

export const getCategoryBannersByCategoryId = (
  categoryId: string, 
  deviceType?: DeviceType,
  includeInactive?: boolean
) => categoryBannerServiceInstance.getByCategoryId(categoryId, deviceType, includeInactive)

export const getCategoryBannerById = (id: string) => 
  categoryBannerServiceInstance.getById(id)

export const createCategoryBanner = (data: CategoryBannerFormData) => 
  categoryBannerServiceInstance.create(data)

export const updateCategoryBanner = (id: string, data: Partial<CategoryBannerFormData>) => 
  categoryBannerServiceInstance.update(id, data)

export const deleteCategoryBanner = (id: string) => 
  categoryBannerServiceInstance.delete(id)

export const trackBannerClick = (categoryId: string, bannerId: string, clickData?: any) => 
  categoryBannerServiceInstance.trackClick(categoryId, bannerId, clickData)

export const getActiveCategoryBanners = (
  categoryId: string,
  options?: {
    deviceType?: DeviceType
    limit?: number
    prioritySort?: boolean
  }
) => categoryBannerServiceInstance.getActiveBanners(categoryId, options)

export default categoryBannerServiceInstance
