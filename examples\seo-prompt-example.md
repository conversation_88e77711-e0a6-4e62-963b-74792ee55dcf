# SEO Generation Prompt Kullanım Örneği

## Test Ürünü

**Ürün Adı:** Knmaster Kntag 4 - Akıllı Takip Cihazı
**Ürün Açıklaması:** KNMASTER KNTAG 4, Apple Find My ağı ile entegre ç<PERSON>, kiş<PERSON>l eşyalarınızı, motosikletinizi veya değerli eşyalarınızı hızlı ve güvenli bir şekilde takip etmenizi sağlayan 4 adet yenilikçi akıllı takip cihazı paketidir. Küçük ve kompakt tasarımı sayesinde, her türlü eşyada rahatlıkla kullanılabilir ve taşınabilir, bu da onun hayatınızı kolaylaştıran bir yardımcı olmasını sağlar.
**Kategori:** Elektronik
**Marka:** Knmaster
**Fiyat:** 299 TL

## Hazır Prompt (LLM'e Gönderilecek)

```
Sen Türkiye e-ticaret pazarı için uzman bir SEO specialist ve içerik stratejistisin. Türk tüketicileri, arama motorları (Google.com.tr, Yandex.com.tr) ve schema.org standartları hakkında derin bilgin var.

Görevin: Verilen ürün bilgileri için kapsamlı, profesyonel SEO içeriği oluşturmak. Çıktı Türkçe arama motorları ve Türk tüketiciler için optimize edilmiş olmalı.

ÜRÜN BİLGİLERİ:
Ürün Adı: Knmaster Kntag 4 - Akıllı Takip Cihazı
Ürün Açıklaması: KNMASTER KNTAG 4, Apple Find My ağı ile entegre çalışan, kişisel eşyalarınızı, motosikletinizi veya değerli eşyalarınızı hızlı ve güvenli bir şekilde takip etmenizi sağlayan 4 adet yenilikçi akıllı takip cihazı paketidir. Küçük ve kompakt tasarımı sayesinde, her türlü eşyada rahatlıkla kullanılabilir ve taşınabilir, bu da onun hayatınızı kolaylaştıran bir yardımcı olmasını sağlar.
Kategori: Elektronik
Marka: Knmaster
Fiyat: 299 TL

ÇIKTI GEREKSİNİMLERİ:

Aşağıdaki JSON formatında tam ve geçerli bir yanıt döndür:

{
  "seoTitle": "string (50-60 karakter)",
  "seoDescription": "string (120-160 karakter)",
  "metaKeywords": ["dizi formatında maksimum 10 anahtar kelime"],
  "focusKeyword": "string (ana hedef anahtar kelime)",
  "canonicalUrl": "string (URL slug formatı)",
  "robotsDirective": "index,follow",
  "ogTitle": "string (50-60 karakter)",
  "ogDescription": "string (120-160 karakter)",
  "ogType": "product",
  "twitterCard": "summary_large_image",
  "twitterTitle": "string (50-60 karakter)",
  "twitterDescription": "string (120-160 karakter)",
  "alternativeText": "string (görsel alt metni)",
  "breadcrumbs": ["dizi formatında navigasyon yolu"],
  "structuredData": {
    "@context": "https://schema.org/",
    "@type": "Product",
    "name": "string",
    "description": "string",
    "brand": {
      "@type": "Brand",
      "name": "string"
    },
    "offers": {
      "@type": "Offer",
      "price": "string",
      "priceCurrency": "TRY",
      "availability": "https://schema.org/InStock",
      "seller": {
        "@type": "Organization",
        "name": "Teknoloji Mağazası"
      }
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.5",
      "reviewCount": "25"
    }
  },
  "schemaMarkup": "string (yukarıdaki structuredData'nın JSON string formatı)"
}

[... tüm kurallar ve örnekler ...]

Şimdi verilen ürün bilgileri için yukarıdaki tüm kurallara uygun SEO içeriği oluştur ve sadece JSON yanıtını döndür.
```

## Beklenen Çıktı Örneği

```json
{
  "seoTitle": "Knmaster Kntag 4 Akıllı Takip Cihazı - 299 TL",
  "seoDescription": "Knmaster Kntag 4 ile eşyalarınızı güvenle takip edin. Apple Find My ağı desteği, kompakt tasarım ve 4'lü paket. Hızlı teslimat ve uygun fiyat.",
  "metaKeywords": ["knmaster kntag", "akıllı takip cihazı", "find my", "eşya takip", "bluetooth tracker", "apple find my", "kayıp eşya bulucu", "gps tracker"],
  "focusKeyword": "knmaster kntag 4 akıllı takip cihazı",
  "canonicalUrl": "/urun/knmaster-kntag-4-akilli-takip-cihazi",
  "robotsDirective": "index,follow",
  "ogTitle": "Knmaster Kntag 4 Akıllı Takip Cihazı - 4'lü Paket",
  "ogDescription": "Knmaster Kntag 4 ile eşyalarınızı güvenle takip edin. Apple Find My ağı desteği ve kompakt tasarım ile her yerde kullanım.",
  "ogType": "product",
  "twitterCard": "summary_large_image",
  "twitterTitle": "Knmaster Kntag 4 Akıllı Takip Cihazı",
  "twitterDescription": "Eşyalarınızı kaybetmeyin! Knmaster Kntag 4 ile Apple Find My ağı desteği. Kompakt ve güvenilir takip çözümü.",
  "alternativeText": "Knmaster Kntag 4 akıllı takip cihazı 4'lü paket - beyaz renk kompakt tasarım",
  "breadcrumbs": ["Ana Sayfa", "Elektronik", "Akıllı Cihazlar", "Takip Cihazları", "Knmaster Kntag 4"],
  "structuredData": {
    "@context": "https://schema.org/",
    "@type": "Product",
    "name": "Knmaster Kntag 4 - Akıllı Takip Cihazı",
    "description": "KNMASTER KNTAG 4, Apple Find My ağı ile entegre çalışan 4 adet akıllı takip cihazı. Kişisel eşyalarınızı güvenle takip edin.",
    "brand": {
      "@type": "Brand",
      "name": "Knmaster"
    },
    "offers": {
      "@type": "Offer",
      "price": "299.00",
      "priceCurrency": "TRY",
      "availability": "https://schema.org/InStock",
      "seller": {
        "@type": "Organization",
        "name": "Teknoloji Mağazası"
      }
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.6",
      "reviewCount": "42"
    }
  },
  "schemaMarkup": "{\"@context\":\"https://schema.org/\",\"@type\":\"Product\",\"name\":\"Knmaster Kntag 4 - Akıllı Takip Cihazı\",\"description\":\"KNMASTER KNTAG 4, Apple Find My ağı ile entegre çalışan 4 adet akıllı takip cihazı. Kişisel eşyalarınızı güvenle takip edin.\",\"brand\":{\"@type\":\"Brand\",\"name\":\"Knmaster\"},\"offers\":{\"@type\":\"Offer\",\"price\":\"299.00\",\"priceCurrency\":\"TRY\",\"availability\":\"https://schema.org/InStock\",\"seller\":{\"@type\":\"Organization\",\"name\":\"Teknoloji Mağazası\"}},\"aggregateRating\":{\"@type\":\"AggregateRating\",\"ratingValue\":\"4.6\",\"reviewCount\":\"42\"}}"
}
```

## Kullanım Talimatları

### 1. Prompt Hazırlama
- `prompts/seo-generation-text-prompt.txt` dosyasını kopyala
- `{PRODUCT_NAME}`, `{PRODUCT_DESCRIPTION}`, `{CATEGORY}`, `{BRAND}`, `{PRICE}` placeholder'larını gerçek değerlerle değiştir

### 2. LLM Servisleri
Bu prompt şu servislerle kullanılabilir:
- **Claude (Anthropic)**: claude.ai
- **Gemini (Google)**: gemini.google.com
- **ChatGPT (OpenAI)**: chat.openai.com
- **Perplexity**: perplexity.ai
- **Local LLM**: Ollama, LM Studio vb.

### 3. Prompt Gönderme
1. LLM servisine git
2. Hazırladığın prompt'u yapıştır
3. Gönder ve JSON yanıtını bekle
4. Çıktıyı kopyala ve ProductFormModal'da kullan

### 4. Sonuç İşleme
- JSON yanıtını parse et
- Her alanı ProductFormModal'daki ilgili alana kopyala
- Karakter limitlerini kontrol et
- Türkçe karakterlerin doğru olduğunu kontrol et

## Farklı Ürün Kategorileri İçin Örnekler

### İş Güvenliği Ürünü
```
Ürün Adı: 3M H-700 Güvenlik Bareti
Kategori: Baş Koruma
Marka: 3M
Fiyat: 65 TL
```

### Elektronik Ürün
```
Ürün Adı: Samsung Galaxy Buds Pro
Kategori: Kulaklık
Marka: Samsung
Fiyat: 899 TL
```

### Ev & Yaşam Ürünü
```
Ürün Adı: Philips Airfryer XXL
Kategori: Mutfak Aletleri
Marka: Philips
Fiyat: 1299 TL
```

## İpuçları

### Prompt Optimizasyonu
- Ürün açıklamasını detaylandır
- Kategori bilgisini net ver
- Marka adını doğru yaz
- Fiyat bilgisini güncel tut

### Kalite Kontrol
- JSON formatının geçerli olduğunu kontrol et
- Karakter limitlerini kontrol et (title: 60, description: 160)
- Türkçe gramer ve yazımı kontrol et
- Anahtar kelimelerin alakalı olduğunu kontrol et

### Hata Durumları
- JSON parse hatası: Prompt'u tekrar gönder
- Eksik alanlar: Prompt'ta tüm alanların gerekli olduğunu belirt
- Karakter limiti aşımı: LLM'den kısaltmasını iste
- Türkçe karakter sorunu: UTF-8 encoding kontrol et
