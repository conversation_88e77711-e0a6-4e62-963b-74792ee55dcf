"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CategoryImageUpload } from "@/components/ui/category-image-upload"
import { 
  createCategoryBanner, 
  updateCategoryBanner,
  type CategoryBannerFormData 
} from "@/lib/api/category-banners"
import {
  TransitionType,
  DeviceType,
  TransitionTypeLabels,
  DeviceTypeLabels,
  getEnumOptions
} from "@/lib/enums"
import type { CategoryBanner, Category } from "@/types"


interface CategoryBannerFormProps {
  category: Category
  banner?: CategoryBanner | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess?: () => void
}

export function CategoryBannerForm({ 
  category, 
  banner, 
  open, 
  onOpenChange, 
  onSuccess 
}: CategoryBannerFormProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Form state
  const [formData, setFormData] = useState<CategoryBannerFormData>({
    categoryId: category.id,
    imageUrl: '',
    imageAlt: '',
    mobileImageUrl: '',
    title: '',
    subtitle: '',
    description: '',
    displayOrder: 0,
    isActive: true,
    startDate: '',
    endDate: '',
    displayDuration: 5,
    transitionType: 'FADE',
    backgroundColor: '',
    textColor: '',
    priority: 1,
    deviceType: ['DESKTOP', 'MOBILE', 'TABLET']
  })

  // Initialize form with banner data if editing
  useEffect(() => {
    if (banner) {
      setFormData({
        categoryId: banner.categoryId,
        imageUrl: banner.imageUrl,
        imageAlt: banner.imageAlt || '',
        mobileImageUrl: banner.mobileImageUrl || '',
        title: banner.title || '',
        subtitle: banner.subtitle || '',
        description: banner.description || '',
        displayOrder: banner.displayOrder,
        isActive: banner.isActive,
        startDate: banner.startDate ? new Date(banner.startDate).toISOString().slice(0, 16) : '',
        endDate: banner.endDate ? new Date(banner.endDate).toISOString().slice(0, 16) : '',
        displayDuration: banner.displayDuration,
        transitionType: banner.transitionType,
        backgroundColor: banner.backgroundColor || '',
        textColor: banner.textColor || '',
        priority: banner.priority,
        deviceType: banner.deviceType
      })
    } else {
      // Reset form for new banner
      setFormData({
        categoryId: category.id,
        imageUrl: '',
        imageAlt: '',
        mobileImageUrl: '',
        title: '',
        subtitle: '',
        description: '',
        displayOrder: 0,
        isActive: true,
        startDate: '',
        endDate: '',
        displayDuration: 5,
        transitionType: 'FADE',
        backgroundColor: '',
        textColor: '',
        priority: 1,
        deviceType: ['DESKTOP', 'MOBILE', 'TABLET']
      })
    }
  }, [banner, category.id])

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.imageUrl.trim()) {
      setError('Resim URL\'si gerekli')
      return
    }

    try {
      setLoading(true)
      setError(null)

      if (banner) {
        // Update existing banner
        await updateCategoryBanner(banner.id, formData)
      } else {
        // Create new banner
        await createCategoryBanner(formData)
      }

      onSuccess?.()
      onOpenChange(false)
    } catch (err) {
      console.error('Error saving banner:', err)
      setError('Banner kaydedilirken hata oluştu')
    } finally {
      setLoading(false)
    }
  }

  // Handle device type toggle
  const toggleDeviceType = (deviceType: DeviceType) => {
    setFormData(prev => ({
      ...prev,
      deviceType: prev.deviceType.includes(deviceType)
        ? prev.deviceType.filter(d => d !== deviceType)
        : [...prev.deviceType, deviceType]
    }))
  }

  const transitionOptions = getEnumOptions(TransitionType, TransitionTypeLabels)

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {banner ? 'Banner Düzenle' : 'Yeni Banner Ekle'}
          </DialogTitle>
          <DialogDescription>
            {category.name} kategorisi için banner {banner ? 'düzenleyin' : 'oluşturun'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <div className="text-red-600 text-sm bg-red-50 p-3 rounded-lg">
              {error}
            </div>
          )}

          {/* Basic Info */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Temel Bilgiler</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="title">Başlık</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    placeholder="Banner başlığı"
                  />
                </div>
                
                <div>
                  <Label htmlFor="subtitle">Alt Başlık</Label>
                  <Input
                    id="subtitle"
                    value={formData.subtitle}
                    onChange={(e) => setFormData(prev => ({ ...prev, subtitle: e.target.value }))}
                    placeholder="Banner alt başlığı"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="description">Açıklama</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Banner açıklaması"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Images */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Görseller</CardTitle>
              <p className="text-sm text-gray-600">
                Bu görseller kategori detay sayfasındaki carousel'de kullanılacak
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Ana Banner Resmi *</Label>
                <CategoryImageUpload
                  imageUrl={formData.imageUrl || undefined}
                  onImageChange={(url) => setFormData(prev => ({ ...prev, imageUrl: url || '' }))}
                  placeholder="Ana banner resmi yüklemek için tıklayın veya sürükleyin"
                  label="Ana Banner Resmi"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Kategori detay sayfasında gösterilecek ana banner resmi (PNG, JPG, WEBP - Max 5MB)
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="imageAlt">Alt Metin</Label>
                  <Input
                    id="imageAlt"
                    value={formData.imageAlt}
                    onChange={(e) => setFormData(prev => ({ ...prev, imageAlt: e.target.value }))}
                    placeholder="Resim alt metni"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    SEO ve erişilebilirlik için resim açıklaması
                  </p>
                </div>

                <div>
                  <Label>Mobil Banner Resmi</Label>
                  <CategoryImageUpload
                    imageUrl={formData.mobileImageUrl || undefined}
                    onImageChange={(url) => setFormData(prev => ({ ...prev, mobileImageUrl: url || '' }))}
                    placeholder="Mobil banner resmi yüklemek için tıklayın"
                    label="Mobil Banner Resmi"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Mobil cihazlar için optimize edilmiş banner (opsiyonel)
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>



          {/* Display Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Görüntüleme Ayarları</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="displayOrder">Sıralama</Label>
                  <Input
                    id="displayOrder"
                    type="number"
                    min="0"
                    value={formData.displayOrder}
                    onChange={(e) => setFormData(prev => ({ ...prev, displayOrder: parseInt(e.target.value) || 0 }))}
                    placeholder="0"
                  />
                </div>

                <div>
                  <Label htmlFor="displayDuration">Gösterim Süresi (saniye)</Label>
                  <Input
                    id="displayDuration"
                    type="number"
                    min="1"
                    max="60"
                    value={formData.displayDuration}
                    onChange={(e) => setFormData(prev => ({ ...prev, displayDuration: parseInt(e.target.value) || 5 }))}
                    placeholder="5"
                  />
                </div>

                <div>
                  <Label htmlFor="priority">Öncelik</Label>
                  <Input
                    id="priority"
                    type="number"
                    min="1"
                    max="10"
                    value={formData.priority}
                    onChange={(e) => setFormData(prev => ({ ...prev, priority: parseInt(e.target.value) || 1 }))}
                    placeholder="1"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="transitionType">Geçiş Animasyonu</Label>
                  <Select
                    value={formData.transitionType}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, transitionType: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Geçiş türü seçin" />
                    </SelectTrigger>
                    <SelectContent>
                      {transitionOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                  />
                  <Label htmlFor="isActive">Banner Aktif</Label>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Scheduling */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Zamanlama</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="startDate">Başlangıç Tarihi</Label>
                  <Input
                    id="startDate"
                    type="datetime-local"
                    value={formData.startDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                  />
                  <p className="text-xs text-gray-500 mt-1">Boş bırakılırsa hemen başlar</p>
                </div>

                <div>
                  <Label htmlFor="endDate">Bitiş Tarihi</Label>
                  <Input
                    id="endDate"
                    type="datetime-local"
                    value={formData.endDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
                  />
                  <p className="text-xs text-gray-500 mt-1">Boş bırakılırsa süresiz</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Device Targeting */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Cihaz Hedefleme</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label>Hedef Cihazlar</Label>
                <div className="flex flex-wrap gap-4 mt-2">
                  {Object.values(DeviceType).map(deviceType => (
                    <div key={deviceType} className="flex items-center space-x-2">
                      <Switch
                        id={`device-${deviceType}`}
                        checked={formData.deviceType.includes(deviceType)}
                        onCheckedChange={() => toggleDeviceType(deviceType)}
                      />
                      <Label htmlFor={`device-${deviceType}`}>
                        {DeviceTypeLabels[deviceType]}
                      </Label>
                    </div>
                  ))}
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  Banner hangi cihazlarda gösterilsin?
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Styling */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Stil Özelleştirme</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="backgroundColor">Arka Plan Rengi</Label>
                  <div className="flex gap-2">
                    <Input
                      id="backgroundColor"
                      type="color"
                      value={formData.backgroundColor || '#000000'}
                      onChange={(e) => setFormData(prev => ({ ...prev, backgroundColor: e.target.value }))}
                      className="w-16 h-10 p-1 border rounded"
                    />
                    <Input
                      value={formData.backgroundColor}
                      onChange={(e) => setFormData(prev => ({ ...prev, backgroundColor: e.target.value }))}
                      placeholder="#000000 veya transparent"
                      className="flex-1"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="textColor">Metin Rengi</Label>
                  <div className="flex gap-2">
                    <Input
                      id="textColor"
                      type="color"
                      value={formData.textColor || '#ffffff'}
                      onChange={(e) => setFormData(prev => ({ ...prev, textColor: e.target.value }))}
                      className="w-16 h-10 p-1 border rounded"
                    />
                    <Input
                      value={formData.textColor}
                      onChange={(e) => setFormData(prev => ({ ...prev, textColor: e.target.value }))}
                      placeholder="#ffffff"
                      className="flex-1"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>



          {/* Preview */}
          {formData.imageUrl && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Önizleme</CardTitle>
              </CardHeader>
              <CardContent>
                <BannerPreview formData={formData} />
              </CardContent>
            </Card>
          )}

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              İptal
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Kaydediliyor...' : (banner ? 'Güncelle' : 'Oluştur')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}

// Banner Preview Component
interface BannerPreviewProps {
  formData: CategoryBannerFormData
}

function BannerPreview({ formData }: BannerPreviewProps) {
  const customStyles = {
    backgroundColor: formData.backgroundColor || undefined,
    color: formData.textColor || undefined
  }

  return (
    <div className="space-y-4">
      <div className="flex gap-4 mb-4">
        <Badge variant="outline">Desktop Önizleme</Badge>
        <Badge variant="outline">Süre: {formData.displayDuration}s</Badge>
        <Badge variant="outline">Geçiş: {TransitionTypeLabels[formData.transitionType as TransitionType]}</Badge>
      </div>

      <div
        className="relative w-full h-64 overflow-hidden rounded-lg border"
        style={formData.backgroundColor ? { backgroundColor: formData.backgroundColor } : undefined}
      >
        {/* Background Image */}
        <img
          src={formData.imageUrl}
          alt={formData.imageAlt || formData.title || 'Banner Preview'}
          className="w-full h-full object-cover"
          onError={(e) => {
            e.currentTarget.style.display = 'none'
          }}
        />

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />

        {/* Content */}
        <div className="absolute inset-0 flex items-end">
          <div className="p-6 text-white w-full" style={formData.textColor ? { color: formData.textColor } : undefined}>
            <div className="max-w-4xl">
              {formData.title && (
                <h2 className="text-2xl md:text-3xl font-bold mb-2 leading-tight">
                  {formData.title}
                </h2>
              )}

              {formData.subtitle && (
                <h3 className="text-lg font-medium mb-2 opacity-90">
                  {formData.subtitle}
                </h3>
              )}

              {formData.description && (
                <p className="text-sm opacity-80 mb-4 leading-relaxed">
                  {formData.description}
                </p>
              )}


            </div>
          </div>
        </div>
      </div>

      {/* Mobile Preview */}
      <div className="mt-6">
        <Badge variant="outline" className="mb-2">Mobil Önizleme</Badge>
        <div className="w-80 mx-auto">
          <div
            className="relative w-full h-48 overflow-hidden rounded-lg border"
            style={formData.backgroundColor ? { backgroundColor: formData.backgroundColor } : undefined}
          >
            <img
              src={formData.mobileImageUrl || formData.imageUrl}
              alt={formData.imageAlt || formData.title || 'Mobile Banner Preview'}
              className="w-full h-full object-cover"
              onError={(e) => {
                e.currentTarget.style.display = 'none'
              }}
            />

            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />

            <div className="absolute inset-0 flex items-end">
              <div className="p-4 text-white w-full" style={formData.textColor ? { color: formData.textColor } : undefined}>
                {formData.title && (
                  <h2 className="text-lg font-bold mb-1 leading-tight">
                    {formData.title}
                  </h2>
                )}

                {formData.subtitle && (
                  <h3 className="text-sm font-medium mb-1 opacity-90">
                    {formData.subtitle}
                  </h3>
                )}

                {formData.description && (
                  <p className="text-xs opacity-80 mb-2 leading-relaxed line-clamp-2">
                    {formData.description}
                  </p>
                )}


              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Settings Summary */}
      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-medium mb-2">Banner Ayarları</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-500">Sıralama:</span> {formData.displayOrder}
          </div>
          <div>
            <span className="text-gray-500">Öncelik:</span> {formData.priority}
          </div>
          <div>
            <span className="text-gray-500">Durum:</span> {formData.isActive ? 'Aktif' : 'Pasif'}
          </div>
          <div>
            <span className="text-gray-500">Cihazlar:</span> {formData.deviceType.length}
          </div>
          {formData.startDate && (
            <div>
              <span className="text-gray-500">Başlangıç:</span> {new Date(formData.startDate).toLocaleDateString('tr-TR')}
            </div>
          )}
          {formData.endDate && (
            <div>
              <span className="text-gray-500">Bitiş:</span> {new Date(formData.endDate).toLocaleDateString('tr-TR')}
            </div>
          )}
          {formData.conversionGoal && (
            <div>
              <span className="text-gray-500">Hedef:</span> {formData.conversionGoal}
            </div>
          )}
          {formData.budgetAllocation && (
            <div>
              <span className="text-gray-500">Bütçe:</span> ₺{formData.budgetAllocation}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
