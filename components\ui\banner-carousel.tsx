"use client"

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { ChevronLeft, ChevronRight, Play, Pause } from 'lucide-react'
import { cn } from '@/lib/utils'

export interface CarouselItem {
  id: string
  content: React.ReactNode
  duration?: number // saniye cinsinden, varsayılan 5 saniye
}

export interface BannerCarouselProps {
  items: CarouselItem[]
  autoplay?: boolean
  autoplayDelay?: number // milisaniye cinsinden, varsayılan 5000ms
  showNavigation?: boolean
  showPagination?: boolean
  showPlayPause?: boolean
  infinite?: boolean
  pauseOnHover?: boolean
  swipeEnabled?: boolean
  transitionDuration?: number // milisaniye cinsinden, varsayılan 500ms
  transitionType?: 'FADE' | 'SLIDE' | 'ZOOM' | 'FLIP' | 'CUBE' | 'COVERFLOW' // Add transition type
  className?: string
  onSlideChange?: (index: number) => void
  onItemClick?: (item: CarouselItem, index: number) => void
}

export function BannerCarousel({
  items,
  autoplay = true,
  autoplayDelay = 5000,
  showNavigation = true,
  showPagination = true,
  showPlayPause = false,
  infinite = true,
  pauseOnHover = true,
  swipeEnabled = true,
  transitionDuration = 500,
  transitionType = 'SLIDE',
  className,
  onSlideChange,
  onItemClick
}: BannerCarouselProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isPlaying, setIsPlaying] = useState(autoplay)
  const [isPaused, setIsPaused] = useState(false)
  const [touchStart, setTouchStart] = useState<number | null>(null)
  const [touchEnd, setTouchEnd] = useState<number | null>(null)
  const [isTransitioning, setIsTransitioning] = useState(false)

  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const carouselRef = useRef<HTMLDivElement>(null)

  // Minimum swipe distance (in px)
  const minSwipeDistance = 50

  // Get transition styles based on transition type
  const getTransitionStyles = () => {
    switch (transitionType) {
      case 'FADE':
        return {
          container: 'relative overflow-hidden',
          item: 'absolute inset-0 transition-opacity duration-500 ease-in-out',
          transform: (index: number) => ({
            opacity: index === currentIndex ? 1 : 0,
            zIndex: index === currentIndex ? 1 : 0
          })
        }
      case 'ZOOM':
        return {
          container: 'relative overflow-hidden',
          item: 'absolute inset-0 transition-all duration-500 ease-in-out',
          transform: (index: number) => ({
            opacity: index === currentIndex ? 1 : 0,
            transform: index === currentIndex ? 'scale(1)' : 'scale(0.8)',
            zIndex: index === currentIndex ? 1 : 0
          })
        }
      case 'FLIP':
        return {
          container: 'relative overflow-hidden',
          item: 'absolute inset-0 transition-all duration-500 ease-in-out',
          transform: (index: number) => ({
            opacity: index === currentIndex ? 1 : 0,
            transform: index === currentIndex ? 'rotateY(0deg)' : 'rotateY(90deg)',
            zIndex: index === currentIndex ? 1 : 0
          }),
          containerStyle: { perspective: '1000px' }
        }
      case 'SLIDE':
      default:
        return {
          container: 'flex transition-transform duration-500 ease-in-out',
          item: 'w-full flex-shrink-0',
          transform: () => ({
            transform: `translateX(-${currentIndex * 100}%)`
          })
        }
    }
  }

  const nextSlide = useCallback(() => {
    if (items.length === 0) return
    
    setCurrentIndex(prevIndex => {
      const newIndex = infinite 
        ? (prevIndex + 1) % items.length
        : Math.min(prevIndex + 1, items.length - 1)
      
      onSlideChange?.(newIndex)
      return newIndex
    })
  }, [items.length, infinite, onSlideChange])

  const prevSlide = useCallback(() => {
    if (items.length === 0) return
    
    setCurrentIndex(prevIndex => {
      const newIndex = infinite
        ? prevIndex === 0 ? items.length - 1 : prevIndex - 1
        : Math.max(prevIndex - 1, 0)
      
      onSlideChange?.(newIndex)
      return newIndex
    })
  }, [items.length, infinite, onSlideChange])

  const goToSlide = useCallback((index: number) => {
    if (index >= 0 && index < items.length) {
      setCurrentIndex(index)
      onSlideChange?.(index)
    }
  }, [items.length, onSlideChange])

  const togglePlayPause = useCallback(() => {
    setIsPlaying(prev => !prev)
  }, [])

  // Autoplay logic
  useEffect(() => {
    if (isPlaying && !isPaused && items.length > 1) {
      const currentItem = items[currentIndex]
      const delay = currentItem?.duration ? currentItem.duration * 1000 : autoplayDelay
      
      intervalRef.current = setTimeout(() => {
        nextSlide()
      }, delay)
    }

    return () => {
      if (intervalRef.current) {
        clearTimeout(intervalRef.current)
      }
    }
  }, [currentIndex, isPlaying, isPaused, items, autoplayDelay, nextSlide])

  // Touch handlers
  const onTouchStart = (e: React.TouchEvent) => {
    if (!swipeEnabled) return
    setTouchEnd(null)
    setTouchStart(e.targetTouches[0].clientX)
  }

  const onTouchMove = (e: React.TouchEvent) => {
    if (!swipeEnabled) return
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const onTouchEnd = () => {
    if (!swipeEnabled || !touchStart || !touchEnd) return
    
    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > minSwipeDistance
    const isRightSwipe = distance < -minSwipeDistance

    if (isLeftSwipe) {
      nextSlide()
    } else if (isRightSwipe) {
      prevSlide()
    }
  }

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowLeft') {
        prevSlide()
      } else if (e.key === 'ArrowRight') {
        nextSlide()
      } else if (e.key === ' ') {
        e.preventDefault()
        togglePlayPause()
      }
    }

    if (carouselRef.current) {
      carouselRef.current.addEventListener('keydown', handleKeyDown)
    }

    return () => {
      if (carouselRef.current) {
        carouselRef.current.removeEventListener('keydown', handleKeyDown)
      }
    }
  }, [prevSlide, nextSlide, togglePlayPause])

  // Pause on hover
  const handleMouseEnter = () => {
    if (pauseOnHover) {
      setIsPaused(true)
    }
  }

  const handleMouseLeave = () => {
    if (pauseOnHover) {
      setIsPaused(false)
    }
  }

  // Handle item click
  const handleItemClick = (item: CarouselItem, index: number) => {
    onItemClick?.(item, index)
  }

  if (items.length === 0) {
    return null
  }

  return (
    <div
      ref={carouselRef}
      className={cn(
        "relative w-full overflow-hidden rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500",
        className
      )}
      tabIndex={0}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onTouchStart={onTouchStart}
      onTouchMove={onTouchMove}
      onTouchEnd={onTouchEnd}
      role="region"
      aria-label="Banner Carousel"
      aria-live="polite"
    >
      {/* Carousel Items */}
      <div
        className={getTransitionStyles().container}
        style={{
          ...(transitionType === 'SLIDE' ? {
            transform: `translateX(-${currentIndex * 100}%)`,
            transitionDuration: `${transitionDuration}ms`
          } : {}),
          ...(getTransitionStyles().containerStyle || {})
        }}
      >
        {items.map((item, index) => (
          <div
            key={item.id}
            className={`cursor-pointer ${getTransitionStyles().item}`}
            style={transitionType !== 'SLIDE' ? {
              ...getTransitionStyles().transform(index),
              transitionDuration: `${transitionDuration}ms`
            } : undefined}
            onClick={() => handleItemClick(item, index)}
            role="tabpanel"
            aria-label={`Banner ${index + 1} of ${items.length}`}
          >
            {item.content}
          </div>
        ))}
      </div>

      {/* Navigation Arrows */}
      {showNavigation && items.length > 1 && (
        <>
          <button
            onClick={prevSlide}
            className="absolute left-2 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white z-10"
            aria-label="Previous banner"
            disabled={!infinite && currentIndex === 0}
          >
            <ChevronLeft className="w-5 h-5" />
          </button>
          
          <button
            onClick={nextSlide}
            className="absolute right-2 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white z-10"
            aria-label="Next banner"
            disabled={!infinite && currentIndex === items.length - 1}
          >
            <ChevronRight className="w-5 h-5" />
          </button>
        </>
      )}

      {/* Pagination Dots */}
      {showPagination && items.length > 1 && (
        <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex space-x-2 z-10">
          {items.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={cn(
                "w-3 h-3 rounded-full transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white",
                index === currentIndex
                  ? "bg-white"
                  : "bg-white/50 hover:bg-white/70"
              )}
              aria-label={`Go to banner ${index + 1}`}
            />
          ))}
        </div>
      )}

      {/* Play/Pause Button */}
      {showPlayPause && items.length > 1 && (
        <button
          onClick={togglePlayPause}
          className="absolute top-4 right-4 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-white z-10"
          aria-label={isPlaying ? "Pause carousel" : "Play carousel"}
        >
          {isPlaying ? (
            <Pause className="w-4 h-4" />
          ) : (
            <Play className="w-4 h-4" />
          )}
        </button>
      )}

      {/* Progress Bar (optional) */}
      {autoplay && isPlaying && !isPaused && items.length > 1 && (
        <div className="absolute bottom-0 left-0 w-full h-1 bg-black/20 z-10">
          <div 
            className="h-full bg-white transition-all duration-100 ease-linear"
            style={{
              width: `${((currentIndex + 1) / items.length) * 100}%`
            }}
          />
        </div>
      )}

      {/* Screen Reader Info */}
      <div className="sr-only" aria-live="polite" aria-atomic="true">
        Banner {currentIndex + 1} of {items.length}
      </div>
    </div>
  )
}
