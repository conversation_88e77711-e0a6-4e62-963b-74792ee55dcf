"use client"

import React, { useState, useCallback, useRef } from 'react'
import { Upload, X, Image as ImageIcon, Loader2, Eye, Edit } from 'lucide-react'
import { Button } from './button'
import { cn } from '@/lib/utils'
import { validateCategoryImageFiles, uploadCategoryImages, deleteCategoryImage, extractFilenameFromUrl } from '@/lib/category-image-utils'
import { ImageEditorModal } from './image-editor-modal'

interface CategoryImageUploadProps {
  imageUrl?: string
  onImageChange: (imageUrl: string | null) => void
  maxFileSize?: number
  className?: string
  disabled?: boolean
  label?: string
  placeholder?: string
}

export function CategoryImageUpload({
  imageUrl,
  onImageChange,
  maxFileSize = 5 * 1024 * 1024, // 5MB
  className,
  disabled = false,
  label = "Resim Yükle",
  placeholder = "Resim yüklemek için tıklayın veya sürükleyin"
}: CategoryImageUploadProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [showImageEditor, setShowImageEditor] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [fileInputKey, setFileInputKey] = useState(0) // Force re-render of file input
  const fileInputRef = useRef<HTMLInputElement>(null)
  const triggerTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // Debug logging for state changes
  console.log('🎨 CategoryImageUpload render state:')
  console.log('   - Label:', label)
  console.log('   - Image URL:', imageUrl)
  console.log('   - Image URL Type:', typeof imageUrl)
  console.log('   - Image URL Length:', imageUrl?.length || 0)
  console.log('   - Has Image URL:', !!imageUrl)
  console.log('   - Is Uploading:', isUploading)

  // TEMPORARY DEBUG: Test with a sample image URL
  const debugImageUrl = imageUrl || (label === "Kategori Görseli" ? "https://via.placeholder.com/400x300/0066cc/ffffff?text=Test+Image" : undefined)
  console.log('🔧 DEBUG: Using imageUrl:', debugImageUrl)

  // Enhanced file input trigger with robust error handling and cleanup
  const triggerFileInput = useCallback(() => {
    console.log('🔥 TRIGGER FILE INPUT CALLED')

    // Clear any existing timeout
    if (triggerTimeoutRef.current) {
      clearTimeout(triggerTimeoutRef.current)
      triggerTimeoutRef.current = null
    }

    // Prevent multiple rapid clicks
    if (isUploading || showImageEditor) {
      console.log('⚠️ Upload in progress or editor open, ignoring trigger')
      return
    }

    const attemptTrigger = () => {
      if (fileInputRef.current) {
        console.log('🔥 FILE INPUT REF EXISTS, CLICKING...')

        // Reset file input value to ensure change event fires
        fileInputRef.current.value = ''

        // Force focus and click
        try {
          fileInputRef.current.focus()
          fileInputRef.current.click()
          console.log('✅ File input clicked successfully')
        } catch (error) {
          console.error('❌ Error clicking file input:', error)
          // Fallback: recreate file input
          setFileInputKey(prev => prev + 1)
          console.log('🔄 Recreating file input due to click error')
        }
      } else {
        console.log('❌ FILE INPUT REF NOT FOUND, recreating...')
        setFileInputKey(prev => prev + 1)

        // Retry after recreation
        triggerTimeoutRef.current = setTimeout(() => {
          if (fileInputRef.current) {
            console.log('🔄 Retrying file input click after recreation')
            fileInputRef.current.value = ''
            fileInputRef.current.focus()
            fileInputRef.current.click()
          }
        }, 100)
      }
    }

    // Small delay to ensure DOM is ready
    triggerTimeoutRef.current = setTimeout(attemptTrigger, 10)
  }, [isUploading, showImageEditor])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    if (!disabled) {
      setIsDragging(true)
    }
  }, [disabled])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)

    if (disabled) return

    const files = Array.from(e.dataTransfer.files)
    console.log('📁 CategoryImageUpload: Files dropped', {
      label,
      filesCount: files.length,
      timestamp: new Date().toISOString()
    })

    if (files.length > 0) {
      const file = files[0]
      console.log('📁 Dropped file details:', {
        name: file.name,
        size: file.size,
        type: file.type
      })

      // Validate file before opening editor
      console.log('🔍 Validating dropped file...')
      const validation = validateCategoryImageFiles([file])
      console.log('🔍 Validation result:', validation)

      if (!validation.valid) {
        console.error('❌ Dropped file validation failed:', validation.errors)
        return
      }

      console.log('✅ Dropped file validation passed, opening image editor...')

      // Open image editor instead of immediate upload
      setSelectedFile(file)
      console.log('📁 Set selected file from drop:', file.name)

      setShowImageEditor(true)
      console.log('🎨 Set showImageEditor to true from drop')
    }
  }, [disabled, label])

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('📁 CategoryImageUpload: File selected', {
      label,
      filesCount: e.target.files?.length || 0,
      timestamp: new Date().toISOString()
    })

    // Clear any pending timeouts
    if (triggerTimeoutRef.current) {
      clearTimeout(triggerTimeoutRef.current)
      triggerTimeoutRef.current = null
    }

    const files = Array.from(e.target.files || [])
    console.log('📁 Files array:', files)

    if (files.length > 0) {
      const file = files[0]
      console.log('📁 Selected file details:', {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified
      })

      // Validate file before opening editor
      console.log('🔍 Validating file...')
      const validation = validateCategoryImageFiles([file])
      console.log('🔍 Validation result:', validation)

      if (!validation.valid) {
        console.error('❌ File validation failed:', validation.errors)
        // Reset input and recreate for next attempt
        e.target.value = ''
        setFileInputKey(prev => prev + 1)
        return
      }

      console.log('✅ File validation passed, opening image editor...', {
        validationResult: validation,
        fileName: file.name
      })

      // Open image editor instead of immediate upload
      setSelectedFile(file)
      console.log('📁 Set selected file:', file.name)

      setShowImageEditor(true)
      console.log('🎨 Set showImageEditor to true - Modal should open now!')
    } else {
      console.log('❌ No files selected')
    }

    // Always reset input value and recreate for next use
    e.target.value = ''
    // Recreate file input to prevent browser state issues
    setTimeout(() => setFileInputKey(prev => prev + 1), 100)
  }, [label])

  // Handle edit existing image
  const handleEditImage = useCallback(async () => {
    if (!imageUrl) return

    console.log('🎨 CategoryImageUpload: Starting edit of existing image', imageUrl)
    setIsUploading(true)

    try {
      // Fetch the existing image and convert to File object
      const response = await fetch(imageUrl)
      const blob = await response.blob()
      const fileName = extractFilenameFromUrl(imageUrl) || 'existing-image.jpg'
      const file = new File([blob], fileName, { type: blob.type || 'image/jpeg' })

      console.log('🎨 Converted existing image to file:', {
        fileName,
        size: file.size,
        type: file.type
      })

      // Set the file and open editor
      setSelectedFile(file)
      setShowImageEditor(true)
    } catch (error) {
      console.error('❌ CategoryImageUpload: Error loading existing image for edit:', error)
    } finally {
      setIsUploading(false)
    }
  }, [imageUrl])

  // Handle delete image
  const handleDeleteImage = useCallback(async () => {
    if (!imageUrl) return

    console.log('🗑️ CategoryImageUpload: Deleting image', imageUrl)
    setIsUploading(true)

    try {
      // Delete from server
      await deleteCategoryImage(imageUrl)

      // Clear from form state
      onImageChange(null)

      console.log('✅ CategoryImageUpload: Image deleted successfully')
    } catch (error) {
      console.error('❌ CategoryImageUpload: Error deleting image:', error)
    } finally {
      setIsUploading(false)
    }
  }, [imageUrl, onImageChange])

  // Handle processed image from editor
  const handleProcessedImage = useCallback(async (processedBlob: Blob) => {
    console.log('🎨 CategoryImageUpload: handleProcessedImage called', {
      label,
      blobSize: processedBlob.size,
      blobType: processedBlob.type,
      selectedFileName: selectedFile?.name,
      timestamp: new Date().toISOString()
    })

    setIsUploading(true)

    try {
      // Convert blob to file
      const fileName = selectedFile?.name || 'edited-image.jpg'
      const processedFile = new File([processedBlob], fileName, {
        type: 'image/jpeg'
      })

      console.log('🎨 CategoryImageUpload: Created file from blob', {
        fileName: processedFile.name,
        fileSize: processedFile.size,
        fileType: processedFile.type
      })

      // Upload the processed image
      console.log('🎨 CategoryImageUpload: Starting upload...')
      const uploadResults = await uploadCategoryImages([processedFile])

      console.log('🎨 CategoryImageUpload: Upload result:', uploadResults)

      if (uploadResults && uploadResults.length > 0) {
        const uploadedUrl = uploadResults[0].url
        console.log('✅ CategoryImageUpload: Upload successful, calling onImageChange', {
          uploadedUrl,
          uploadResult: uploadResults[0],
          onImageChangeType: typeof onImageChange
        })

        onImageChange(uploadedUrl)

        console.log('✅ CategoryImageUpload: onImageChange called, closing modal...')

        // Verify the callback worked by checking if imageUrl prop will update
        setTimeout(() => {
          console.log('🔍 CategoryImageUpload: Checking imageUrl after onImageChange', {
            currentImageUrl: imageUrl,
            expectedUrl: uploadedUrl
          })
        }, 100)

        // Close the image editor modal and reset state
        setShowImageEditor(false)
        setSelectedFile(null)

        // Recreate file input for next use
        setFileInputKey(prev => prev + 1)

        console.log('✅ CategoryImageUpload: Modal closed and state reset')
      } else {
        console.error('❌ CategoryImageUpload: Upload failed - no results returned', {
          uploadResults,
          resultsLength: uploadResults?.length || 0
        })
      }
    } catch (error) {
      console.error('❌ CategoryImageUpload: Error in handleProcessedImage:', error)
      console.error('❌ Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      })
    } finally {
      setIsUploading(false)
      console.log('🎨 CategoryImageUpload: setIsUploading(false) called')
    }
  }, [selectedFile, label, onImageChange])

  const handleRemoveImage = async () => {
    if (!imageUrl) return

    try {
      // Extract filename from URL for deletion
      const filename = extractFilenameFromUrl(imageUrl)
      if (filename) {
        await deleteCategoryImage(filename)
      }
      onImageChange(null)

      // Recreate file input for next use
      setFileInputKey(prev => prev + 1)
    } catch (error) {
      console.error('Delete error:', error)
      // Still remove from UI even if server deletion fails
      onImageChange(null)
      setFileInputKey(prev => prev + 1)
    }
  }

  // Cleanup effect
  React.useEffect(() => {
    return () => {
      // Clear any pending timeouts on unmount
      if (triggerTimeoutRef.current) {
        clearTimeout(triggerTimeoutRef.current)
        triggerTimeoutRef.current = null
      }
    }
  }, [])

  // Reset file input when modal states change
  React.useEffect(() => {
    if (!showImageEditor && !showPreview) {
      // Small delay to ensure modal is fully closed
      const timeout = setTimeout(() => {
        setFileInputKey(prev => prev + 1)
      }, 200)

      return () => clearTimeout(timeout)
    }
  }, [showImageEditor, showPreview])

  // Debug logging for component state
  React.useEffect(() => {
    console.log('🎨 CategoryImageUpload render state:', {
      label,
      imageUrl: imageUrl ? imageUrl.substring(0, 50) + '...' : undefined,
      imageUrlType: typeof imageUrl,
      imageUrlLength: imageUrl?.length || 0,
      hasImageUrl: !!imageUrl,
      isUploading,
      showImageEditor,
      showPreview,
      fileInputKey,
      fileInputExists: !!fileInputRef.current,
      disabled,
      timestamp: new Date().toISOString()
    })
  })

  return (
    <div className={cn("space-y-4", className)}>
      {/* Current Image Preview */}
      {imageUrl && (
        <div className="relative group">
          <div className="relative w-full h-48 bg-gray-100 rounded-lg overflow-hidden border">
            <img
              src={imageUrl}
              alt="Category banner"
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
              <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex gap-2">
                <Button
                  type="button"
                  variant="secondary"
                  size="sm"
                  onClick={() => setShowPreview(true)}
                  className="bg-white/90 hover:bg-white"
                >
                  <Eye className="h-4 w-4" />
                </Button>
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  onClick={handleRemoveImage}
                  disabled={disabled}
                  className="bg-red-500/90 hover:bg-red-500"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}



      {/* Hidden File Input - Key forces recreation to prevent browser state issues */}
      <input
        key={fileInputKey}
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        disabled={disabled || isUploading}
        style={{ display: 'none' }}
        multiple={false}
      />

      {/* Upload Area */}
      {!imageUrl && (
        <div
          className={cn(
            "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
            isDragging ? "border-blue-500 bg-blue-50" : "border-gray-300",
            disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer hover:border-gray-400",
            isUploading && "pointer-events-none"
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={(e) => {
            e.preventDefault()
            e.stopPropagation()
            console.log('🔥 Upload area clicked', {
              disabled,
              isUploading,
              showImageEditor,
              fileInputExists: !!fileInputRef.current,
              timestamp: new Date().toISOString()
            })
            triggerFileInput()
          }}
        >
          <div className="flex flex-col items-center justify-center space-y-2">
            {isUploading ? (
              <>
                <Loader2 className="h-8 w-8 text-blue-500 animate-spin" />
                <p className="text-sm text-gray-600">Yükleniyor...</p>
              </>
            ) : (
              <>
                <Upload className="h-8 w-8 text-gray-400" />
                <p className="text-sm text-gray-600">{placeholder}</p>
                <p className="text-xs text-gray-400">
                  PNG, JPG, WEBP (Max 5MB)
                </p>
                {/* Debug fallback button - only show in development */}
                {process.env.NODE_ENV === 'development' && (
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={(e) => {
                      e.stopPropagation()
                      console.log('🔧 Debug: Force recreating file input')
                      setFileInputKey(prev => prev + 1)
                      setTimeout(() => {
                        console.log('🔧 Debug: Attempting file input trigger after recreation')
                        triggerFileInput()
                      }, 150)
                    }}
                  >
                    🔧 Debug: Reset File Input
                  </Button>
                )}
              </>
            )}
          </div>
        </div>
      )}

      {/* Action Buttons for Uploaded Image */}
      {imageUrl && !isUploading && (
        <div className="grid grid-cols-2 gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={triggerFileInput}
            disabled={disabled}
          >
            <Upload className="h-4 w-4 mr-2" />
            Yeni Resim
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={handleEditImage}
            disabled={disabled}
          >
            <Edit className="h-4 w-4 mr-2" />
            Düzenle
          </Button>
        </div>
      )}

      {/* Preview Modal */}
      {showPreview && imageUrl && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50"
          onClick={() => setShowPreview(false)}
        >
          <div className="max-w-4xl max-h-[90vh] p-4">
            <img
              src={imageUrl}
              alt="Category banner preview"
              className="max-w-full max-h-full object-contain rounded-lg"
            />
          </div>
        </div>
      )}

      {/* Image Editor Modal */}
      <ImageEditorModal
        open={showImageEditor}
        onOpenChange={(open) => {
          console.log('🎨 ImageEditorModal onOpenChange called:', open)
          setShowImageEditor(open)
        }}
        imageFile={selectedFile}
        onSave={handleProcessedImage}
        targetDimensions={{ width: 400, height: 300 }}
      />
    </div>
  )
}
