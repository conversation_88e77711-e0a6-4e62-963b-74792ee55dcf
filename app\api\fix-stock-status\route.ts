import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    console.log('🔧 Adding missing stockStatus column...')

    // Add stockStatus column
    await prisma.$executeRaw`
      ALTER TABLE "products" ADD COLUMN IF NOT EXISTS "stockStatus" TEXT DEFAULT 'IN_STOCK';
    `
    console.log('✅ stockStatus column added')

    // Update stockStatus based on current stock levels
    await prisma.$executeRaw`
      UPDATE "products"
      SET "stockStatus" = CASE
        WHEN "stockQuantity" <= 0 THEN 'OUT_OF_STOCK'
        WHEN "stockQuantity" <= "minStockThreshold" THEN 'LOW_STOCK'
        ELSE 'IN_STOCK'
      END;
    `
    console.log('✅ stockStatus values updated')

    // Test the fix
    const testProduct = await prisma.product.findFirst({
      select: {
        id: true,
        name: true,
        stockQuantity: true,
        minStockThreshold: true,
        stockStatus: true
      }
    })

    console.log('✅ Test successful:', testProduct)

    return NextResponse.json({
      success: true,
      message: 'stockStatus column added and products API should work now',
      testProduct: testProduct
    })

  } catch (error: any) {
    console.error('❌ Fix failed:', error)
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 })
  }
}
