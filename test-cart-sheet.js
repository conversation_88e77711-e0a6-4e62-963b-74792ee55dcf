// Simple Cart Sheet Test
async function testCartSheet() {
  console.log('🛒 Testing Cart Sheet Component...\n')
  
  try {
    // Test homepage to see if cart sheet loads without errors
    console.log('1. Testing Homepage with Cart Sheet...')
    const response = await fetch('http://localhost:3000/')
    
    if (response.ok) {
      console.log('✅ Homepage loads successfully')
      console.log('   Status:', response.status)
      console.log('   Content-Type:', response.headers.get('content-type'))
    } else {
      console.log('❌ Homepage failed to load:', response.status)
      return
    }
    
    // Test products page (where cart is commonly used)
    console.log('\n2. Testing Products Page...')
    const productsResponse = await fetch('http://localhost:3000/urunler')
    
    if (productsResponse.ok) {
      console.log('✅ Products page loads successfully')
    } else {
      console.log('❌ Products page failed to load:', productsResponse.status)
    }
    
    // Test cart API endpoint
    console.log('\n3. Testing Cart API...')
    const cartResponse = await fetch('http://localhost:3000/api/cart')
    
    if (cartResponse.ok) {
      const cartData = await cartResponse.json()
      console.log('✅ Cart API responds successfully')
      console.log('   Cart items:', cartData.items?.length || 0)
      console.log('   Total amount:', cartData.totalAmount || 0)
    } else {
      console.log('❌ Cart API failed:', cartResponse.status)
    }
    
    console.log('\n🎉 Cart Sheet test completed!')
    console.log('\n💡 To test the UI:')
    console.log('   1. Open http://localhost:3000')
    console.log('   2. Click the cart icon in the header')
    console.log('   3. Verify the cart sheet opens without errors')
    console.log('   4. Check that empty cart message displays correctly')
    console.log('   5. Add a product and verify cart items display properly')
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  }
}

testCartSheet()
