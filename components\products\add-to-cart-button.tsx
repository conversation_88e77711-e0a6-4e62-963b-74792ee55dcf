"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ShoppingCart, Plus, Minus, Check } from "lucide-react"
import { useCartStore } from "@/lib/stores/cart-store"
import { toast } from "sonner"
import type { Product } from "@/types"

interface AddToCartButtonProps {
  product: Product
  quantity?: number
  className?: string
  showQuantityControls?: boolean
  variant?: "default" | "outline" | "secondary"
  size?: "default" | "sm" | "lg"
}

export function AddToCartButton({
  product,
  quantity = 1,
  className = "",
  showQuantityControls = false,
  variant = "default",
  size = "default",
}: AddToCartButtonProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [localQuantity, setLocalQuantity] = useState(quantity)
  const { addItem, updateQuantity, removeItem, getItems, isLoading: cartLoading } = useCartStore()

  // Check if product is in cart
  const items = getItems()
  const cartItem = items.find((i) => i.id === product.id)
  const isInCart = !!cartItem
  const cartQuantity = cartItem?.quantity || 0

  const handleAddToCart = async () => {
    if (product.stockStatus === "out_of_stock") {
      toast.error("Bu ürün şu anda stokta bulunmuyor")
      return
    }

    setIsLoading(true)

    try {
      // Use enhanced cart store with real-time stock validation
      const success = await addItem(product.id, localQuantity)

      if (success) {
        // Reset local quantity after successful add
        setLocalQuantity(1)
      }
    } catch (error) {
      console.error('Add to cart error:', error)
      toast.error("Ürün sepete eklenirken hata oluştu")
    } finally {
      setIsLoading(false)
    }
  }

  const handleUpdateQuantity = async (newQuantity: number) => {
    if (newQuantity <= 0) {
      handleRemoveFromCart()
      return
    }

    if (product.stockQuantity && newQuantity > product.stockQuantity) {
      toast.error(`Maksimum ${product.stockQuantity} adet ekleyebilirsiniz`)
      return
    }

    setIsLoading(true)

    try {
      await new Promise((resolve) => setTimeout(resolve, 300))

      updateQuantity(product.id, newQuantity)

      toast.success("Sepet güncellendi", {
        description: `${newQuantity} adet`,
      })
    } catch (error) {
      toast.error("Sepet güncellenirken hata oluştu")
    } finally {
      setIsLoading(false)
    }
  }

  const handleRemoveFromCart = async () => {
    setIsLoading(true)

    try {
      await new Promise((resolve) => setTimeout(resolve, 300))

      removeItem(product.id)

      toast.success("Ürün sepetten çıkarıldı")
    } catch (error) {
      toast.error("Ürün sepetten çıkarılırken hata oluştu")
    } finally {
      setIsLoading(false)
    }
  }

  // If product is in cart and we want to show quantity controls
  if (isInCart && showQuantityControls) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <Button
          variant="outline"
          size="icon"
          onClick={() => handleUpdateQuantity(cartQuantity - 1)}
          disabled={isLoading || cartQuantity <= 1}
          className="h-8 w-8"
        >
          <Minus className="w-3 h-3" />
        </Button>

        <span className="min-w-[2rem] text-center font-medium">{cartQuantity}</span>

        <Button
          variant="outline"
          size="icon"
          onClick={() => handleUpdateQuantity(cartQuantity + 1)}
          disabled={isLoading || (product.stockQuantity ? cartQuantity >= product.stockQuantity : false)}
          className="h-8 w-8"
        >
          <Plus className="w-3 h-3" />
        </Button>
      </div>
    )
  }

  // If product is in cart but we don't want quantity controls
  if (isInCart) {
    return (
      <Button
        variant="secondary"
        size={size}
        className={`${className} bg-blue-100 text-blue-800 hover:bg-blue-200`}
        disabled
      >
        <Check className="w-4 h-4 mr-2" />
        Sepette ({cartQuantity})
      </Button>
    )
  }

  // Default add to cart button
  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleAddToCart}
      disabled={isLoading || product.stockStatus === "out_of_stock"}
      className={className}
    >
      {isLoading ? (
        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
      ) : (
        <ShoppingCart className="w-4 h-4 mr-2" />
      )}
      {isLoading
        ? "Ekleniyor..."
        : product.stockStatus === "out_of_stock"
          ? "Stokta Yok"
          : isInCart
            ? "Daha Ekle"
            : "Sepete Ekle"}
    </Button>
  )
}
