/**
 * SEO Helper Functions for Product Management
 * Comprehensive SEO optimization utilities for Turkish e-commerce
 */

// Turkish stop words for keyword analysis
const TURKISH_STOP_WORDS = [
  'bir', 'bu', 'da', 'de', 've', 'ki', 'mi', 'mu', 'mü', 'ile', 'için', 'olan', 'olan',
  'her', 'hiç', 'çok', 'daha', 'en', 'gibi', 'kadar', 'sonra', 'önce', 'şu', 'o', 'ben',
  'sen', 'biz', 'siz', 'onlar', 'kendi', 'hangi', 'nasıl', 'neden', 'nerede', 'ne', 'kim'
]

// Turkish safety equipment keywords for better SEO
const SAFETY_KEYWORDS = {
  'baş koruma': ['güven<PERSON> bareti', 'kask', 'baret', 'baş koruyucu', 'i<PERSON> gü<PERSON>ğ<PERSON> kaskı'],
  'göz koruma': ['güven<PERSON> gözlüğü', 'koruyucu gözlük', 'i<PERSON> gözlüğü', 'göz koruyucu'],
  'kulak koruma': ['kulaklık', 'kulak tıkacı', 'gürültü koruma', 'işitme koruyucu'],
  'solunum koruma': ['maske', 'respiratör', 'solunum maskesi', 'toz maskesi'],
  'el koruma': ['iş eldiveni', 'koruyucu eldiven', 'güvenlik eldiveni', 'el koruyucu'],
  'ayak koruma': ['güvenlik ayakkabısı', 'iş botu', 'çelik burunlu', 'koruyucu ayakkabı'],
  'vücut koruma': ['iş tulumu', 'koruyucu kıyafet', 'güvenlik kıyafeti', 'reflektörlü yelek'],
  'yüksekte çalışma': ['emniyet kemeri', 'güvenlik halatı', 'yüksek çalışma ekipmanı']
}

export interface SEOAnalysis {
  score: number
  issues: string[]
  suggestions: string[]
  keywordDensity: number
  readabilityScore: number
}

export interface SEOFieldValues {
  seoTitle?: string
  seoDescription?: string
  metaKeywords?: string[]
  focusKeyword?: string
  canonicalUrl?: string
  robotsDirective?: string
  ogTitle?: string
  ogDescription?: string
  ogImage?: string
  twitterTitle?: string
  twitterDescription?: string
  twitterImage?: string
  alternativeText?: string
  breadcrumbs?: string[]
}

/**
 * Generate automatic SEO values based on product information
 */
export function generateAutoSEO(productData: {
  name: string
  brand?: string
  category?: string
  description?: string
  basePrice?: number
}): SEOFieldValues {
  const { name, brand, category, description, basePrice } = productData
  
  // Generate SEO title (max 60 chars)
  const brandPrefix = brand ? `${brand} ` : ''
  const priceInfo = basePrice ? ` - ${basePrice} TL` : ''
  const seoTitle = `${brandPrefix}${name}${priceInfo} | İş Güvenliği`.substring(0, 60)
  
  // Generate SEO description (max 160 chars)
  const categoryInfo = category ? ` ${category} kategorisinde` : ''
  const brandInfo = brand ? ` ${brand} markası` : ''
  const seoDescription = `${name}${brandInfo}${categoryInfo}. Kaliteli iş güvenliği ekipmanları, uygun fiyat ve hızlı teslimat. Güvenilir koruma çözümleri.`.substring(0, 160)
  
  // Generate focus keyword
  const focusKeyword = `${brand ? brand + ' ' : ''}${name}`.toLowerCase()
  
  // Generate meta keywords
  const metaKeywords = generateMetaKeywords(name, brand, category)
  
  // Generate Open Graph data
  const ogTitle = seoTitle
  const ogDescription = seoDescription
  
  // Generate Twitter data
  const twitterTitle = seoTitle
  const twitterDescription = seoDescription
  
  // Generate canonical URL
  const slug = generateSlug(name)
  const canonicalUrl = `/urun/${slug}`
  
  // Generate breadcrumbs
  const breadcrumbs = ['Ana Sayfa', category || 'Ürünler', name]
  
  // Generate alternative text for images
  const alternativeText = `${name} - ${brand || ''} ${category || 'İş Güvenliği Ekipmanı'}`.trim()
  
  return {
    seoTitle,
    seoDescription,
    metaKeywords,
    focusKeyword,
    canonicalUrl,
    robotsDirective: 'index,follow',
    ogTitle,
    ogDescription,
    twitterTitle,
    twitterDescription,
    alternativeText,
    breadcrumbs
  }
}

/**
 * Generate meta keywords based on product information
 */
function generateMetaKeywords(name: string, brand?: string, category?: string): string[] {
  const keywords = new Set<string>()
  
  // Add product name variations
  keywords.add(name.toLowerCase())
  
  // Add brand if available
  if (brand) {
    keywords.add(brand.toLowerCase())
    keywords.add(`${brand.toLowerCase()} ${name.toLowerCase()}`)
  }
  
  // Add category-specific keywords
  if (category) {
    const categoryLower = category.toLowerCase()
    keywords.add(categoryLower)
    
    // Add related safety keywords
    Object.entries(SAFETY_KEYWORDS).forEach(([key, values]) => {
      if (categoryLower.includes(key)) {
        values.forEach(keyword => keywords.add(keyword))
      }
    })
  }
  
  // Add general safety keywords
  keywords.add('iş güvenliği')
  keywords.add('güvenlik ekipmanı')
  keywords.add('koruyucu ekipman')
  
  return Array.from(keywords).slice(0, 10) // Limit to 10 keywords
}

/**
 * Generate URL-friendly slug
 */
function generateSlug(text: string): string {
  const turkishChars: Record<string, string> = {
    'ç': 'c', 'ğ': 'g', 'ı': 'i', 'ö': 'o', 'ş': 's', 'ü': 'u',
    'Ç': 'c', 'Ğ': 'g', 'I': 'i', 'İ': 'i', 'Ö': 'o', 'Ş': 's', 'Ü': 'u'
  }
  
  return text
    .toLowerCase()
    .replace(/[çğıöşüÇĞIİÖŞÜ]/g, char => turkishChars[char] || char)
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim()
}

/**
 * Calculate SEO score based on filled fields and content quality
 */
export function calculateSEOScore(data: SEOFieldValues & { description?: string }): SEOAnalysis {
  let score = 0
  const issues: string[] = []
  const suggestions: string[] = []
  
  // SEO Title (20 points)
  if (data.seoTitle) {
    if (data.seoTitle.length >= 30 && data.seoTitle.length <= 60) {
      score += 20
    } else if (data.seoTitle.length > 0) {
      score += 10
      if (data.seoTitle.length < 30) {
        issues.push('SEO başlığı çok kısa (minimum 30 karakter önerilir)')
      } else {
        issues.push('SEO başlığı çok uzun (maksimum 60 karakter)')
      }
    }
  } else {
    issues.push('SEO başlığı eksik')
    suggestions.push('Ürün adı ve marka içeren bir SEO başlığı ekleyin')
  }
  
  // SEO Description (20 points)
  if (data.seoDescription) {
    if (data.seoDescription.length >= 120 && data.seoDescription.length <= 160) {
      score += 20
    } else if (data.seoDescription.length > 0) {
      score += 10
      if (data.seoDescription.length < 120) {
        issues.push('SEO açıklaması çok kısa (minimum 120 karakter önerilir)')
      } else {
        issues.push('SEO açıklaması çok uzun (maksimum 160 karakter)')
      }
    }
  } else {
    issues.push('SEO açıklaması eksik')
    suggestions.push('Ürünü tanıtan özlü bir açıklama ekleyin')
  }
  
  // Focus Keyword (15 points)
  if (data.focusKeyword) {
    score += 15
    
    // Check if focus keyword is in title and description
    const keyword = data.focusKeyword.toLowerCase()
    if (data.seoTitle && !data.seoTitle.toLowerCase().includes(keyword)) {
      suggestions.push('Odak anahtar kelimeyi SEO başlığına ekleyin')
    }
    if (data.seoDescription && !data.seoDescription.toLowerCase().includes(keyword)) {
      suggestions.push('Odak anahtar kelimeyi SEO açıklamasına ekleyin')
    }
  } else {
    issues.push('Odak anahtar kelime eksik')
    suggestions.push('Ana anahtar kelimeyi belirleyin')
  }
  
  // Meta Keywords (10 points)
  if (data.metaKeywords && data.metaKeywords.length > 0) {
    score += 10
  } else {
    suggestions.push('Meta anahtar kelimeler ekleyin')
  }
  
  // Open Graph (15 points)
  if (data.ogTitle && data.ogDescription) {
    score += 15
  } else {
    suggestions.push('Open Graph verilerini tamamlayın')
  }
  
  // Twitter Card (10 points)
  if (data.twitterTitle && data.twitterDescription) {
    score += 10
  } else {
    suggestions.push('Twitter Card verilerini ekleyin')
  }
  
  // Canonical URL (5 points)
  if (data.canonicalUrl) {
    score += 5
  }
  
  // Alternative Text (5 points)
  if (data.alternativeText) {
    score += 5
  } else {
    suggestions.push('Görseller için alternatif metin ekleyin')
  }
  
  // Calculate keyword density and readability
  const keywordDensity = calculateKeywordDensity(data.description || '', data.focusKeyword || '')
  const readabilityScore = calculateReadabilityScore(data.description || '')
  
  return {
    score: Math.min(score, 100),
    issues,
    suggestions,
    keywordDensity,
    readabilityScore
  }
}

/**
 * Calculate keyword density in content
 */
function calculateKeywordDensity(content: string, keyword: string): number {
  if (!content || !keyword) return 0
  
  const words = content.toLowerCase().split(/\s+/)
  const keywordWords = keyword.toLowerCase().split(/\s+/)
  const keywordCount = words.filter(word => keywordWords.includes(word)).length
  
  return words.length > 0 ? (keywordCount / words.length) * 100 : 0
}

/**
 * Calculate readability score (simplified Turkish readability)
 */
function calculateReadabilityScore(content: string): number {
  if (!content) return 0
  
  const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0)
  const words = content.split(/\s+/).filter(w => w.length > 0)
  const syllables = words.reduce((count, word) => count + countSyllables(word), 0)
  
  if (sentences.length === 0 || words.length === 0) return 0
  
  const avgWordsPerSentence = words.length / sentences.length
  const avgSyllablesPerWord = syllables / words.length
  
  // Simplified readability formula for Turkish
  const score = 100 - (avgWordsPerSentence * 1.5) - (avgSyllablesPerWord * 10)
  
  return Math.max(0, Math.min(100, score))
}

/**
 * Count syllables in a Turkish word (simplified)
 */
function countSyllables(word: string): number {
  const vowels = 'aeiouıöüAEIOUIÖÜ'
  let count = 0
  let prevWasVowel = false
  
  for (const char of word) {
    const isVowel = vowels.includes(char)
    if (isVowel && !prevWasVowel) {
      count++
    }
    prevWasVowel = isVowel
  }
  
  return Math.max(1, count)
}

/**
 * Generate structured data for product
 */
export function generateStructuredData(productData: {
  name: string
  brand?: string
  description?: string
  basePrice?: number
  category?: string
  images?: string[]
  availability?: 'InStock' | 'OutOfStock' | 'PreOrder'
}): Record<string, any> {
  const { name, brand, description, basePrice, category, images, availability = 'InStock' } = productData
  
  return {
    "@context": "https://schema.org/",
    "@type": "Product",
    "name": name,
    "brand": brand ? {
      "@type": "Brand",
      "name": brand
    } : undefined,
    "description": description,
    "category": category,
    "image": images || [],
    "offers": {
      "@type": "Offer",
      "price": basePrice,
      "priceCurrency": "TRY",
      "availability": `https://schema.org/${availability}`,
      "seller": {
        "@type": "Organization",
        "name": "İş Güvenliği Mağazası"
      }
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.5",
      "reviewCount": "10"
    }
  }
}
