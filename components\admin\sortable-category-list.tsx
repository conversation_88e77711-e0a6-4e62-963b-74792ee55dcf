"use client"

import { useState } from "react"
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  type DragEndEvent,
} from "@dnd-kit/core"
import { arrayMove, SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from "@dnd-kit/sortable"
import { restrictToVerticalAxis, restrictToWindowEdges } from "@dnd-kit/modifiers"
import { SortableCategoryItem } from "./sortable-category-item"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Save, RotateCcw, GripVertical } from "lucide-react"
import { CategoryService } from "@/lib/api/categories"
import type { Category } from "@/types"

interface SortableCategoryListProps {
  categories: Category[]
  onOrderUpdate: () => void
  onEdit: (category: Category) => void
  onDelete: (category: Category) => void
}

export function SortableCategoryList({ categories, onOrderUpdate, onEdit, onDelete }: SortableCategoryListProps) {
  const [items, setItems] = useState(categories)
  const [hasChanges, setHasChanges] = useState(false)
  const [saving, setSaving] = useState(false)

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  )

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event

    if (over && active.id !== over.id) {
      const oldIndex = items.findIndex((item) => item.id === active.id)
      const newIndex = items.findIndex((item) => item.id === over.id)

      const newItems = arrayMove(items, oldIndex, newIndex)

      // Update sort order based on new positions
      const updatedItems = newItems.map((item, index) => ({
        ...item,
        sortOrder: index + 1,
      }))

      setItems(updatedItems)
      setHasChanges(true)
    }
  }

  const handleSaveOrder = async () => {
    try {
      setSaving(true)

      // Prepare data for API call
      const orderUpdates = items.map((item, index) => ({
        id: item.id,
        sortOrder: index + 1,
      }))

      // Call API to update order
      await CategoryService.updateCategoryOrder(orderUpdates)

      setHasChanges(false)
      onOrderUpdate()
    } catch (error) {
      console.error("Error saving category order:", error)
    } finally {
      setSaving(false)
    }
  }

  const handleResetOrder = () => {
    setItems(categories)
    setHasChanges(false)
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <GripVertical className="h-5 w-5 text-gray-400" />
              Kategori Sıralaması
            </CardTitle>
            <p className="text-sm text-gray-600 mt-1">Kategorileri sürükleyip bırakarak sırasını değiştirin</p>
          </div>

          {hasChanges && (
            <div className="flex items-center gap-2">
              <Badge variant="warning" className="bg-yellow-100 text-yellow-800">
                Kaydedilmemiş değişiklikler
              </Badge>
              <Button variant="outline" size="sm" onClick={handleResetOrder} disabled={saving}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Sıfırla
              </Button>
              <Button
                size="sm"
                onClick={handleSaveOrder}
                disabled={saving}
                className="bg-orange-500 hover:bg-orange-600"
              >
                {saving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Kaydediliyor...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Sıralamayı Kaydet
                  </>
                )}
              </Button>
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent>
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
          modifiers={[restrictToVerticalAxis, restrictToWindowEdges]}
        >
          <SortableContext items={items.map((item) => item.id)} strategy={verticalListSortingStrategy}>
            <div className="space-y-2">
              {items.map((category, index) => (
                <SortableCategoryItem
                  key={category.id}
                  category={category}
                  index={index}
                  onEdit={onEdit}
                  onDelete={onDelete}
                />
              ))}
            </div>
          </SortableContext>
        </DndContext>

        {items.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <GripVertical className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>Sıralanacak kategori bulunamadı</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
