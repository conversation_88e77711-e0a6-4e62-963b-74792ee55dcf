"use client"

import { useState, useCallback } from "react"
import { useRouter, usePathname, useSearchParams } from "next/navigation"

interface UseProductFiltersProps {
  initialFilters?: {
    search?: string
    category?: string
    brand?: string
    minPrice?: string
    maxPrice?: string
    inStock?: boolean
    sort?: string
    order?: string
    page?: number
  }
}

export function useProductFilters({ initialFilters = {} }: UseProductFiltersProps = {}) {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()

  // State for filter values
  const [filters, setFilters] = useState({
    search: initialFilters.search || "",
    category: initialFilters.category || "",
    brand: initialFilters.brand || "",
    minPrice: initialFilters.minPrice || "",
    maxPrice: initialFilters.maxPrice || "",
    inStock: initialFilters.inStock || false,
    sort: initialFilters.sort || "popularity",
    order: initialFilters.order || "desc",
    page: initialFilters.page || 1,
  })

  // Update a single filter
  const updateFilter = useCallback((key: string, value: string | number | boolean) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
      // Reset page when any filter changes
      page: key === "page" ? value : 1,
    }))
  }, [])

  // Apply all filters to URL
  const applyFilters = useCallback(() => {
    const params = new URLSearchParams()

    // Add non-empty filters to URL
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== "" && value !== false && value !== undefined && value !== null) {
        params.set(key, String(value))
      }
    })

    router.push(`${pathname}?${params.toString()}`)
  }, [filters, pathname, router])

  // Reset all filters
  const resetFilters = useCallback(() => {
    setFilters({
      search: "",
      category: "",
      brand: "",
      minPrice: "",
      maxPrice: "",
      inStock: false,
      sort: "popularity",
      order: "desc",
      page: 1,
    })

    router.push(pathname)
  }, [pathname, router])

  return {
    filters,
    updateFilter,
    applyFilters,
    resetFilters,
  }
}
