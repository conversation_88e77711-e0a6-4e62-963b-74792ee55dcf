import { create } from "zustand"
import { persist } from "zustand/middleware"
import { CartManager, type EnhancedCartItem, type CartSummary } from '@/lib/services/CartManager'
import { toast } from "sonner"

// Legacy interface for backward compatibility
export interface CartItem {
  id: string
  name: string
  price: number
  quantity: number
  image: string
  slug: string
}

// Enhanced cart state
interface CartState {
  // UI state
  isOpen: boolean
  isLoading: boolean

  // Cart data
  sessionId: string | null
  cartSummary: CartSummary | null
  lastUpdated: number

  // Error handling
  error: string | null
  stockIssues: CartSummary['stockIssues']
}

interface CartActions {
  // UI actions
  setIsOpen: (isOpen: boolean) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void

  // Cart management
  initializeSession: () => string
  refreshCart: () => Promise<void>
  addItem: (productId: string, quantity?: number) => Promise<boolean>
  updateQuantity: (cartItemId: string, quantity: number) => Promise<boolean>
  removeItem: (cartItemId: string) => Promise<boolean>
  clearCart: () => Promise<void>

  // Getters (for backward compatibility)
  getTotalItems: () => number
  getTotalPrice: () => number
  getItems: () => CartItem[] // Legacy format

  // Legacy property for direct access
  items: CartItem[]
}

interface CartStore extends CartState, CartActions {}

// Generate session ID
const generateSessionId = (): string => {
  return `cart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

export const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      // Initial state
      isOpen: false,
      isLoading: false,
      sessionId: null,
      cartSummary: null,
      lastUpdated: 0,
      error: null,
      stockIssues: [],

      // Computed property for backward compatibility
      items: [],

      // UI actions
      setIsOpen: (isOpen) => set({ isOpen }),
      setLoading: (loading) => set({ isLoading: loading }),
      setError: (error) => set({ error }),

      // Initialize session
      initializeSession: () => {
        const state = get()
        if (!state.sessionId) {
          const newSessionId = generateSessionId()
          set({ sessionId: newSessionId })
          return newSessionId
        }
        return state.sessionId
      },

      // Refresh cart from server
      refreshCart: async () => {
        const state = get()
        if (!state.sessionId) return

        try {
          set({ isLoading: true, error: null })

          const response = await fetch(`/api/cart?sessionId=${state.sessionId}`)
          const result = await response.json()

          if (result.success) {
            // Update items array for backward compatibility
            const items = result.data?.items ? result.data.items.map((item: any) => ({
              id: item.productId,
              name: item.product.name,
              price: item.currentPrice,
              quantity: item.quantity,
              image: item.product.images?.[0]?.url || '',
              slug: item.product.slug,
              product: {
                id: item.productId,
                name: item.product.name,
                price: item.currentPrice,
                slug: item.product.slug,
                images: item.product.images || [],
                stock: item.product.stock || 999,
                brand: item.product.brand || null,
                originalPrice: item.originalPrice
              }
            })) : []

            set({
              cartSummary: result.data,
              items: items,
              stockIssues: result.data.stockIssues || [],
              lastUpdated: Date.now(),
              error: null
            })
          } else {
            set({ error: result.error })
          }
        } catch (error) {
          console.error('Refresh cart error:', error)
          set({ error: 'Failed to refresh cart' })
        } finally {
          set({ isLoading: false })
        }
      },

      // Add item to cart
      addItem: async (productId: string, quantity: number = 1) => {
        const state = get()
        const sessionId = state.sessionId || get().initializeSession()

        try {
          set({ isLoading: true, error: null })

          const response = await fetch('/api/cart', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              sessionId,
              productId,
              quantity
            })
          })

          const result = await response.json()

          if (result.success) {
            await get().refreshCart()
            toast.success('Ürün sepete eklendi')
            return true
          } else {
            set({ error: result.error })
            toast.error(result.error || 'Ürün sepete eklenemedi')
            return false
          }
        } catch (error) {
          console.error('Add item error:', error)
          const errorMsg = 'Ürün sepete eklenemedi'
          set({ error: errorMsg })
          toast.error(errorMsg)
          return false
        } finally {
          set({ isLoading: false })
        }
      },

      // Update item quantity
      updateQuantity: async (cartItemId: string, quantity: number) => {
        const state = get()
        if (!state.sessionId) return false

        try {
          set({ isLoading: true, error: null })

          const response = await fetch('/api/cart', {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              sessionId: state.sessionId,
              cartItemId,
              quantity
            })
          })

          const result = await response.json()

          if (result.success) {
            await get().refreshCart()
            return true
          } else {
            set({ error: result.error })
            toast.error(result.error || 'Miktar güncellenemedi')
            return false
          }
        } catch (error) {
          console.error('Update quantity error:', error)
          const errorMsg = 'Miktar güncellenemedi'
          set({ error: errorMsg })
          toast.error(errorMsg)
          return false
        } finally {
          set({ isLoading: false })
        }
      },

      // Remove item from cart
      removeItem: async (cartItemId: string) => {
        const state = get()
        if (!state.sessionId) return false

        try {
          set({ isLoading: true, error: null })

          const response = await fetch('/api/cart', {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              sessionId: state.sessionId,
              cartItemId
            })
          })

          const result = await response.json()

          if (result.success) {
            await get().refreshCart()
            toast.success('Ürün sepetten kaldırıldı')
            return true
          } else {
            set({ error: result.error })
            toast.error(result.error || 'Ürün kaldırılamadı')
            return false
          }
        } catch (error) {
          console.error('Remove item error:', error)
          const errorMsg = 'Ürün kaldırılamadı'
          set({ error: errorMsg })
          toast.error(errorMsg)
          return false
        } finally {
          set({ isLoading: false })
        }
      },

      // Clear cart
      clearCart: async () => {
        const state = get()
        if (!state.sessionId || !state.cartSummary?.items.length) return

        try {
          set({ isLoading: true, error: null })

          // Remove all items one by one
          for (const item of state.cartSummary.items) {
            await get().removeItem(item.id)
          }

          toast.success('Sepet temizlendi')
        } catch (error) {
          console.error('Clear cart error:', error)
          toast.error('Sepet temizlenemedi')
        } finally {
          set({ isLoading: false })
        }
      },

      // Legacy getters for backward compatibility
      getTotalItems: () => {
        const state = get()
        return state.cartSummary?.totalItems || 0
      },

      getTotalPrice: () => {
        const state = get()
        return state.cartSummary?.totalAmount || 0
      },

      getItems: () => {
        const state = get()
        if (!state.cartSummary?.items) return []

        // Convert to legacy format with enhanced compatibility
        return state.cartSummary.items.map(item => ({
          id: item.productId,
          name: item.product.name,
          price: item.currentPrice,
          quantity: item.quantity,
          image: item.product.images?.[0]?.url || '',
          slug: item.product.slug,
          // Include product object for new format compatibility
          product: {
            id: item.productId,
            name: item.product.name,
            price: item.currentPrice,
            slug: item.product.slug,
            images: item.product.images || [],
            stock: item.product.stock || 999,
            brand: item.product.brand || null,
            originalPrice: item.originalPrice
          }
        }))
      }
    }),
    {
      name: "enhanced-cart-storage",
      partialize: (state) => ({
        sessionId: state.sessionId,
        lastUpdated: state.lastUpdated
      })
    }
  )
)
