import { PrismaClient } from '@prisma/client'
import { mockCategories } from '../data/mock-data'

const prisma = new PrismaClient()

// Helper functions
function generateSKU(): string {
  return `SKU-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .trim()
}

function getRandomElement<T>(array: T[]): T {
  return array[Math.floor(Math.random() * array.length)]
}

function getRandomPrice(min: number, max: number): number {
  return Math.round((Math.random() * (max - min) + min) * 100) / 100
}

function getRandomInt(min: number, max: number): number {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

// Product data templates for each category
const productTemplates = {
  "1": { // Baş Koruma
    products: [
      {
        name: "3M H-700 Güvenlik Bareti",
        brand: "3M",
        model: "H-700",
        description: "Yüksek dayanıklılık ve konfor sunan güvenlik bareti. UV koruması ve ayarlanabilir kafa bandı ile uzun süreli kullanım için ideal.",
        shortDescription: "Profesyonel güvenlik bareti, UV korumalı",
        priceRange: { min: 45, max: 85 }
      },
      {
        name: "MSA V-Gard Endüstriyel Kask",
        brand: "MSA",
        model: "V-Gard",
        description: "Endüstriyel ortamlar için tasarlanmış dayanıklı güvenlik kaskı. Çarpma ve penetrasyon koruması sağlar.",
        shortDescription: "Endüstriyel güvenlik kaskı, yüksek dayanıklılık",
        priceRange: { min: 65, max: 120 }
      },
      {
        name: "Honeywell Fibre-Metal SuperEight",
        brand: "Honeywell",
        model: "SuperEight",
        description: "Termoset kompozit malzemeden üretilmiş premium güvenlik bareti. Elektriksel yalıtım özelliği.",
        shortDescription: "Premium güvenlik bareti, elektriksel yalıtım",
        priceRange: { min: 85, max: 150 }
      },
      {
        name: "Uvex Pheos B-WR Güvenlik Kaskı",
        brand: "Uvex",
        model: "Pheos B-WR",
        description: "Havalandırma sistemi ve yağmur oluğu ile donatılmış modern güvenlik kaskı. Ergonomik tasarım.",
        shortDescription: "Havalandırmalı güvenlik kaskı, ergonomik",
        priceRange: { min: 55, max: 95 }
      }
    ]
  },
  "2": { // Göz Koruma
    products: [
      {
        name: "3M SecureFit 400 Güvenlik Gözlüğü",
        brand: "3M",
        model: "SecureFit 400",
        description: "Anti-fog ve çizilme direnci olan güvenlik gözlüğü. Esnek çerçeve tasarımı ile mükemmel uyum.",
        shortDescription: "Anti-fog güvenlik gözlüğü, esnek çerçeve",
        priceRange: { min: 25, max: 45 }
      },
      {
        name: "Honeywell Uvex Genesis Gözlük",
        brand: "Honeywell",
        model: "Genesis",
        description: "Wraparound tasarım ile 180° koruma sağlayan güvenlik gözlüğü. UV koruması ve anti-fog kaplama.",
        shortDescription: "180° koruma, UV korumalı güvenlik gözlüğü",
        priceRange: { min: 35, max: 65 }
      },
      {
        name: "JSP Stealth 16g Güvenlik Gözlüğü",
        brand: "JSP",
        model: "Stealth 16g",
        description: "Ultra hafif güvenlik gözlüğü. Sadece 16 gram ağırlığında, tüm gün konforlu kullanım.",
        shortDescription: "Ultra hafif güvenlik gözlüğü, 16g",
        priceRange: { min: 30, max: 50 }
      },
      {
        name: "Bollé Safety Rush+ Gözlük",
        brand: "Bollé",
        model: "Rush+",
        description: "Spor tarzı güvenlik gözlüğü. Platinum anti-fog ve anti-çizik kaplama ile üstün performans.",
        shortDescription: "Spor tarzı güvenlik gözlüğü, platinum kaplama",
        priceRange: { min: 40, max: 70 }
      }
    ]
  },
  "3": { // Kulak Koruma
    products: [
      {
        name: "3M Peltor X5A Kulaklık",
        brand: "3M",
        model: "Peltor X5A",
        description: "Yüksek performanslı gürültü önleyici kulaklık. 37 dB SNR değeri ile maksimum koruma sağlar.",
        shortDescription: "37 dB SNR, maksimum gürültü koruması",
        priceRange: { min: 250, max: 350 }
      },
      {
        name: "Honeywell Howard Leight Impact",
        brand: "Honeywell",
        model: "Impact",
        description: "Elektronik kulaklık, çevresel sesleri amplifikatör ile artırır, zararlı sesleri bloke eder.",
        shortDescription: "Elektronik kulaklık, ses amplifikatörü",
        priceRange: { min: 180, max: 280 }
      },
      {
        name: "Moldex Pura-Fit Kulak Tıkacı",
        brand: "Moldex",
        model: "Pura-Fit",
        description: "Tek kullanımlık köpük kulak tıkacı. 36 dB NRR değeri ile yüksek koruma sağlar.",
        shortDescription: "Tek kullanımlık kulak tıkacı, 36 dB NRR",
        priceRange: { min: 0.5, max: 1.2 }
      },
      {
        name: "3M E-A-R Classic Kulak Tıkacı",
        brand: "3M",
        model: "E-A-R Classic",
        description: "Sarı köpük kulak tıkacı, kolay takılabilir ve yüksek konfor sağlar. 29 dB NRR.",
        shortDescription: "Sarı köpük kulak tıkacı, 29 dB NRR",
        priceRange: { min: 0.3, max: 0.8 }
      }
    ]
  }
}

// Continue with more categories...
const additionalTemplates = {
  "4": { // Solunum Koruma
    products: [
      {
        name: "3M 8210 N95 Maske",
        brand: "3M",
        model: "8210",
        description: "NIOSH onaylı N95 partikül filtreli maske. %95 filtrasyon verimliliği ile güvenilir koruma.",
        shortDescription: "N95 partikül filtreli maske, NIOSH onaylı",
        priceRange: { min: 3, max: 8 }
      },
      {
        name: "Honeywell North 7700 Yarım Yüz Maskesi",
        brand: "Honeywell",
        model: "North 7700",
        description: "Silikon yarım yüz maskesi, değiştirilebilir filtreler ile çok amaçlı kullanım.",
        shortDescription: "Silikon yarım yüz maskesi, değiştirilebilir filtre",
        priceRange: { min: 85, max: 150 }
      },
      {
        name: "MSA Advantage 900 Tam Yüz Maskesi",
        brand: "MSA",
        model: "Advantage 900",
        description: "Tam yüz koruması sağlayan respiratör maske. Geniş görüş alanı ve konforlu kullanım.",
        shortDescription: "Tam yüz respiratör maske, geniş görüş",
        priceRange: { min: 180, max: 300 }
      },
      {
        name: "Moldex 2400 N95 Maske",
        brand: "Moldex",
        model: "2400",
        description: "Ventilli N95 maske, nefes verme kolaylığı ve konfor sağlar. Tek kullanımlık.",
        shortDescription: "Ventilli N95 maske, tek kullanımlık",
        priceRange: { min: 4, max: 10 }
      }
    ]
  },
  "5": { // El Koruma
    products: [
      {
        name: "Ansell HyFlex 11-801 Eldiven",
        brand: "Ansell",
        model: "HyFlex 11-801",
        description: "Nitrile kaplı iş eldiveni. Mükemmel kavrama ve hassasiyet sağlar. Yağ direnci.",
        shortDescription: "Nitrile kaplı iş eldiveni, yağ dirençli",
        priceRange: { min: 8, max: 15 }
      },
      {
        name: "3M Comfort Grip Eldiven",
        brand: "3M",
        model: "Comfort Grip",
        description: "Lateks kaplı pamuklu eldiven. Nefes alabilir yapı ve üstün kavrama performansı.",
        shortDescription: "Lateks kaplı pamuklu eldiven, nefes alabilir",
        priceRange: { min: 5, max: 12 }
      },
      {
        name: "Honeywell Rig Dog CR Eldiven",
        brand: "Honeywell",
        model: "Rig Dog CR",
        description: "Kesim dirençli eldiven. ANSI A4 kesim koruması ile yüksek güvenlik sağlar.",
        shortDescription: "Kesim dirençli eldiven, ANSI A4 koruması",
        priceRange: { min: 25, max: 45 }
      },
      {
        name: "Uvex Rubiflex S NB27E Eldiven",
        brand: "Uvex",
        model: "Rubiflex S NB27E",
        description: "Kimyasal dirençli nitril eldiven. Asit ve baz koruması, uzun kullanım ömrü.",
        shortDescription: "Kimyasal dirençli nitril eldiven",
        priceRange: { min: 15, max: 28 }
      }
    ]
  },
  "6": { // Ayak Koruma
    products: [
      {
        name: "Caterpillar Holton S3 Bot",
        brand: "Caterpillar",
        model: "Holton S3",
        description: "Çelik burunlu güvenlik botu. Su geçirmez deri üst, anti-slip taban ile maksimum koruma.",
        shortDescription: "Çelik burunlu güvenlik botu, su geçirmez",
        priceRange: { min: 180, max: 280 }
      },
      {
        name: "Timberland PRO Titan S3 Ayakkabı",
        brand: "Timberland",
        model: "PRO Titan S3",
        description: "Kompozit burunlu güvenlik ayakkabısı. Metal detektör geçişi, hafif ve konforlu.",
        shortDescription: "Kompozit burunlu güvenlik ayakkabısı",
        priceRange: { min: 220, max: 350 }
      },
      {
        name: "Puma Safety Velocity 2.0 Low",
        brand: "Puma",
        model: "Velocity 2.0 Low",
        description: "Spor tarzı güvenlik ayakkabısı. Nefes alabilir mesh üst, ESD koruması.",
        shortDescription: "Spor tarzı güvenlik ayakkabısı, ESD korumalı",
        priceRange: { min: 150, max: 250 }
      },
      {
        name: "Uvex 1 Sport S1P Ayakkabı",
        brand: "Uvex",
        model: "1 Sport S1P",
        description: "Ultra hafif güvenlik ayakkabısı. Sadece 460g ağırlığında, tüm gün konfor.",
        shortDescription: "Ultra hafif güvenlik ayakkabısı, 460g",
        priceRange: { min: 120, max: 200 }
      }
    ]
  }
}

// Add remaining categories
const finalTemplates = {
  "7": { // Vücut Koruma
    products: [
      {
        name: "DuPont Tyvek 1422A Tulum",
        brand: "DuPont",
        model: "Tyvek 1422A",
        description: "Kimyasal koruyucu tulum. Sıvı kimyasallara karşı bariyer koruma, nefes alabilir.",
        shortDescription: "Kimyasal koruyucu tulum, nefes alabilir",
        priceRange: { min: 25, max: 45 }
      },
      {
        name: "3M 4515 Koruyucu Tulum",
        brand: "3M",
        model: "4515",
        description: "Tek kullanımlık koruyucu tulum. Partikül ve sınırlı sıvı sıçrama koruması.",
        shortDescription: "Tek kullanımlık koruyucu tulum",
        priceRange: { min: 15, max: 30 }
      },
      {
        name: "Honeywell Salisbury Arc Flash Takım",
        brand: "Honeywell",
        model: "Salisbury",
        description: "Elektrik ark korumalı iş kıyafeti. ATPV 40 cal/cm² koruma seviyesi.",
        shortDescription: "Elektrik ark korumalı iş kıyafeti",
        priceRange: { min: 350, max: 550 }
      },
      {
        name: "Kimberly-Clark A40 Tulum",
        brand: "Kimberly-Clark",
        model: "A40",
        description: "Sıvı ve partikül korumalı tulum. Rahat hareket imkanı, dayanıklı yapı.",
        shortDescription: "Sıvı ve partikül korumalı tulum",
        priceRange: { min: 20, max: 35 }
      }
    ]
  },
  "8": { // Yüksekte Çalışma
    products: [
      {
        name: "3M DBI-SALA ExoFit NEX Emniyet Kemeri",
        brand: "3M",
        model: "ExoFit NEX",
        description: "Tam vücut emniyet kemeri. Ergonomik tasarım ve kolay ayarlama sistemi.",
        shortDescription: "Tam vücut emniyet kemeri, ergonomik",
        priceRange: { min: 180, max: 280 }
      },
      {
        name: "Honeywell Miller Revolution Kemer",
        brand: "Honeywell",
        model: "Revolution",
        description: "Devrim niteliğinde emniyet kemeri. DualTech webbing teknolojisi ile üstün dayanıklılık.",
        shortDescription: "DualTech emniyet kemeri, üstün dayanıklılık",
        priceRange: { min: 220, max: 350 }
      },
      {
        name: "MSA Workman Emniyet Kemeri",
        brand: "MSA",
        model: "Workman",
        description: "Ekonomik tam vücut emniyet kemeri. Temel koruma ihtiyaçları için ideal.",
        shortDescription: "Ekonomik tam vücut emniyet kemeri",
        priceRange: { min: 120, max: 200 }
      },
      {
        name: "Petzl AVAO BOD Fast Kemer",
        brand: "Petzl",
        model: "AVAO BOD Fast",
        description: "Hızlı takılabilir emniyet kemeri. DoubleBack toka sistemi ile kolay kullanım.",
        shortDescription: "Hızlı takılabilir emniyet kemeri",
        priceRange: { min: 150, max: 250 }
      }
    ]
  }
}

// Merge all templates
Object.assign(productTemplates, additionalTemplates, finalTemplates)

// Sample discount data for testing
const sampleDiscounts = [
  {
    name: "Yaz İndirimi",
    type: "PERCENTAGE" as const,
    value: 15,
    isActive: true,
    startDate: new Date('2024-06-01').toISOString(),
    endDate: new Date('2024-08-31').toISOString(),
    usageLimit: null
  },
  {
    name: "Erken Kuş İndirimi",
    type: "PERCENTAGE" as const,
    value: 25,
    isActive: true,
    startDate: new Date('2024-01-01').toISOString(),
    endDate: new Date('2024-12-31').toISOString(),
    usageLimit: 100
  },
  {
    name: "50 TL İndirim",
    type: "FIXED_AMOUNT" as const,
    value: 50,
    isActive: true,
    startDate: new Date('2024-07-01').toISOString(),
    endDate: new Date('2024-07-31').toISOString(),
    usageLimit: 50
  }
]

// Stock scenarios for testing
const stockScenarios = [
  { name: "Yüksek Stok", stockQuantity: 150, minStockThreshold: 20, maxStockCapacity: 200 },
  { name: "Normal Stok", stockQuantity: 45, minStockThreshold: 10, maxStockCapacity: 100 },
  { name: "Düşük Stok", stockQuantity: 8, minStockThreshold: 15, maxStockCapacity: 50 },
  { name: "Kritik Stok", stockQuantity: 2, minStockThreshold: 10, maxStockCapacity: 30 },
  { name: "Stok Yok", stockQuantity: 0, minStockThreshold: 5, maxStockCapacity: 25 }
]

// Warehouse locations
const stockLocations = [
  "Depo A - Raf 1",
  "Depo A - Raf 2",
  "Depo B - Raf 1",
  "Depo C - Zemin Kat",
  "Ana Depo - Sektör 1",
  "Ana Depo - Sektör 2"
]

// SEO keywords for different categories
const seoKeywords = {
  "1": ["güvenlik bareti", "iş güvenliği", "baş koruma", "endüstriyel kask", "inşaat bareti"],
  "2": ["güvenlik gözlüğü", "koruyucu gözlük", "iş gözlüğü", "göz koruma", "anti-fog gözlük"],
  "3": ["kulaklık", "kulak koruma", "gürültü önleyici", "kulak tıkacı", "işitme koruması"],
  "4": ["maske", "respiratör", "solunum koruma", "N95 maske", "kimyasal maske"],
  "5": ["iş eldiveni", "koruyucu eldiven", "el koruma", "nitril eldiven", "kesim dirençli eldiven"],
  "6": ["güvenlik ayakkabısı", "iş botu", "çelik burunlu", "ayak koruma", "kompozit burun"],
  "7": ["koruyucu tulum", "iş kıyafeti", "kimyasal koruma", "vücut koruma", "tek kullanımlık tulum"],
  "8": ["emniyet kemeri", "yüksekte çalışma", "düşme koruması", "tam vücut kemeri", "güvenlik halatı"]
}

// Main seeding function
async function seedProducts() {
  console.log('🌱 Starting product seeding...')

  // Get existing categories from database
  const categories = await prisma.category.findMany({
    where: { isActive: true }
  })

  if (categories.length === 0) {
    console.log('❌ No categories found. Please run category seeding first.')
    return
  }

  let totalProductsCreated = 0

  for (const category of categories) {
    console.log(`\n📦 Creating products for category: ${category.name}`)

    // Find template for this category
    const template = productTemplates[category.id] || productTemplates[category.sortOrder?.toString()]

    if (!template) {
      console.log(`⚠️  No template found for category ${category.name}, skipping...`)
      continue
    }

    for (let i = 0; i < template.products.length; i++) {
      const productTemplate = template.products[i]

      // Generate pricing
      const costPrice = getRandomPrice(productTemplate.priceRange.min * 0.6, productTemplate.priceRange.min * 0.8)
      const basePrice = getRandomPrice(productTemplate.priceRange.min, productTemplate.priceRange.max)
      const zakatAmount = basePrice > 100 ? getRandomPrice(2, 8) : undefined

      // Select stock scenario
      const stockScenario = getRandomElement(stockScenarios)
      const stockLocation = getRandomElement(stockLocations)

      // Generate product data
      const productData = {
        name: productTemplate.name,
        slug: generateSlug(productTemplate.name),
        description: productTemplate.description,
        shortDescription: productTemplate.shortDescription,
        sku: generateSKU(),
        categoryId: category.id,
        brand: productTemplate.brand,
        model: productTemplate.model,

        // Enhanced pricing structure
        basePrice: basePrice,
        baseCostPrice: costPrice,
        zakatAmount: zakatAmount,
        taxRate: 20,
        currency: 'TRY',

        // Legacy fields for backward compatibility
        price: basePrice,
        costPrice: costPrice,

        // Stock management
        trackStock: true,
        stockQuantity: stockScenario.stockQuantity,
        minStockThreshold: stockScenario.minStockThreshold,
        maxStockCapacity: stockScenario.maxStockCapacity,
        stockLocation: stockLocation,
        reorderPoint: Math.max(5, Math.floor(stockScenario.minStockThreshold * 1.5)),
        reorderQuantity: Math.max(20, stockScenario.maxStockCapacity - stockScenario.minStockThreshold),

        // Legacy stock fields
        stock: stockScenario.stockQuantity,
        minStock: stockScenario.minStockThreshold,
        stockStatus: stockScenario.stockQuantity > stockScenario.minStockThreshold ? 'IN_STOCK' :
                    stockScenario.stockQuantity > 0 ? 'LOW_STOCK' : 'OUT_OF_STOCK',

        // Product status
        isActive: true,
        isFeatured: Math.random() < 0.3, // 30% chance of being featured
        isNew: Math.random() < 0.2, // 20% chance of being new
        isOnSale: Math.random() < 0.25, // 25% chance of being on sale

        // SEO fields
        seoTitle: `${productTemplate.name} - ${productTemplate.brand} | İş Güvenliği`,
        seoDescription: `${productTemplate.shortDescription}. ${productTemplate.brand} kalitesi ile güvenilir koruma. Hızlı teslimat ve uygun fiyat.`,
        metaKeywords: JSON.stringify(seoKeywords[category.id] || []),

        // Physical properties
        weight: getRandomPrice(0.1, 2.5),
        dimensions: JSON.stringify({
          length: getRandomInt(10, 50),
          width: getRandomInt(10, 40),
          height: getRandomInt(5, 30)
        }),

        publishedAt: new Date()
      }

      try {
        const product = await prisma.product.create({
          data: productData
        })

        // Add sample discount for some products (30% chance)
        if (Math.random() < 0.3) {
          const discount = getRandomElement(sampleDiscounts)
          await prisma.productDiscount.create({
            data: {
              productId: product.id,
              name: discount.name,
              type: discount.type,
              value: discount.value,
              isActive: discount.isActive,
              startDate: new Date(discount.startDate),
              endDate: new Date(discount.endDate),
              usageLimit: discount.usageLimit,
              usageCount: 0,
              clickCount: 0,
              conversionCount: 0,
              totalSavings: 0
            }
          })
          console.log(`  💰 Added discount "${discount.name}" to ${product.name}`)
        }

        totalProductsCreated++
        console.log(`  ✅ Created product: ${product.name} (Stock: ${stockScenario.name})`)

      } catch (error) {
        console.error(`  ❌ Error creating product ${productTemplate.name}:`, error)
      }
    }
  }

  // Update category product counts
  for (const category of categories) {
    const productCount = await prisma.product.count({
      where: { categoryId: category.id }
    })

    await prisma.category.update({
      where: { id: category.id },
      data: { productCount }
    })
  }

  console.log(`\n🎉 Product seeding completed! Created ${totalProductsCreated} products.`)
}

// Run the seeding
async function main() {
  try {
    await seedProducts()
  } catch (error) {
    console.error('❌ Error during seeding:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Export for use in other files
export { seedProducts }

// Run if called directly
if (require.main === module) {
  main()
    .catch((error) => {
      console.error(error)
      process.exit(1)
    })
}
