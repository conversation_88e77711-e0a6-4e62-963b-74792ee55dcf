import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

// GET /api/products/brands - Marka listesini getir
export async function GET() {
  try {
    // Get distinct brands from products
    const brands = await prisma.product.findMany({
      select: {
        brand: true,
      },
      distinct: ['brand'],
      where: {
        brand: {
          not: null,
        },
      },
      orderBy: {
        brand: 'asc',
      },
    })

    const brandNames = brands.map(product => product.brand).filter(<PERSON>olean)

    return NextResponse.json({
      success: true,
      data: brandNames,
    })
  } catch (error) {
    console.error("Brands GET error:", error)
    return NextResponse.json({ success: false, message: "Markalar yüklenirken hata olu<PERSON>tu" }, { status: 500 })
  }
}
