"use client"

import type React from "react"
import { useState, useEffect } from "react"
import {
  Loader2,
  Plus,
  Trash2,
  FileText,
  Ruler,
  Package,
  Tag,
  DollarSign,
  CheckCircle,
  Globe,
  ImageIcon as ImageIconLucide,
  CalendarIcon,
  Percent,
  TrendingDown,
  Clock,
  Users,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { useCreateProduct, useUpdateProduct } from "@/lib/hooks/use-products"
import { useMainCategories } from "@/lib/hooks/use-categories"
import { mockBrands, certificateTypes, specificationGroups } from "@/data/mock-data"
import type {
  Product,
  ProductFormData,
  ProductSpecification,
  ProductCertificate,
  ProductDimensions,
  ProductImage,
  ProductCreateInput,
  ProductUpdateInput,
} from "@/types"
import { Separator } from "@/components/ui/separator"
import { ImageUpload } from "@/components/ui/image-upload"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { format } from "date-fns"
import { tr } from "date-fns/locale"
import { ProductDiscountManager } from "./product-discount-manager"
import ProductService from "@/lib/services/ProductService"

interface ProductFormModalProps {
  open: boolean
  onClose: () => void
  product: Product | null
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

export function ProductFormModal({ open, onClose, product, onSuccess, onError }: ProductFormModalProps) {
  const isEditing = !!product
  
  const [formData, setFormData] = useState<ProductFormData>({
    name: "",
    description: "",
    shortDescription: "",
    categoryId: "",
    brand: "",
    model: "",
    price: 0,
    originalPrice: undefined,
    costPrice: undefined,
    taxRate: 20,
    trackStock: true,
    weight: undefined,
    dimensions: undefined,
    specifications: [],
    certificates: [],
    isActive: true,
    isFeatured: false,
    isNew: true,
    isOnSale: false,
    seoTitle: "",
    seoDescription: "",
    metaKeywords: [],
    images: [],
    discounts: [],
  })
  
  const [productImages, setProductImages] = useState<ProductImage[]>([])
  const { data: categoriesData } = useMainCategories()
  const { mutate: createProduct, loading: creating } = useCreateProduct()
  const { mutate: updateProduct, loading: updating } = useUpdateProduct()
  const loading = creating || updating
  const categories = categoriesData?.data || []

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const productDataToSubmit: ProductCreateInput | ProductUpdateInput = {
        ...formData,
        basePrice: Number(formData.price),
        originalPrice: formData.originalPrice ? Number(formData.originalPrice) : undefined,
        baseCostPrice: formData.costPrice ? Number(formData.costPrice) : undefined,
        taxRate: Number(formData.taxRate),
        weight: formData.weight ? Number(formData.weight) : undefined,
      } as ProductCreateInput | ProductUpdateInput

      if (isEditing) {
        await updateProduct({ id: product!.id, data: productDataToSubmit as ProductUpdateInput })
        onSuccess("Ürün başarıyla güncellendi!")
      } else {
        await createProduct(productDataToSubmit as ProductCreateInput)
        onSuccess("Ürün başarıyla oluşturuldu!")
      }
      onClose()
    } catch (error: any) {
      onError(error.message || "Bir hata oluştu.")
    }
  }

  const handleInputChange = (field: keyof ProductFormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEditing ? "Ürün Düzenle" : "Yeni Ürün Ekle"}</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="basic">Temel Bilgiler</TabsTrigger>
              <TabsTrigger value="pricing">Fiyat & Stok</TabsTrigger>
              <TabsTrigger value="discounts">İndirimler</TabsTrigger>
              <TabsTrigger value="media">Medya</TabsTrigger>
              <TabsTrigger value="seo">SEO</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Ürün Adı *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange("name", e.target.value)}
                    placeholder="Örn: 3M Peltor X5A Kulaklık"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="categoryId">Kategori *</Label>
                  <Select value={formData.categoryId} onValueChange={(value) => handleInputChange("categoryId", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Kategori seçin" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="description">Açıklama *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange("description", e.target.value)}
                  placeholder="Ürün açıklaması..."
                  rows={4}
                  required
                />
              </div>
            </TabsContent>

            <TabsContent value="pricing" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="price">Fiyat *</Label>
                  <Input
                    id="price"
                    type="number"
                    value={formData.price}
                    onChange={(e) => handleInputChange("price", Number.parseFloat(e.target.value) || 0)}
                    min="0"
                    step="0.01"
                    required
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="discounts" className="space-y-4">
              <ProductDiscountManager
                discounts={formData.discounts || []}
                onDiscountsChange={(discounts) => handleInputChange("discounts", discounts)}
                productPrice={formData.price}
              />
            </TabsContent>

            <TabsContent value="media" className="space-y-4">
              <div>Medya yönetimi buraya gelecek</div>
            </TabsContent>

            <TabsContent value="seo" className="space-y-4">
              <div>SEO ayarları buraya gelecek</div>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end gap-3 border-t pt-6">
            <Button type="button" variant="outline" onClick={onClose} disabled={loading}>
              İptal
            </Button>
            <Button type="submit" disabled={loading} className="bg-orange-500 hover:bg-orange-600">
              {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              {isEditing ? "Güncelle" : "Oluştur"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
