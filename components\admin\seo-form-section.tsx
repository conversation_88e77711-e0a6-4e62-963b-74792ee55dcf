"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Search, 
  Eye, 
  Share2, 
  Twitter, 
  Globe, 
  Zap, 
  CheckCircle, 
  AlertCircle, 
  Lightbulb,
  BarChart3,
  Target,
  Link
} from 'lucide-react'
import { generateAutoSEO, calculateSEOScore, generateStructuredData, type SEOAnalysis } from '@/lib/seo-helpers'
import type { ProductFormData } from '@/types'

interface SEOFormSectionProps {
  formData: ProductFormData
  onFieldChange: (field: keyof ProductFormData, value: any) => void
  categories?: Array<{ id: string; name: string }>
}

export function SEOFormSection({ formData, onFieldChange, categories }: SEOFormSectionProps) {
  const [seoAnalysis, setSeoAnalysis] = useState<SEOAnalysis>({
    score: 0,
    issues: [],
    suggestions: [],
    keywordDensity: 0,
    readabilityScore: 0
  })
  const [activeTab, setActiveTab] = useState("basic")
  const [isGenerating, setIsGenerating] = useState(false)
  const [generationStatus, setGenerationStatus] = useState<{
    type: 'success' | 'error' | 'info' | null
    message: string
  }>({ type: null, message: '' })

  // Calculate SEO score when relevant fields change
  useEffect(() => {
    const analysis = calculateSEOScore({
      seoTitle: formData.seoTitle,
      seoDescription: formData.seoDescription,
      metaKeywords: formData.metaKeywords,
      focusKeyword: formData.focusKeyword,
      canonicalUrl: formData.canonicalUrl,
      ogTitle: formData.ogTitle,
      ogDescription: formData.ogDescription,
      twitterTitle: formData.twitterTitle,
      twitterDescription: formData.twitterDescription,
      alternativeText: formData.alternativeText,
      description: formData.description
    })
    
    setSeoAnalysis(analysis)
    onFieldChange('seoScore', analysis.score)
    onFieldChange('keywordDensity', analysis.keywordDensity)
    onFieldChange('readabilityScore', analysis.readabilityScore)
  }, [
    formData.seoTitle,
    formData.seoDescription,
    formData.metaKeywords,
    formData.focusKeyword,
    formData.canonicalUrl,
    formData.ogTitle,
    formData.ogDescription,
    formData.twitterTitle,
    formData.twitterDescription,
    formData.alternativeText,
    formData.description
  ])

  // Auto-generate SEO values using LLM
  const handleAutoGenerate = async () => {
    // Validation
    if (!formData.name?.trim()) {
      setGenerationStatus({
        type: 'error',
        message: 'Lütfen önce ürün adını girin.'
      })
      return
    }

    if (!formData.description?.trim()) {
      setGenerationStatus({
        type: 'error',
        message: 'Lütfen önce ürün açıklamasını girin.'
      })
      return
    }

    setIsGenerating(true)
    setGenerationStatus({ type: 'info', message: 'SEO değerleri oluşturuluyor...' })

    try {
      const categoryName = categories?.find(c => c.id === formData.categoryId)?.name

      console.log('🚀 Starting SEO generation for:', formData.name)

      // Call LLM API for SEO generation
      const response = await fetch('/api/seo/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productName: formData.name,
          productDescription: formData.description,
          category: categoryName,
          brand: formData.brand,
          price: formData.basePrice,
          storeName: 'İş Güvenliği Mağazası'
        })
      })

      const result = await response.json()
      console.log('📊 API Response:', { success: result.success, hasData: !!result.data })

      if (!response.ok) {
        throw new Error(result.message || `HTTP ${response.status}: ${response.statusText}`)
      }

      if (result.success && result.data) {
        const seoData = result.data
        console.log('✅ SEO data received, updating form fields...')

        // Update all SEO fields with LLM-generated data
        const fieldsUpdated = []
        Object.entries(seoData).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            onFieldChange(key as keyof ProductFormData, value)
            fieldsUpdated.push(key)
          }
        })

        console.log('🎯 Updated fields:', fieldsUpdated)

        setGenerationStatus({
          type: 'success',
          message: `SEO değerleri başarıyla oluşturuldu! (${fieldsUpdated.length} alan güncellendi)`
        })

        // Clear success message after 5 seconds
        setTimeout(() => {
          setGenerationStatus({ type: null, message: '' })
        }, 5000)

      } else {
        throw new Error(result.message || 'SEO generation failed - no data received')
      }

    } catch (error) {
      console.error('❌ LLM SEO generation failed:', error)

      const errorMessage = error instanceof Error ? error.message : 'Unknown error'

      // Check if it's an LLM implementation error
      if (errorMessage.includes('LLM API not implemented')) {
        setGenerationStatus({
          type: 'error',
          message: 'LLM servisi henüz yapılandırılmamış. Yerel SEO üretimi kullanılıyor...'
        })

        // Use local fallback
        await handleLocalFallback(categoryName)
      } else {
        setGenerationStatus({
          type: 'error',
          message: `SEO üretimi başarısız: ${errorMessage}`
        })
      }
    } finally {
      setIsGenerating(false)
    }
  }

  // Local fallback generation
  const handleLocalFallback = async (categoryName?: string) => {
    try {
      console.log('🔄 Using local SEO generation fallback')

      const autoSEO = generateAutoSEO({
        name: formData.name,
        brand: formData.brand,
        category: categoryName,
        description: formData.description,
        basePrice: formData.basePrice
      })

      // Update form fields
      Object.entries(autoSEO).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          onFieldChange(key as keyof ProductFormData, value)
        }
      })

      // Generate structured data
      const structuredData = generateStructuredData({
        name: formData.name,
        brand: formData.brand,
        description: formData.description,
        basePrice: formData.basePrice,
        category: categoryName,
        availability: formData.stockQuantity && formData.stockQuantity > 0 ? 'InStock' : 'OutOfStock'
      })

      onFieldChange('structuredData', structuredData)
      onFieldChange('schemaMarkup', JSON.stringify(structuredData, null, 2))

      setGenerationStatus({
        type: 'success',
        message: 'SEO değerleri yerel olarak oluşturuldu.'
      })

      // Clear message after 5 seconds
      setTimeout(() => {
        setGenerationStatus({ type: null, message: '' })
      }, 3000)

    } catch (error) {
      console.error('❌ Local fallback failed:', error)
      setGenerationStatus({
        type: 'error',
        message: 'SEO değerleri oluşturulamadı. Lütfen manuel olarak girin.'
      })
    }
  }

  // Get SEO score color
  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600"
    if (score >= 60) return "text-yellow-600"
    return "text-red-600"
  }

  // Get SEO score badge variant
  const getScoreBadgeVariant = (score: number): "default" | "secondary" | "destructive" => {
    if (score >= 80) return "default"
    if (score >= 60) return "secondary"
    return "destructive"
  }

  return (
    <div className="space-y-6">
      {/* SEO Score Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                SEO Skoru
              </CardTitle>
              <CardDescription>
                Genel SEO performansı ve optimizasyon durumu
              </CardDescription>
            </div>
            <div className="text-right">
              <div className={`text-3xl font-bold ${getScoreColor(seoAnalysis.score)}`}>
                {seoAnalysis.score}/100
              </div>
              <Badge variant={getScoreBadgeVariant(seoAnalysis.score)}>
                {seoAnalysis.score >= 80 ? 'Mükemmel' : 
                 seoAnalysis.score >= 60 ? 'İyi' : 'Geliştirilmeli'}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Progress value={seoAnalysis.score} className="mb-4" />
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-sm mb-2 flex items-center gap-1">
                <Target className="h-4 w-4" />
                Anahtar Kelime Yoğunluğu
              </h4>
              <div className="text-2xl font-semibold">
                {seoAnalysis.keywordDensity.toFixed(1)}%
              </div>
            </div>
            <div>
              <h4 className="font-medium text-sm mb-2 flex items-center gap-1">
                <Eye className="h-4 w-4" />
                Okunabilirlik Skoru
              </h4>
              <div className="text-2xl font-semibold">
                {seoAnalysis.readabilityScore.toFixed(0)}/100
              </div>
            </div>
          </div>

          {/* Generation Status */}
          {generationStatus.type && (
            <Alert className={`mt-4 ${
              generationStatus.type === 'success' ? 'border-green-200 bg-green-50' :
              generationStatus.type === 'error' ? 'border-red-200 bg-red-50' :
              'border-blue-200 bg-blue-50'
            }`}>
              {generationStatus.type === 'success' ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : generationStatus.type === 'error' ? (
                <AlertCircle className="h-4 w-4 text-red-600" />
              ) : (
                <Zap className="h-4 w-4 text-blue-600" />
              )}
              <AlertDescription className={
                generationStatus.type === 'success' ? 'text-green-800' :
                generationStatus.type === 'error' ? 'text-red-800' :
                'text-blue-800'
              }>
                {generationStatus.message}
              </AlertDescription>
            </Alert>
          )}

          {/* Auto-generate button */}
          <div className="mt-4 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={handleAutoGenerate}
              disabled={isGenerating || !formData.name || !formData.description}
              className="w-full"
            >
              {isGenerating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                  SEO Değerleri Oluşturuluyor...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  Otomatik SEO Değerleri Oluştur
                </>
              )}
            </Button>
            {(!formData.name || !formData.description) && (
              <p className="text-xs text-muted-foreground mt-2 text-center">
                Önce ürün adı ve açıklamasını girin
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* SEO Issues and Suggestions */}
      {(seoAnalysis.issues.length > 0 || seoAnalysis.suggestions.length > 0) && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {seoAnalysis.issues.length > 0 && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <div className="font-medium mb-2">Düzeltilmesi Gerekenler:</div>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  {seoAnalysis.issues.map((issue, index) => (
                    <li key={index}>{issue}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}
          
          {seoAnalysis.suggestions.length > 0 && (
            <Alert>
              <Lightbulb className="h-4 w-4" />
              <AlertDescription>
                <div className="font-medium mb-2">Öneriler:</div>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  {seoAnalysis.suggestions.map((suggestion, index) => (
                    <li key={index}>{suggestion}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}
        </div>
      )}

      {/* SEO Form Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic" className="flex items-center gap-1">
            <Search className="h-4 w-4" />
            Temel SEO
          </TabsTrigger>
          <TabsTrigger value="social" className="flex items-center gap-1">
            <Share2 className="h-4 w-4" />
            Sosyal Medya
          </TabsTrigger>
          <TabsTrigger value="technical" className="flex items-center gap-1">
            <Globe className="h-4 w-4" />
            Teknik SEO
          </TabsTrigger>
          <TabsTrigger value="advanced" className="flex items-center gap-1">
            <Target className="h-4 w-4" />
            Gelişmiş
          </TabsTrigger>
        </TabsList>

        {/* Basic SEO Tab */}
        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                Temel SEO Ayarları
              </CardTitle>
              <CardDescription>
                Arama motorları için temel meta bilgileri
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="seoTitle">SEO Başlık *</Label>
                <Input
                  id="seoTitle"
                  value={formData.seoTitle || ''}
                  onChange={(e) => onFieldChange('seoTitle', e.target.value)}
                  placeholder="Ürün adı ve marka içeren SEO başlığı"
                  maxLength={60}
                />
                <div className="text-xs text-muted-foreground mt-1">
                  {(formData.seoTitle || '').length}/60 karakter
                </div>
              </div>

              <div>
                <Label htmlFor="seoDescription">SEO Açıklama *</Label>
                <Textarea
                  id="seoDescription"
                  value={formData.seoDescription || ''}
                  onChange={(e) => onFieldChange('seoDescription', e.target.value)}
                  placeholder="Ürünü tanıtan özlü açıklama"
                  maxLength={160}
                  rows={3}
                />
                <div className="text-xs text-muted-foreground mt-1">
                  {(formData.seoDescription || '').length}/160 karakter
                </div>
              </div>

              <div>
                <Label htmlFor="focusKeyword">Odak Anahtar Kelime</Label>
                <Input
                  id="focusKeyword"
                  value={formData.focusKeyword || ''}
                  onChange={(e) => onFieldChange('focusKeyword', e.target.value)}
                  placeholder="Ana anahtar kelime"
                />
              </div>

              <div>
                <Label htmlFor="metaKeywords">Meta Anahtar Kelimeler</Label>
                <Input
                  id="metaKeywords"
                  value={(formData.metaKeywords || []).join(', ')}
                  onChange={(e) => onFieldChange('metaKeywords', e.target.value.split(',').map(k => k.trim()).filter(k => k))}
                  placeholder="anahtar kelime 1, anahtar kelime 2, ..."
                />
                <div className="text-xs text-muted-foreground mt-1">
                  Virgülle ayırarak yazın (maksimum 10 adet)
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Social Media Tab */}
        <TabsContent value="social" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Open Graph */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Share2 className="h-5 w-5" />
                  Open Graph (Facebook)
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="ogTitle">OG Başlık</Label>
                  <Input
                    id="ogTitle"
                    value={formData.ogTitle || ''}
                    onChange={(e) => onFieldChange('ogTitle', e.target.value)}
                    placeholder="Facebook paylaşım başlığı"
                    maxLength={60}
                  />
                </div>

                <div>
                  <Label htmlFor="ogDescription">OG Açıklama</Label>
                  <Textarea
                    id="ogDescription"
                    value={formData.ogDescription || ''}
                    onChange={(e) => onFieldChange('ogDescription', e.target.value)}
                    placeholder="Facebook paylaşım açıklaması"
                    maxLength={160}
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="ogImage">OG Görsel URL</Label>
                  <Input
                    id="ogImage"
                    value={formData.ogImage || ''}
                    onChange={(e) => onFieldChange('ogImage', e.target.value)}
                    placeholder="https://example.com/image.jpg"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Twitter Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Twitter className="h-5 w-5" />
                  Twitter Card
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="twitterTitle">Twitter Başlık</Label>
                  <Input
                    id="twitterTitle"
                    value={formData.twitterTitle || ''}
                    onChange={(e) => onFieldChange('twitterTitle', e.target.value)}
                    placeholder="Twitter paylaşım başlığı"
                    maxLength={60}
                  />
                </div>

                <div>
                  <Label htmlFor="twitterDescription">Twitter Açıklama</Label>
                  <Textarea
                    id="twitterDescription"
                    value={formData.twitterDescription || ''}
                    onChange={(e) => onFieldChange('twitterDescription', e.target.value)}
                    placeholder="Twitter paylaşım açıklaması"
                    maxLength={160}
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="twitterImage">Twitter Görsel URL</Label>
                  <Input
                    id="twitterImage"
                    value={formData.twitterImage || ''}
                    onChange={(e) => onFieldChange('twitterImage', e.target.value)}
                    placeholder="https://example.com/image.jpg"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Technical SEO Tab */}
        <TabsContent value="technical" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Teknik SEO Ayarları
              </CardTitle>
              <CardDescription>
                Arama motoru tarama ve indeksleme ayarları
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="canonicalUrl">Canonical URL</Label>
                <Input
                  id="canonicalUrl"
                  value={formData.canonicalUrl || ''}
                  onChange={(e) => onFieldChange('canonicalUrl', e.target.value)}
                  placeholder="/urun/urun-adi"
                />
                <div className="text-xs text-muted-foreground mt-1">
                  Ürünün benzersiz URL'si
                </div>
              </div>

              <div>
                <Label htmlFor="robotsDirective">Robots Direktifi</Label>
                <Select
                  value={formData.robotsDirective || 'index,follow'}
                  onValueChange={(value) => onFieldChange('robotsDirective', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="index,follow">Index, Follow (Önerilen)</SelectItem>
                    <SelectItem value="noindex,nofollow">No Index, No Follow</SelectItem>
                    <SelectItem value="index,nofollow">Index, No Follow</SelectItem>
                    <SelectItem value="noindex,follow">No Index, Follow</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="alternativeText">Görsel Alt Metni</Label>
                <Input
                  id="alternativeText"
                  value={formData.alternativeText || ''}
                  onChange={(e) => onFieldChange('alternativeText', e.target.value)}
                  placeholder="Ürün görseli için açıklayıcı metin"
                />
              </div>

              <div>
                <Label htmlFor="breadcrumbs">Breadcrumb Yolu</Label>
                <Input
                  id="breadcrumbs"
                  value={(formData.breadcrumbs || []).join(' > ')}
                  onChange={(e) => onFieldChange('breadcrumbs', e.target.value.split(' > ').map(b => b.trim()).filter(b => b))}
                  placeholder="Ana Sayfa > Kategori > Ürün"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Advanced SEO Tab */}
        <TabsContent value="advanced" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Gelişmiş SEO Ayarları
              </CardTitle>
              <CardDescription>
                Yapılandırılmış veri ve gelişmiş SEO özellikleri
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="schemaMarkup">Schema Markup (JSON-LD)</Label>
                <Textarea
                  id="schemaMarkup"
                  value={formData.schemaMarkup || ''}
                  onChange={(e) => onFieldChange('schemaMarkup', e.target.value)}
                  placeholder="Yapılandırılmış veri JSON formatında"
                  rows={8}
                  className="font-mono text-sm"
                />
                <div className="text-xs text-muted-foreground mt-1">
                  Otomatik oluştur butonunu kullanarak JSON-LD formatında yapılandırılmış veri oluşturabilirsiniz
                </div>
              </div>

              <Separator />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="internalLinks">İç Linkler</Label>
                  <Textarea
                    id="internalLinks"
                    value={(formData.internalLinks || []).join('\n')}
                    onChange={(e) => onFieldChange('internalLinks', e.target.value.split('\n').map(l => l.trim()).filter(l => l))}
                    placeholder="/kategori/urun1&#10;/kategori/urun2"
                    rows={4}
                  />
                </div>

                <div>
                  <Label htmlFor="externalLinks">Dış Linkler</Label>
                  <Textarea
                    id="externalLinks"
                    value={(formData.externalLinks || []).join('\n')}
                    onChange={(e) => onFieldChange('externalLinks', e.target.value.split('\n').map(l => l.trim()).filter(l => l))}
                    placeholder="https://example.com&#10;https://example2.com"
                    rows={4}
                  />
                </div>
              </div>

              <Separator />

              <div>
                <Label>Hreflang Etiketleri</Label>
                <div className="space-y-2 mt-2">
                  <div className="grid grid-cols-2 gap-2">
                    <Input
                      placeholder="tr-TR"
                      value="tr-TR"
                      disabled
                    />
                    <Input
                      placeholder="Türkçe URL"
                      value={formData.canonicalUrl || ''}
                      onChange={(e) => {
                        const hreflangTags = { ...formData.hreflangTags, 'tr-TR': e.target.value }
                        onFieldChange('hreflangTags', hreflangTags)
                      }}
                    />
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Çoklu dil desteği için hreflang etiketleri
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* SEO Performance Metrics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                SEO Performans Metrikleri
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {seoAnalysis.score}
                  </div>
                  <div className="text-sm text-muted-foreground">SEO Skoru</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {seoAnalysis.keywordDensity.toFixed(1)}%
                  </div>
                  <div className="text-sm text-muted-foreground">Anahtar Kelime Yoğunluğu</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {seoAnalysis.readabilityScore.toFixed(0)}
                  </div>
                  <div className="text-sm text-muted-foreground">Okunabilirlik</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
