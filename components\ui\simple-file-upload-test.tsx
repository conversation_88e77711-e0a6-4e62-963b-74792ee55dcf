'use client'

import { useState } from 'react'

export function SimpleFileUploadTest() {
  const [selectedFile, setSelectedFile] = useState<string>('')

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setSelectedFile(file.name)
      console.log('✅ File selected:', file.name)
    }
  }

  return (
    <div className="p-4 border rounded-lg">
      <h3 className="text-lg font-semibold mb-4">Simple File Upload Test</h3>
      
      {/* Method 1: Basic input */}
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">Method 1: Basic Input</label>
        <input
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
        />
      </div>

      {/* Method 2: Label with hidden input */}
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">Method 2: Label with Hidden Input</label>
        <label className="cursor-pointer inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
          <input
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            className="sr-only"
          />
          Choose File
        </label>
      </div>

      {/* Method 3: Drag and drop area */}
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">Method 3: Drag & Drop Area</label>
        <label className="cursor-pointer block w-full p-6 border-2 border-dashed border-gray-300 rounded-lg text-center hover:border-gray-400">
          <input
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            className="sr-only"
          />
          <div>
            <p className="text-gray-600">Click to upload or drag and drop</p>
            <p className="text-xs text-gray-400">PNG, JPG, WEBP (Max 5MB)</p>
          </div>
        </label>
      </div>

      {selectedFile && (
        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
          <p className="text-green-800">Selected file: {selectedFile}</p>
        </div>
      )}
    </div>
  )
}
