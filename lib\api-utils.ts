/**
 * API Response Utilities
 * Standardized response helpers for API endpoints
 */

export interface ApiSuccessResponse<T = any> {
  success: true
  data?: T
  message?: string
  meta?: any
  pagination?: {
    total: number
    page: number
    limit: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export interface ApiErrorResponse {
  success: false
  message: string
  error?: string
  code?: string
  details?: any
}

export type ApiResponse<T = any> = ApiSuccessResponse<T> | ApiErrorResponse

/**
 * Create a standardized success response
 */
export function createSuccessResponse<T = any>(
  data?: T | { data?: T; message?: string; meta?: any; pagination?: any }
): ApiSuccessResponse<T> {
  // If data is an object with nested structure, extract it
  if (data && typeof data === 'object' && 'data' in data) {
    return {
      success: true,
      data: data.data,
      message: data.message,
      meta: data.meta,
      pagination: data.pagination
    }
  }

  // Simple data response
  return {
    success: true,
    data: data as T
  }
}

/**
 * Create a standardized error response
 */
export function createErrorResponse(
  message: string,
  options: {
    error?: string
    code?: string
    details?: any
  } = {}
): ApiErrorResponse {
  return {
    success: false,
    message,
    error: options.error,
    code: options.code,
    details: options.details
  }
}

/**
 * Validate required fields in request body
 */
export function validateRequiredFields(
  body: any,
  requiredFields: string[]
): { isValid: boolean; missingFields: string[] } {
  const missingFields: string[] = []

  for (const field of requiredFields) {
    if (body[field] === undefined || body[field] === null || body[field] === '') {
      missingFields.push(field)
    }
  }

  return {
    isValid: missingFields.length === 0,
    missingFields
  }
}

/**
 * Parse and validate pagination parameters
 */
export function parsePaginationParams(searchParams: URLSearchParams): {
  page: number
  limit: number
  skip: number
} {
  const page = Math.max(1, parseInt(searchParams.get('page') || '1'))
  const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '10')))
  const skip = (page - 1) * limit

  return { page, limit, skip }
}

/**
 * Create pagination metadata
 */
export function createPaginationMeta(
  total: number,
  page: number,
  limit: number
) {
  const totalPages = Math.ceil(total / limit)
  
  return {
    total,
    page,
    limit,
    totalPages,
    hasNext: page < totalPages,
    hasPrev: page > 1
  }
}

/**
 * Handle API errors consistently
 */
export function handleApiError(error: any, defaultMessage: string = "Bir hata oluştu") {
  console.error("API Error:", error)
  
  if (error.code === 'P2002') {
    return createErrorResponse("Bu kayıt zaten mevcut", {
      code: 'DUPLICATE_ENTRY',
      error: error.message
    })
  }
  
  if (error.code === 'P2025') {
    return createErrorResponse("Kayıt bulunamadı", {
      code: 'NOT_FOUND',
      error: error.message
    })
  }
  
  return createErrorResponse(defaultMessage, {
    error: error.message,
    code: error.code
  })
}

/**
 * Sanitize and validate sort parameters
 */
export function parseSortParams(
  searchParams: URLSearchParams,
  allowedFields: string[] = []
): { sortBy?: string; sortOrder: 'asc' | 'desc' } {
  const sortBy = searchParams.get('sortBy')
  const sortOrder = searchParams.get('sortOrder')?.toLowerCase() === 'desc' ? 'desc' : 'asc'

  // Validate sortBy field
  if (sortBy && allowedFields.length > 0 && !allowedFields.includes(sortBy)) {
    return { sortOrder }
  }

  return {
    sortBy: sortBy || undefined,
    sortOrder
  }
}

/**
 * Parse boolean query parameters
 */
export function parseBooleanParam(value: string | null): boolean | undefined {
  if (value === null) return undefined
  return value.toLowerCase() === 'true'
}

/**
 * Rate limiting helper (basic implementation)
 */
export class RateLimiter {
  private requests: Map<string, number[]> = new Map()

  isAllowed(
    identifier: string,
    maxRequests: number = 100,
    windowMs: number = 60000 // 1 minute
  ): boolean {
    const now = Date.now()
    const windowStart = now - windowMs

    // Get existing requests for this identifier
    const userRequests = this.requests.get(identifier) || []
    
    // Filter out old requests
    const recentRequests = userRequests.filter(time => time > windowStart)
    
    // Check if limit exceeded
    if (recentRequests.length >= maxRequests) {
      return false
    }

    // Add current request
    recentRequests.push(now)
    this.requests.set(identifier, recentRequests)

    return true
  }

  reset(identifier: string): void {
    this.requests.delete(identifier)
  }
}

// Global rate limiter instance
export const globalRateLimiter = new RateLimiter()

/**
 * Extract client IP from request
 */
export function getClientIP(request: Request): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  if (realIP) {
    return realIP
  }
  
  return 'unknown'
}
