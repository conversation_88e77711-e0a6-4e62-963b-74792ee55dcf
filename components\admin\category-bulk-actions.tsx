"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Loader2, Eye, EyeOff, Trash2 } from "lucide-react"

interface CategoryBulkActionsProps {
  open: boolean
  onClose: () => void
  selectedCategories: string[]
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

export function CategoryBulkActions({
  open,
  onClose,
  selectedCategories,
  onSuccess,
  onError,
}: CategoryBulkActionsProps) {
  const [action, setAction] = useState<string>("")
  const [loading, setLoading] = useState(false)

  const handleExecute = async () => {
    if (!action) return

    try {
      setLoading(true)

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000))

      let message = ""
      switch (action) {
        case "activate":
          message = `${selectedCategories.length} kategori aktif hale getirildi.`
          break
        case "deactivate":
          message = `${selectedCategories.length} kategori pasif hale getirildi.`
          break
        case "delete":
          message = `${selectedCategories.length} kategori silindi.`
          break
        default:
          message = "İşlem tamamlandı."
      }

      onSuccess(message)
      onClose()
    } catch (error) {
      onError("Toplu işlem sırasında bir hata oluştu.")
    } finally {
      setLoading(false)
    }
  }

  const actionOptions = [
    { value: "activate", label: "Aktif Yap", icon: Eye, color: "text-green-600" },
    { value: "deactivate", label: "Pasif Yap", icon: EyeOff, color: "text-gray-600" },
    { value: "delete", label: "Sil", icon: Trash2, color: "text-red-600" },
  ]

  const selectedAction = actionOptions.find((opt) => opt.value === action)

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Toplu İşlem</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Badge variant="secondary">{selectedCategories.length} kategori seçildi</Badge>
          </div>

          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">Yapılacak İşlem</label>
            <Select value={action} onValueChange={setAction}>
              <SelectTrigger>
                <SelectValue placeholder="İşlem seçin..." />
              </SelectTrigger>
              <SelectContent>
                {actionOptions.map((option) => {
                  const Icon = option.icon
                  return (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <Icon className={`h-4 w-4 ${option.color}`} />
                        {option.label}
                      </div>
                    </SelectItem>
                  )
                })}
              </SelectContent>
            </Select>
          </div>

          {action === "delete" && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-800">
                <strong>Uyarı:</strong> Bu işlem geri alınamaz. Seçili kategoriler kalıcı olarak silinecektir.
              </p>
            </div>
          )}
        </div>

        <div className="flex justify-end gap-3 pt-4">
          <Button variant="outline" onClick={onClose} disabled={loading}>
            İptal
          </Button>
          <Button
            onClick={handleExecute}
            disabled={!action || loading}
            variant={action === "delete" ? "destructive" : "default"}
          >
            {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            {selectedAction ? selectedAction.label : "Uygula"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
