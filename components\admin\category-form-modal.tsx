"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import {
  Loader2,
  Save,
  X,
  Info,
  Eye,
  Palette,
  DollarSign,
  Package,
  Shield,
  History,
  Smartphone,
  Image
} from "lucide-react"

// UI Components
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardH<PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { CategoryImageUpload } from "@/components/ui/category-image-upload"
import { CategoryBannerManager } from "@/components/admin/category-banner-manager"
import { CategoryBannerForm } from "@/components/admin/category-banner-form"

// Hooks and Services
import { useMainCategories, useCreateCategory, useUpdateCategory } from "@/lib/hooks/use-categories"
import {
  ApprovalStatus,
  ApprovalStatusLabels,
  IconType,
  IconTypeLabels,
  MobileTemplate,
  MobileTemplateLabels,
  getEnumOptions
} from "@/lib/enums"
import type { Category } from "@/types"
import { toast } from "sonner"

// Form validation schema
const categoryFormSchema = z.object({
  // Temel Bilgiler
  name: z.string().min(2, "Kategori adı en az 2 karakter olmalıdır"),
  description: z.string().min(10, "Açıklama en az 10 karakter olmalıdır"),
  icon: z.string().min(1, "Icon seçilmelidir"),
  parentId: z.string().nullable(),
  isActive: z.boolean(),
  sortOrder: z.number().min(0),

  // SEO Bilgileri
  seoTitle: z.string().optional(),
  seoDescription: z.string().optional(),
  metaKeywords: z.string().optional(),
  ogTitle: z.string().optional(),
  ogDescription: z.string().optional(),
  ogImage: z.string().optional(),

  // Görsel & UI
  categoryImage: z.string().optional(),
  colorCode: z.string().optional(),
  iconType: z.nativeEnum(IconType).optional(),

  // Performans
  searchKeywords: z.string().optional(),
  isSearchable: z.boolean(),

  // İş Kuralları
  minOrderAmount: z.number().min(0).optional(),
  commissionRate: z.number().min(0).max(100).optional(),
  taxRate: z.number().min(0).max(100).optional(),

  // Stok Yönetimi alanları kaldırıldı - stok yönetimi Product seviyesinde yapılacak

  // Admin Özellikleri
  isPromoted: z.boolean(),
  isFeatured: z.boolean(),
  adminNotes: z.string().optional(),
  approvalStatus: z.nativeEnum(ApprovalStatus),

  // Mobil Optimizasyon
  mobileIcon: z.string().optional(),
  mobileTemplate: z.nativeEnum(MobileTemplate).optional(),
})

type CategoryFormData = z.infer<typeof categoryFormSchema>

interface CategoryFormModalProps {
  open: boolean
  onClose: () => void
  category: Category | null
  onSuccess?: (message: string) => void
}

export function CategoryFormModal({ open, onClose, category, onSuccess }: CategoryFormModalProps) {
  const isEditing = !!category
  const [activeTab, setActiveTab] = useState("basic")
  const [isClosing, setIsClosing] = useState(false)

  // Banner management states
  const [showBannerForm, setShowBannerForm] = useState(false)
  const [editingBanner, setEditingBanner] = useState<any>(null)
  const [bannerRefreshTrigger, setBannerRefreshTrigger] = useState(0)

  // Form setup with React Hook Form
  const form = useForm<CategoryFormData>({
    resolver: zodResolver(categoryFormSchema),
    defaultValues: {
      name: "",
      description: "",
      icon: "Package",
      parentId: null,
      isActive: true,
      sortOrder: 0,
      seoTitle: "",
      seoDescription: "",
      metaKeywords: "",
      ogTitle: "",
      ogDescription: "",
      ogImage: "",
      categoryImage: "",
      colorCode: "#3B82F6",
      iconType: IconType.SVG,
      searchKeywords: "",
      isSearchable: true,
      minOrderAmount: 0,
      commissionRate: 0,
      taxRate: 20,
      isPromoted: false,
      isFeatured: false,
      adminNotes: "",
      approvalStatus: ApprovalStatus.ONAYLANDI,
      mobileIcon: "",
      mobileTemplate: MobileTemplate.VARSAYILAN,
    }
  })

  // Hooks
  const { data: mainCategoriesData } = useMainCategories()
  const { mutate: createCategory, loading: creating } = useCreateCategory()
  const { mutate: updateCategory, loading: updating } = useUpdateCategory()

  const loading = creating || updating
  const mainCategories = mainCategoriesData?.data || []

  // Reset form when modal opens/closes
  useEffect(() => {
    if (open) {
      if (category) {
        // Editing mode - populate form with category data
        form.reset({
          name: category.name,
          description: category.description,
          icon: category.icon,
          parentId: category.parentId,
          isActive: category.isActive,
          sortOrder: category.sortOrder,
          seoTitle: category.seoTitle || "",
          seoDescription: category.seoDescription || "",
          metaKeywords: category.metaKeywords || "",
          ogTitle: category.ogTitle || "",
          ogDescription: category.ogDescription || "",
          ogImage: category.ogImage || "",
          categoryImage: category.categoryImage || "",
          colorCode: category.colorCode || "#3B82F6",
          iconType: category.iconType || IconType.SVG,
          searchKeywords: category.searchKeywords || "",
          isSearchable: category.isSearchable ?? true,
          minOrderAmount: category.minOrderAmount || 0,
          commissionRate: category.commissionRate || 0,
          taxRate: category.taxRate || 20,
          isPromoted: category.isPromoted || false,
          isFeatured: category.isFeatured || false,
          adminNotes: category.adminNotes || "",
          approvalStatus: category.approvalStatus || ApprovalStatus.ONAYLANDI,
          mobileIcon: category.mobileIcon || "",
          mobileTemplate: category.mobileTemplate || MobileTemplate.VARSAYILAN,
        })
      } else {
        // Create mode - reset to defaults
        form.reset()
      }
      setActiveTab("basic")
    }
  }, [open, category, form])

  // Banner management handlers
  const handleCreateBanner = () => {
    setEditingBanner(null)
    setShowBannerForm(true)
  }

  const handleEditBanner = (banner: any) => {
    setEditingBanner(banner)
    setShowBannerForm(true)
  }

  const handleBannerFormClose = () => {
    setShowBannerForm(false)
    setEditingBanner(null)
  }

  const handleBannerFormSuccess = () => {
    setShowBannerForm(false)
    setEditingBanner(null)
    // Trigger banner list refresh
    setBannerRefreshTrigger(prev => prev + 1)
  }

  // Form submit handler
  const onSubmit = async (data: CategoryFormData) => {
    try {
      // Show loading toast
      const loadingToast = toast.loading(
        isEditing ? "Kategori güncelleniyor..." : "Kategori oluşturuluyor..."
      )

      let result
      if (isEditing && category) {
        console.log('🔄 Updating category with data:', data)
        result = await updateCategory({
          id: category.id,
          ...data
        })
      } else {
        console.log('🔄 Creating category with data:', data)
        result = await createCategory(data)
      }

      // Dismiss loading toast
      toast.dismiss(loadingToast)

      // Show success toast
      const successMessage = isEditing ? "Kategori başarıyla güncellendi!" : "Kategori başarıyla oluşturuldu!"
      toast.success(successMessage, {
        description: `${data.name} kategorisi ${isEditing ? 'güncellendi' : 'oluşturuldu'}.`,
        action: !isEditing ? {
          label: "Banner Ekle",
          onClick: () => {
            // Kategori oluşturulduktan sonra banner sekmesine geç
            setActiveTab("banners")
          }
        } : undefined
      })

      // Close modal with proper cleanup
      setIsClosing(true)
      setTimeout(() => {
        onSuccess?.(successMessage)
        onClose()
        setIsClosing(false)
      }, 150) // Small delay to ensure proper cleanup
    } catch (error) {
      console.error('❌ Form submission error:', error)

      // Show error toast
      toast.error(
        isEditing ? "Kategori güncellenirken hata oluştu" : "Kategori oluşturulurken hata oluştu",
        {
          description: error instanceof Error ? error.message : "Bilinmeyen bir hata oluştu. Lütfen tekrar deneyin.",
          action: {
            label: "Tekrar Dene",
            onClick: () => form.handleSubmit(onSubmit)()
          }
        }
      )
    }
  }
  // Enum options for dropdowns
  const approvalStatusOptions = getEnumOptions(ApprovalStatus, ApprovalStatusLabels)
  const iconTypeOptions = getEnumOptions(IconType, IconTypeLabels)
  const mobileTemplateOptions = getEnumOptions(MobileTemplate, MobileTemplateLabels)

  // Cleanup modal overlays when component unmounts or modal closes
  useEffect(() => {
    if (!open) {
      // Clean up any lingering modal overlays aggressively
      const cleanupOverlays = () => {
        // Remove all possible modal/dialog overlays
        const selectors = [
          '[data-radix-popper-content-wrapper]',
          '[data-radix-dialog-overlay]',
          '[data-state="open"]',
          '.fixed.inset-0',
          '[role="dialog"]',
          '[data-radix-dialog-content]'
        ]

        selectors.forEach(selector => {
          const elements = document.querySelectorAll(selector)
          elements.forEach(element => {
            if (element.getAttribute('data-state') === 'open' ||
                element.classList.contains('fixed')) {
              element.remove()
            }
          })
        })

        // Reset body styles that might be stuck
        document.body.style.overflow = ''
        document.body.style.pointerEvents = ''
        document.body.style.paddingRight = ''

        // Remove any stuck classes
        document.body.classList.remove('overflow-hidden')
      }

      // Multiple cleanup attempts to ensure it works
      setTimeout(cleanupOverlays, 100)
      setTimeout(cleanupOverlays, 300)
      setTimeout(cleanupOverlays, 500)
    }
  }, [open])

  return (
    <>
    <Dialog
      open={open && !isClosing}
      onOpenChange={(newOpen) => {
        if (!newOpen && !isClosing) {
          setIsClosing(true)
          setTimeout(() => {
            onClose()
            setIsClosing(false)
          }, 100)
        }
      }}
    >
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            {isEditing ? "Kategori Düzenle" : "Yeni Kategori Oluştur"}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <fieldset disabled={form.formState.isSubmitting || loading} className="space-y-6">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-6">
                <TabsTrigger value="basic" className="flex items-center gap-1">
                  <Info className="h-4 w-4" />
                  Temel
                </TabsTrigger>
                <TabsTrigger value="seo" className="flex items-center gap-1">
                  <Eye className="h-4 w-4" />
                  SEO
                </TabsTrigger>
                <TabsTrigger value="visual" className="flex items-center gap-1">
                  <Palette className="h-4 w-4" />
                  Görsel
                </TabsTrigger>
                <TabsTrigger value="banners" className="flex items-center gap-1" disabled={!isEditing}>
                  <Image className="h-4 w-4" />
                  Banner'lar
                </TabsTrigger>
                <TabsTrigger value="business" className="flex items-center gap-1">
                  <DollarSign className="h-4 w-4" />
                  İş Kuralları
                </TabsTrigger>
                <TabsTrigger value="admin" className="flex items-center gap-1">
                  <Shield className="h-4 w-4" />
                  Admin
                </TabsTrigger>
              </TabsList>
              {/* TEMEL BİLGİLER TAB */}
              <TabsContent value="basic" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Info className="h-5 w-5" />
                      Temel Bilgiler
                    </CardTitle>
                    <CardDescription>
                      Kategori için temel bilgileri girin
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Kategori Adı *</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Örn: Baş Koruma"
                                disabled={form.formState.isSubmitting || loading}
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="parentId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Üst Kategori</FormLabel>
                            <Select
                              onValueChange={(value) => {
                                // "null" string'ini null value'ya çevir
                                field.onChange(value === "null" ? null : value)
                              }}
                              value={field.value || "null"}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Ana kategori seçin" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="null">Ana Kategori</SelectItem>
                                {mainCategories.map((cat) => (
                                  <SelectItem key={cat.id} value={cat.id}>
                                    {cat.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Açıklama *</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Kategori açıklaması..."
                              rows={3}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <FormField
                        control={form.control}
                        name="icon"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>İkon</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="HardHat">🪖 Baret</SelectItem>
                                <SelectItem value="Hand">✋ El</SelectItem>
                                <SelectItem value="Footprints">👣 Ayak</SelectItem>
                                <SelectItem value="ShirtIcon">👕 Giyim</SelectItem>
                                <SelectItem value="Wind">💨 Solunum</SelectItem>
                                <SelectItem value="Eye">👁️ Göz</SelectItem>
                                <SelectItem value="Package">📦 Paket</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="sortOrder"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Sıra Numarası</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="flex flex-col gap-4">
                        <FormField
                          control={form.control}
                          name="isActive"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                              <div className="space-y-0.5">
                                <FormLabel>Aktif</FormLabel>
                                <FormDescription>
                                  Kategori aktif mi?
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="isSearchable"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                              <div className="space-y-0.5">
                                <FormLabel>Aranabilir</FormLabel>
                                <FormDescription>
                                  Arama sonuçlarında göster
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    <FormField
                      control={form.control}
                      name="searchKeywords"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Arama Anahtar Kelimeleri</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="virgülle ayırın: baret,kask,baş koruma"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Kullanıcıların bu kategoriyi bulmasını kolaylaştıracak kelimeler
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              {/* SEO TAB */}
              <TabsContent value="seo" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Eye className="h-5 w-5" />
                      SEO Optimizasyonu
                    </CardTitle>
                    <CardDescription>
                      Arama motoru optimizasyonu için meta bilgileri
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="seoTitle"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>SEO Başlık</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Arama motorları için başlık"
                                maxLength={60}
                                {...field}
                              />
                            </FormControl>
                            <FormDescription>
                              {field.value?.length || 0}/60 karakter
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="ogTitle"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Open Graph Başlık</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Sosyal medya paylaşımları için"
                                maxLength={60}
                                {...field}
                              />
                            </FormControl>
                            <FormDescription>
                              {field.value?.length || 0}/60 karakter
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="seoDescription"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>SEO Açıklama</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Arama motorları için açıklama"
                              maxLength={160}
                              rows={3}
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            {field.value?.length || 0}/160 karakter
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="ogDescription"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Open Graph Açıklama</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Sosyal medya paylaşımları için açıklama"
                              maxLength={160}
                              rows={2}
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            {field.value?.length || 0}/160 karakter
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="metaKeywords"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Meta Anahtar Kelimeler</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="virgülle ayırın: güvenlik,iş güvenliği"
                                {...field}
                              />
                            </FormControl>
                            <FormDescription>
                              SEO için anahtar kelimeler
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="ogImage"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Open Graph Resmi</FormLabel>
                            <FormControl>
                              <CategoryImageUpload
                                imageUrl={field.value || undefined}
                                onImageChange={(url) => field.onChange(url || "")}
                                disabled={form.formState.isSubmitting || loading}
                                placeholder="OG resmi yüklemek için tıklayın"
                              />
                            </FormControl>
                            <FormDescription>
                              Sosyal medya paylaşımları için resim (1200x630 önerilir)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* GÖRSEL TAB */}
              <TabsContent value="visual" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Palette className="h-5 w-5" />
                      Görsel Tasarım
                    </CardTitle>
                    <CardDescription>
                      Kategori için görsel öğeler ve tasarım ayarları
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="colorCode"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Renk Kodu</FormLabel>
                            <div className="flex gap-2">
                              <FormControl>
                                <Input
                                  type="color"
                                  className="w-16 h-10 p-1 border rounded"
                                  {...field}
                                />
                              </FormControl>
                              <FormControl>
                                <Input
                                  placeholder="#3B82F6"
                                  {...field}
                                />
                              </FormControl>
                            </div>
                            <FormDescription>
                              Kategori teması için ana renk
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="iconType"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>İkon Tipi</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="İkon tipi seçin" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {iconTypeOptions.map((option) => (
                                  <SelectItem key={option.value} value={option.value}>
                                    {option.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="categoryImage"
                      render={({ field }) => {
                        console.log('🎨 CategoryFormModal: categoryImage field render')
                        console.log('   - Field Value:', field.value)
                        console.log('   - Field Value Type:', typeof field.value)
                        console.log('   - Has Field Value:', !!field.value)
                        console.log('   - Field Value Length:', field.value?.length || 0)

                        return (
                          <FormItem>
                            <FormLabel>
                              Kategori Görseli
                            </FormLabel>
                            <FormControl>
                              <CategoryImageUpload
                                imageUrl={field.value || undefined}
                                onImageChange={(url) => {
                                  console.log('🎨 CategoryFormModal: categoryImage onImageChange called', {
                                    newUrl: url,
                                    urlType: typeof url
                                  })
                                  field.onChange(url || "")
                                }}
                                disabled={form.formState.isSubmitting || loading}
                                placeholder="Kategori görseli yüklemek için tıklayın veya sürükleyin"
                              />
                            </FormControl>
                            <FormDescription>
                              Kategori kartlarında gösterilecek ana görsel (PNG, JPG, WEBP - Max 5MB)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )
                      }}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="mobileIcon"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Mobil İkon</FormLabel>
                            <FormControl>
                              <CategoryImageUpload
                                imageUrl={field.value || undefined}
                                onImageChange={(url) => field.onChange(url || "")}
                                disabled={form.formState.isSubmitting || loading}
                                placeholder="Mobil ikon yüklemek için tıklayın"
                              />
                            </FormControl>
                            <FormDescription>
                              Mobil cihazlar için özel ikon (PNG, JPG, SVG - Max 5MB)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="mobileTemplate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Mobil Şablon</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Mobil şablon seçin" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {mobileTemplateOptions.map((option) => (
                                  <SelectItem key={option.value} value={option.value}>
                                    {option.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              Mobil görünüm şablonu
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Renk Önizleme */}
                    {form.watch("colorCode") && (
                      <div className="p-4 rounded-lg border">
                        <Label className="text-sm font-medium">Renk Önizleme</Label>
                        <div className="mt-2 flex items-center gap-4">
                          <div
                            className="w-16 h-16 rounded-lg border-2 border-gray-200"
                            style={{ backgroundColor: form.watch("colorCode") }}
                          />
                          <div className="space-y-1">
                            <div className="text-sm text-gray-600">Seçilen Renk</div>
                            <div className="font-mono text-sm">{form.watch("colorCode")}</div>
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* İŞ KURALLARI TAB */}
              <TabsContent value="business" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <DollarSign className="h-5 w-5" />
                      İş Kuralları
                    </CardTitle>
                    <CardDescription>
                      Kategori için iş kuralları ve fiyatlandırma ayarları
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <FormField
                        control={form.control}
                        name="minOrderAmount"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Minimum Sipariş Tutarı (₺)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                step="0.01"
                                placeholder="0.00"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormDescription>
                              Bu kategoriden minimum sipariş tutarı
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="commissionRate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Komisyon Oranı (%)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                max="100"
                                step="0.1"
                                placeholder="0.0"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormDescription>
                              Satış komisyon oranı
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="taxRate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Vergi Oranı (%)</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                max="100"
                                step="0.1"
                                placeholder="20.0"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormDescription>
                              KDV oranı (varsayılan %20)
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* İş Kuralları Özeti */}
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <h4 className="font-medium text-sm mb-2">İş Kuralları Özeti</h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">Min. Sipariş:</span>
                          <span className="ml-2 font-medium">
                            ₺{form.watch("minOrderAmount") || 0}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-600">Komisyon:</span>
                          <span className="ml-2 font-medium">
                            %{form.watch("commissionRate") || 0}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-600">Vergi:</span>
                          <span className="ml-2 font-medium">
                            %{form.watch("taxRate") || 20}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* STOK YÖNETİMİ TAB KALDIRILDI - Stok yönetimi Product seviyesinde yapılacak */}

              {/* BANNER YÖNETİMİ TAB */}
              <TabsContent value="banners" className="space-y-6">
                {isEditing && category ? (
                  <CategoryBannerManager
                    key={bannerRefreshTrigger} // Force re-render when banners change
                    category={category}
                    onCreateBanner={handleCreateBanner}
                    onEditBanner={handleEditBanner}
                  />
                ) : (
                  <Card>
                    <CardContent className="p-8 text-center">
                      <div className="text-gray-500">
                        <Image className="w-12 h-12 mx-auto mb-4 opacity-50" />
                        <p className="text-lg font-medium mb-2">Banner Yönetimi</p>
                        <p className="text-sm">
                          Banner'ları yönetmek için önce kategoriyi kaydedin.
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              {/* ADMİN ÖZELLİKLERİ TAB */}
              <TabsContent value="admin" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="h-5 w-5" />
                      Admin Özellikleri
                    </CardTitle>
                    <CardDescription>
                      Yönetici paneli için özel ayarlar ve notlar
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-4">
                        <FormField
                          control={form.control}
                          name="isPromoted"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base">Öne Çıkarılmış</FormLabel>
                                <FormDescription>
                                  Ana sayfada öne çıkarılsın mı?
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="isFeatured"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                              <div className="space-y-0.5">
                                <FormLabel className="text-base">Vitrin Kategorisi</FormLabel>
                                <FormDescription>
                                  Vitrin alanında gösterilsin mi?
                                </FormDescription>
                              </div>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name="approvalStatus"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Onay Durumu</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Onay durumu seçin" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {approvalStatusOptions.map((option) => (
                                  <SelectItem key={option.value} value={option.value}>
                                    {option.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormDescription>
                              Kategori onay durumu
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="adminNotes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Admin Notları</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Yöneticiler için özel notlar..."
                              rows={4}
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Bu notlar sadece yöneticiler tarafından görülür
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Admin Özellikleri Özeti */}
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <h4 className="font-medium text-sm mb-2">Durum Özeti</h4>
                      <div className="flex flex-wrap gap-2">
                        {form.watch("isPromoted") && (
                          <Badge variant="default">Öne Çıkarılmış</Badge>
                        )}
                        {form.watch("isFeatured") && (
                          <Badge variant="secondary">Vitrin</Badge>
                        )}
                        <Badge
                          variant={
                            form.watch("approvalStatus") === ApprovalStatus.ONAYLANDI ? "default" :
                            form.watch("approvalStatus") === ApprovalStatus.BEKLEMEDE ? "secondary" :
                            "destructive"
                          }
                        >
                          {ApprovalStatusLabels[form.watch("approvalStatus") as ApprovalStatus]}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
            </fieldset>

            {/* FORM ACTIONS */}
            <Separator />
            <div className="flex justify-between items-center pt-4">
              <div className="text-sm text-gray-500">
                {form.formState.isSubmitting ? (
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    {isEditing ? "Kategori güncelleniyor..." : "Kategori oluşturuluyor..."}
                  </div>
                ) : (
                  isEditing ? "Kategori bilgilerini düzenleyin" : "Yeni kategori bilgilerini girin"
                )}
              </div>
              <div className="flex gap-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  disabled={form.formState.isSubmitting}
                >
                  <X className="h-4 w-4 mr-2" />
                  İptal
                </Button>
                <Button
                  type="submit"
                  disabled={form.formState.isSubmitting || loading}
                  className="bg-orange-500 hover:bg-orange-600 disabled:opacity-50"
                >
                  {(form.formState.isSubmitting || loading) && (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  )}
                  {!(form.formState.isSubmitting || loading) && (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  {isEditing ? "Güncelle" : "Oluştur"}
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>

    {/* Banner Form Modal */}
    {isEditing && category && (
      <CategoryBannerForm
        category={category}
        banner={editingBanner}
        open={showBannerForm}
        onOpenChange={handleBannerFormClose}
        onSuccess={handleBannerFormSuccess}
      />
    )}
  </>
  )
}
