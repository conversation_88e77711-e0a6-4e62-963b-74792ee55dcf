'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Package, 
  Search, 
  Plus,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  BarChart3,
  Loader2
} from 'lucide-react'

// Import our components
import StockMovementModal from './StockMovementModal'
import StockSummaryCard from './StockSummaryCard'
import StockHistoryTable from './StockHistoryTable'
import { StockDisplay } from '@/components/common/StockDisplay'
import { useStock } from '@/hooks/useStock'
import ClientOnly from '@/components/common/ClientOnly'

// Types
interface Category {
  id: string
  name: string
  slug: string
  icon?: string
  productCount?: number
}

interface Product {
  id: string
  name: string
  slug: string
  categoryId: string
  stockQuantity: number
  minStockThreshold: number
  maxStockCapacity: number
  stockStatus: 'IN_STOCK' | 'LOW_STOCK' | 'OUT_OF_STOCK'
  trackStock: boolean
}

interface StockData {
  product: Product & {
    stockIndicators: {
      isLowStock: boolean
      isOutOfStock: boolean
      isOverCapacity: boolean
      stockPercentage: number | null
      daysUntilReorder: number | null
    }
  }
  recentMovements: any[]
  statistics: {
    totalMovements: number
    netChange: number
    movementsByType: Array<{
      type: string
      totalQuantity: number
      count: number
    }>
  }
}

interface StockManagementPageProps {
  productId?: string
  className?: string
}

const StockManagementPage: React.FC<StockManagementPageProps> = ({
  productId,
  className = ''
}) => {
  // Hydration-safe state initialization
  const [isClient, setIsClient] = useState(false)

  // State management
  const [categories, setCategories] = useState<Category[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [stockData, setStockData] = useState<StockData | null>(null)

  // Loading states
  const [isLoadingCategories, setIsLoadingCategories] = useState(false)
  const [isLoadingProducts, setIsLoadingProducts] = useState(false)
  const [isLoadingStock, setIsLoadingStock] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Modal states
  const [showStockModal, setShowStockModal] = useState(false)

  // Quick stock entry - Initialize with safe defaults
  const [quickStockQuantity, setQuickStockQuantity] = useState<number>(1)
  const [quickStockInput, setQuickStockInput] = useState<string>('1')
  const [isSubmittingQuickStock, setIsSubmittingQuickStock] = useState(false)

  const [activeTab, setActiveTab] = useState('overview')

  // Client-side hydration check
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Helper functions for stock input handling
  const getMaxAvailableCapacity = () => {
    if (!stockData?.product) return Infinity
    const maxCapacity = stockData.product.maxStockCapacity || 0
    const currentStock = stockData.product.stockQuantity || 0
    return maxCapacity > 0 ? Math.max(0, maxCapacity - currentStock) : Infinity
  }

  const validateStockInput = (value: string): { isValid: boolean; numericValue: number; error?: string } => {
    // Allow empty string during typing
    if (value === '') {
      return { isValid: true, numericValue: 0 }
    }

    // Check if it's a valid number
    const numericValue = parseInt(value, 10)
    if (isNaN(numericValue)) {
      return { isValid: false, numericValue: 0, error: 'Geçerli bir sayı giriniz' }
    }

    // Check minimum value
    if (numericValue < 1) {
      return { isValid: false, numericValue: 0, error: 'Minimum 1 adet girilmelidir' }
    }

    // Check maximum capacity
    const maxAvailable = getMaxAvailableCapacity()
    if (maxAvailable !== Infinity && numericValue > maxAvailable) {
      return {
        isValid: false,
        numericValue: maxAvailable,
        error: `Maksimum ${maxAvailable} adet eklenebilir`
      }
    }

    return { isValid: true, numericValue }
  }

  const handleStockInputChange = (value: string) => {
    setQuickStockInput(value)

    const validation = validateStockInput(value)

    if (validation.isValid && validation.numericValue > 0) {
      setQuickStockQuantity(validation.numericValue)
      setError(null) // Clear any previous errors
    } else if (validation.error) {
      // Don't update quantity for invalid values, but show error
      if (value !== '') { // Don't show error for empty input
        setError(validation.error)
      }
    }
  }

  const handleStockInputBlur = () => {
    // On blur, ensure we have a valid value
    if (quickStockInput === '' || parseInt(quickStockInput, 10) < 1) {
      setQuickStockInput('1')
      setQuickStockQuantity(1)
      setError(null)
    } else {
      const validation = validateStockInput(quickStockInput)
      if (!validation.isValid && validation.numericValue > 0) {
        // Auto-correct to maximum available
        setQuickStockInput(validation.numericValue.toString())
        setQuickStockQuantity(validation.numericValue)
        setError(null)
      }
    }
  }

  const handleStockSpinnerChange = (increment: boolean) => {
    const currentValue = quickStockQuantity
    const newValue = increment ? currentValue + 1 : Math.max(1, currentValue - 1)

    const validation = validateStockInput(newValue.toString())
    if (validation.isValid) {
      setQuickStockInput(newValue.toString())
      setQuickStockQuantity(newValue)
      setError(null)
    }
  }

  // Load categories
  const loadCategories = async () => {
    setIsLoadingCategories(true)
    setError(null)

    try {
      const response = await fetch('/api/categories?isActive=true&limit=100')
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Kategoriler yüklenemedi')
      }

      const categoriesData = Array.isArray(result.data) ? result.data : []
      setCategories(categoriesData)

    } catch (error: any) {
      console.error('Load categories error:', error)
      setError(error.message)
    } finally {
      setIsLoadingCategories(false)
    }
  }

  // Load products by category
  const loadProductsByCategory = async (categoryId: string) => {
    setIsLoadingProducts(true)
    setProducts([])
    setSelectedProduct(null)
    setStockData(null)

    try {
      const response = await fetch(`/api/products?categoryId=${categoryId}&trackStock=true&limit=100`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Ürünler yüklenemedi')
      }

      // Handle different API response structures
      let productsData = []
      if (Array.isArray(result.data)) {
        productsData = result.data
      } else if (Array.isArray(result.data?.data)) {
        productsData = result.data.data
      } else if (Array.isArray(result.products)) {
        productsData = result.products
      } else if (result.data && typeof result.data === 'object') {
        // If data is an object, check for common array properties
        const possibleArrays = ['products', 'items', 'results']
        for (const prop of possibleArrays) {
          if (Array.isArray(result.data[prop])) {
            productsData = result.data[prop]
            break
          }
        }
      }

      setProducts(productsData)

      // If productId is provided and matches category, select that product
      if (productId && productsData.length > 0) {
        const product = productsData.find((p: Product) => p.id === productId)
        if (product) {
          setSelectedProduct(product)
          loadStockData(product.id)
        }
      }

    } catch (error: any) {
      console.error('Load products error:', error)
      setError(error.message)
    } finally {
      setIsLoadingProducts(false)
    }
  }

  // Load stock data for selected product
  const loadStockData = async (productId: string) => {
    setIsLoadingStock(true)
    try {
      const response = await fetch(`/api/products/${productId}/stock`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Stok bilgileri yüklenemedi')
      }

      setStockData(result.data)

    } catch (error: any) {
      console.error('Load stock data error:', error)
      setError(error.message)
    } finally {
      setIsLoadingStock(false)
    }
  }

  // Quick stock entry
  const handleQuickStockEntry = async () => {
    if (!selectedProduct || quickStockQuantity <= 0) return

    // Frontend validation for capacity
    if (stockData?.product) {
      const currentStock = stockData.product.stockQuantity || 0
      const maxCapacity = stockData.product.maxStockCapacity || 0
      const newStock = currentStock + quickStockQuantity

      if (maxCapacity > 0 && newStock > maxCapacity) {
        const availableCapacity = maxCapacity - currentStock
        setError(`Kapasite aşımı! Mevcut stok: ${currentStock}, Maksimum kapasite: ${maxCapacity}. Eklenebilir maksimum: ${availableCapacity} adet`)
        return
      }
    }

    setIsSubmittingQuickStock(true)
    try {
      const response = await fetch('/api/stock-movements', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          productId: selectedProduct.id,
          movementType: 'in',
          quantity: quickStockQuantity,
          reason: 'purchase',
          notes: 'Hızlı stok girişi',
          createdBy: 'admin'
        })
      })

      const result = await response.json()

      if (!response.ok) {
        // Handle capacity error specifically
        if (result.validation) {
          setError(`${result.error}: ${result.details}`)
        } else {
          setError(result.details || result.error || 'Stok girişi başarısız')
        }
        return
      }

      // Refresh data
      await loadStockData(selectedProduct.id)
      if (selectedCategory) {
        await loadProductsByCategory(selectedCategory.id)
      }

      // Reset form
      setQuickStockQuantity(1)
      setQuickStockInput('1')

    } catch (error: any) {
      console.error('Quick stock entry error:', error)
      setError(error.message)
    } finally {
      setIsSubmittingQuickStock(false)
    }
  }

  // Initial load - only after client hydration
  useEffect(() => {
    if (isClient) {
      loadCategories()
    }
  }, [isClient])

  // Handle category selection
  const handleCategorySelect = (category: Category) => {
    setSelectedCategory(category)
    setSelectedProduct(null)
    setStockData(null)
    setActiveTab('overview')
    loadProductsByCategory(category.id)
  }

  // Handle product selection
  const handleProductSelect = (product: Product) => {
    setSelectedProduct(product)
    setStockData(null)
    loadStockData(product.id)
    setActiveTab('overview')
  }

  // Handle stock movement success
  const handleStockMovementSuccess = () => {
    if (selectedProduct && selectedCategory) {
      loadStockData(selectedProduct.id)
      // Refresh products list to update stock quantities
      loadProductsByCategory(selectedCategory.id)
    }
  }

  // Get stock status counts for selected category
  const getStockStatusCounts = () => {
    if (!Array.isArray(products)) {
      return { total: 0, inStock: 0, lowStock: 0, outOfStock: 0 }
    }

    const counts = {
      total: products.length,
      inStock: products.filter(p => p.stockStatus === 'IN_STOCK').length,
      lowStock: products.filter(p => p.stockStatus === 'LOW_STOCK').length,
      outOfStock: products.filter(p => p.stockStatus === 'OUT_OF_STOCK').length
    }
    return counts
  }

  const statusCounts = getStockStatusCounts()

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Stok Yönetimi</h1>
          <p className="text-gray-600">
            {selectedCategory ? `${selectedCategory.name} kategorisindeki ürünleri yönetin` : 'Kategori seçerek başlayın'}
          </p>
        </div>

        {selectedProduct && (
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              onClick={() => setShowStockModal(true)}
              className="flex items-center gap-2"
            >
              <BarChart3 className="h-4 w-4" />
              Detaylı Hareket
            </Button>
          </div>
        )}
      </div>

      {/* Stock Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Package className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Toplam Ürün</p>
                <p className="text-2xl font-bold">{statusCounts.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <TrendingUp className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Stokta Var</p>
                <p className="text-2xl font-bold text-green-600">{statusCounts.inStock}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Düşük Stok</p>
                <p className="text-2xl font-bold text-orange-600">{statusCounts.lowStock}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-100 rounded-lg">
                <TrendingDown className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Stokta Yok</p>
                <p className="text-2xl font-bold text-red-600">{statusCounts.outOfStock}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Hierarchical Selection Flow */}
      <div className="space-y-6">
        {/* Step 1: Category Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <div className="flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 rounded-full text-sm font-bold">1</div>
              Kategori Seçimi
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoadingCategories ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-5 w-5 animate-spin mr-2" />
                <span>Kategoriler yükleniyor...</span>
              </div>
            ) : (
              <Select
                value={selectedCategory?.id || undefined}
                onValueChange={(value) => {
                  const category = categories.find(c => c.id === value)
                  if (category) handleCategorySelect(category)
                }}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Kategori seçin..." />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      <div className="flex items-center gap-2">
                        {category.icon && <span>{category.icon}</span>}
                        <span>{category.name}</span>
                        {category.productCount && (
                          <Badge variant="secondary" className="ml-auto">
                            {category.productCount}
                          </Badge>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </CardContent>
        </Card>

        {/* Step 2: Product Selection */}
        {selectedCategory && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 rounded-full text-sm font-bold">2</div>
                Ürün Seçimi
                <Badge variant="outline" className="ml-auto">
                  {selectedCategory.name}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoadingProducts ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="h-5 w-5 animate-spin mr-2" />
                  <span>Ürünler yükleniyor...</span>
                </div>
              ) : products.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Bu kategoride stok takibi yapılan ürün bulunamadı</p>
                </div>
              ) : (
                <Select
                  value={selectedProduct?.id || undefined}
                  onValueChange={(value) => {
                    const product = products.find(p => p.id === value)
                    if (product) handleProductSelect(product)
                  }}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Ürün seçin..." />
                  </SelectTrigger>
                  <SelectContent>
                    {products.map((product) => (
                      <SelectItem key={product.id} value={product.id}>
                        <div className="flex items-center justify-between w-full">
                          <span>{product.name}</span>
                          <div className="flex items-center gap-2 ml-4">
                            <Badge
                              variant={
                                product.stockStatus === 'IN_STOCK' ? 'success' :
                                product.stockStatus === 'LOW_STOCK' ? 'warning' : 'destructive'
                              }
                              className="text-xs"
                            >
                              {product.stockQuantity || 0}
                            </Badge>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </CardContent>
          </Card>
        )}

        {/* Step 3: Stock Information */}
        {selectedProduct && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 rounded-full text-sm font-bold">3</div>
                Mevcut Stok Bilgisi
                <Badge variant="outline" className="ml-auto">
                  {selectedProduct.name}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoadingStock ? (
                <div className="flex items-center justify-center py-4">
                  <Loader2 className="h-5 w-5 animate-spin mr-2" />
                  <span>Stok bilgileri yükleniyor...</span>
                </div>
              ) : stockData ? (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {stockData.product.stockQuantity || 0}
                    </div>
                    <div className="text-sm text-gray-600">Mevcut Stok</div>
                  </div>
                  <div className="text-center p-4 bg-orange-50 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">
                      {stockData.product.minStockThreshold || 0}
                    </div>
                    <div className="text-sm text-gray-600">Minimum Eşik</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {stockData.product.maxStockCapacity || '-'}
                    </div>
                    <div className="text-sm text-gray-600">Maksimum Kapasite</div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-4 text-gray-500">
                  Stok bilgileri yüklenemedi
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Step 4: Quick Stock Entry */}
        {selectedProduct && stockData && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="flex items-center justify-center w-6 h-6 bg-green-100 text-green-600 rounded-full text-sm font-bold">4</div>
                Hızlı Stok Girişi
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ClientOnly fallback={
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-5 w-5 animate-spin mr-2" />
                  <span>Stok girişi yükleniyor...</span>
                </div>
              }>
              <div className="space-y-4">
                {/* Capacity Information */}
                {stockData.product.maxStockCapacity && stockData.product.maxStockCapacity > 0 && (
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-blue-700 font-medium">Kapasite Bilgisi:</span>
                      <span className="text-blue-600">
                        {stockData.product.stockQuantity || 0} / {stockData.product.maxStockCapacity}
                        ({Math.max(0, stockData.product.maxStockCapacity - (stockData.product.stockQuantity || 0))} boş)
                      </span>
                    </div>
                  </div>
                )}

                <div className="flex items-center gap-4">
                  <div className="flex-1">
                    <Label htmlFor="quickStock">Eklenecek Miktar</Label>
                    <div className="relative">
                      <Input
                        id="quickStock"
                        type="number"
                        min="1"
                        max={getMaxAvailableCapacity() !== Infinity ? getMaxAvailableCapacity() : undefined}
                        value={quickStockInput}
                        onChange={(e) => handleStockInputChange(e.target.value)}
                        onBlur={handleStockInputBlur}
                        onKeyDown={(e) => {
                          // Handle Enter key
                          if (e.key === 'Enter') {
                            e.preventDefault()
                            handleStockInputBlur()
                            // Optionally trigger stock entry
                            if (quickStockQuantity > 0) {
                              handleQuickStockEntry()
                            }
                          }
                          // Handle up/down arrow keys for better spinner support
                          else if (e.key === 'ArrowUp') {
                            e.preventDefault()
                            handleStockSpinnerChange(true)
                          }
                          else if (e.key === 'ArrowDown') {
                            e.preventDefault()
                            handleStockSpinnerChange(false)
                          }
                        }}
                        className="mt-1 pr-12 hide-number-spinners"
                        placeholder="Miktar giriniz"
                      />

                      {/* Custom Spinner Buttons */}
                      <div className="absolute right-1 top-1 bottom-1 flex flex-col">
                        <button
                          type="button"
                          onClick={() => handleStockSpinnerChange(true)}
                          disabled={getMaxAvailableCapacity() !== Infinity && quickStockQuantity >= getMaxAvailableCapacity()}
                          className="custom-spinner-button rounded-t border-b border-gray-200"
                          title="Artır"
                        >
                          ▲
                        </button>
                        <button
                          type="button"
                          onClick={() => handleStockSpinnerChange(false)}
                          disabled={quickStockQuantity <= 1}
                          className="custom-spinner-button rounded-b"
                          title="Azalt"
                        >
                          ▼
                        </button>
                      </div>
                    </div>
                    {/* Input feedback */}
                    <div className="text-xs mt-1">
                      {stockData.product.maxStockCapacity && stockData.product.maxStockCapacity > 0 ? (
                        <span className="text-gray-500">
                          Maksimum eklenebilir: <span className="font-medium">{getMaxAvailableCapacity()}</span> adet
                        </span>
                      ) : (
                        <span className="text-gray-500">
                          Kapasite sınırı yok
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="flex-1">
                    <Label>Yeni Toplam</Label>
                    <div className={`mt-1 p-2 rounded border text-center font-medium transition-colors ${
                      stockData.product.maxStockCapacity && stockData.product.maxStockCapacity > 0 &&
                      (stockData.product.stockQuantity || 0) + quickStockQuantity > stockData.product.maxStockCapacity
                        ? 'bg-red-50 border-red-200 text-red-600'
                        : quickStockQuantity > 0
                          ? 'bg-green-50 border-green-200 text-green-700'
                          : 'bg-gray-50 border-gray-200'
                    }`}>
                      {(stockData.product.stockQuantity || 0) + quickStockQuantity}
                      {stockData.product.maxStockCapacity && stockData.product.maxStockCapacity > 0 && (
                        <span className="text-xs opacity-75 ml-1">
                          / {stockData.product.maxStockCapacity}
                        </span>
                      )}
                      {quickStockQuantity > 0 && (
                        <div className="text-xs opacity-75 mt-1">
                          +{quickStockQuantity} eklenecek
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex flex-col gap-2">
                    <Button
                      onClick={handleQuickStockEntry}
                      disabled={
                        isSubmittingQuickStock ||
                        quickStockQuantity <= 0 ||
                        quickStockInput === '' ||
                        !validateStockInput(quickStockInput).isValid
                      }
                      className="flex items-center gap-2"
                    >
                      {isSubmittingQuickStock ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Plus className="h-4 w-4" />
                      )}
                      Stok Ekle
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Stock Overview Cards - Only show when category is selected */}
      {selectedCategory && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Package className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Toplam Ürün</p>
                  <p className="text-2xl font-bold">{statusCounts.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Stokta Var</p>
                  <p className="text-2xl font-bold text-green-600">{statusCounts.inStock}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <AlertTriangle className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Düşük Stok</p>
                  <p className="text-2xl font-bold text-orange-600">{statusCounts.lowStock}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-red-100 rounded-lg">
                  <TrendingDown className="h-5 w-5 text-red-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600">Stokta Yok</p>
                  <p className="text-2xl font-bold text-red-600">{statusCounts.outOfStock}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <div className="grid grid-cols-1 gap-6">

        {/* Detailed Product Information - Only show when product is selected */}
        {selectedProduct && stockData && (
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="overview">Genel Bakış</TabsTrigger>
              <TabsTrigger value="history">Stok Geçmişi</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <StockSummaryCard
                product={stockData.product}
                stockIndicators={stockData.product.stockIndicators}
                statistics={stockData.statistics}
                onStockMovement={() => setShowStockModal(true)}
                onViewHistory={() => setActiveTab('history')}
              />
            </TabsContent>

            <TabsContent value="history">
              <StockHistoryTable productId={selectedProduct.id} />
            </TabsContent>
          </Tabs>
        )}
      </div>

      {/* Stock Movement Modal */}
      <StockMovementModal
        isOpen={showStockModal}
        onClose={() => setShowStockModal(false)}
        product={selectedProduct}
        onSuccess={handleStockMovementSuccess}
      />
    </div>
  )
}

export default StockManagementPage
