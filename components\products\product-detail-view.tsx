"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { ProductImageGallery } from "./product-image-gallery"
import { ProductQuantitySelector } from "./product-quantity-selector"
import { ProductSpecifications } from "./product-specifications"
import { ProductCertificates } from "./product-certificates"
import { RelatedProducts } from "./related-products"
import { ProductBreadcrumb } from "./product-breadcrumb"
import { ProductReviews } from "./product-reviews"
import { useCartStore } from "@/lib/stores/cart-store"
import { formatCurrency } from "@/lib/utils"
import type { Product } from "@/types"
import { ShoppingCart, Heart, Share2, Truck, Shield, Award, Star, Check } from "lucide-react"
import { toast } from "sonner"

interface ProductDetailViewProps {
  product: Product
}

export function ProductDetailView({ product }: ProductDetailViewProps) {
  const [quantity, setQuantity] = useState(1)
  const [isFavorite, setIsFavorite] = useState(false)
  const [isAddingToCart, setIsAddingToCart] = useState(false)

  const { addItem, setIsOpen, getItemQuantity } = useCartStore()
  const currentCartQuantity = getItemQuantity(product.id)

  const handleAddToCart = async () => {
    setIsAddingToCart(true)

    try {
      // Check stock availability
      if (currentCartQuantity + quantity > product.stock) {
        toast.error("Yetersiz stok!", {
          description: `Sepetinizde zaten ${currentCartQuantity} adet var. Maksimum ${product.stock} adet ekleyebilirsiniz.`,
        })
        return
      }

      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 800))

      // Add to cart
      addItem(product, quantity)

      toast.success(`${product.name} sepete eklendi!`, {
        description: `${quantity} adet ürün sepetinize eklendi.`,
        action: {
          label: "Sepeti Görüntüle",
          onClick: () => setIsOpen(true),
        },
      })

      // Reset quantity to 1 after adding
      setQuantity(1)
    } catch (error) {
      toast.error("Ürün sepete eklenirken bir hata oluştu.")
    } finally {
      setIsAddingToCart(false)
    }
  }

  const handleToggleFavorite = () => {
    setIsFavorite(!isFavorite)

    if (!isFavorite) {
      toast.success("Ürün favorilere eklendi!")
    } else {
      toast.info("Ürün favorilerden çıkarıldı.")
    }

    // TODO: Implement favorite functionality with API
  }

  const handleShare = async () => {
    const shareData = {
      title: product.name,
      text: product.shortDescription,
      url: window.location.href,
    }

    if (navigator.share && navigator.canShare(shareData)) {
      try {
        await navigator.share(shareData)
      } catch (error) {
        // User cancelled sharing
      }
    } else {
      // Fallback: copy to clipboard
      try {
        await navigator.clipboard.writeText(window.location.href)
        toast.success("Ürün linki kopyalandı!")
      } catch (error) {
        toast.error("Link kopyalanırken bir hata oluştu.")
      }
    }
  }

  const discountPercentage = product.originalPrice
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    : 0

  const isInStock = product.stockStatus === "in_stock"
  const isLowStock = product.stockStatus === "low_stock"
  const isOutOfStock = product.stockStatus === "out_of_stock"
  const availableStock = product.stock - currentCartQuantity

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <ProductBreadcrumb product={product} />

        {/* Main Product Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Image Gallery */}
          <div className="space-y-4">
            <ProductImageGallery images={product.images} productName={product.name} />
          </div>

          {/* Product Info */}
          <div className="space-y-6">
            {/* Title and Brand */}
            <div>
              <div className="flex items-center gap-2 mb-2">
                <Badge variant="secondary">{product.brand}</Badge>
                {isInStock && (
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    <Check className="w-3 h-3 mr-1" />
                    Stokta
                  </Badge>
                )}
                {isLowStock && <Badge variant="destructive">Son {product.stock} Adet</Badge>}
                {isOutOfStock && <Badge variant="destructive">Stokta Yok</Badge>}
              </div>
              <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-2">{product.name}</h1>
              <p className="text-gray-600 mb-4">{product.shortDescription}</p>

              {/* Product Code */}
              {product.sku && (
                <p className="text-sm text-gray-500 mb-4">
                  Ürün Kodu: <span className="font-mono">{product.sku}</span>
                </p>
              )}

              {/* Badges */}
              <div className="flex flex-wrap gap-2 mb-4">
                {product.isNew && (
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    Yeni
                  </Badge>
                )}
                {product.isFeatured && (
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    Öne Çıkan
                  </Badge>
                )}
                {discountPercentage > 0 && <Badge variant="destructive">%{discountPercentage} İndirim</Badge>}
              </div>
            </div>

            {/* Rating */}
            <div className="flex items-center gap-2">
              <div className="flex items-center">
                {Array.from({ length: 5 }).map((_, i) => (
                  <Star
                    key={i}
                    className={`w-5 h-5 ${
                      i < Math.floor(product.rating || 0) ? "text-yellow-400 fill-current" : "text-gray-300"
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm text-gray-600">
                {product.rating ? product.rating.toFixed(1) : "0.0"} ({product.reviewCount || 0} değerlendirme)
              </span>
            </div>

            {/* Price */}
            <div className="space-y-2">
              <div className="flex items-baseline gap-3">
                <span className="text-3xl font-bold text-gray-900">{formatCurrency(product.price)}</span>
                {product.originalPrice && product.originalPrice > product.price && (
                  <span className="text-lg text-gray-500 line-through">{formatCurrency(product.originalPrice)}</span>
                )}
                {discountPercentage > 0 && <Badge variant="destructive">%{discountPercentage} İndirim</Badge>}
              </div>
              <p className="text-sm text-gray-600">KDV Dahil • Ücretsiz Kargo</p>
            </div>

            {/* Cart Info */}
            {currentCartQuantity > 0 && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-blue-800 font-medium">Sepetinizde {currentCartQuantity} adet var</p>
                <p className="text-blue-600 text-sm">Kalan stok: {availableStock} adet</p>
              </div>
            )}

            {/* Quantity and Actions */}
            <div className="space-y-4">
              <ProductQuantitySelector
                quantity={quantity}
                onQuantityChange={setQuantity}
                maxQuantity={availableStock}
                disabled={isOutOfStock}
              />

              <div className="flex gap-3">
                <Button
                  onClick={handleAddToCart}
                  disabled={isOutOfStock || isAddingToCart || availableStock <= 0}
                  className="flex-1"
                  size="lg"
                >
                  <ShoppingCart className="w-5 h-5 mr-2" />
                  {isAddingToCart ? "Ekleniyor..." : "Sepete Ekle"}
                </Button>

                <Button
                  variant="outline"
                  size="lg"
                  onClick={handleToggleFavorite}
                  className={isFavorite ? "text-red-600 border-red-600" : ""}
                >
                  <Heart className={`w-5 h-5 ${isFavorite ? "fill-current" : ""}`} />
                </Button>

                <Button variant="outline" size="lg" onClick={handleShare}>
                  <Share2 className="w-5 h-5" />
                </Button>
              </div>

              {isOutOfStock && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-red-800 font-medium">Bu ürün şu anda stokta bulunmamaktadır.</p>
                  <p className="text-red-600 text-sm mt-1">
                    Stoka girdiğinde haberdar olmak için favorilerinize ekleyin.
                  </p>
                </div>
              )}

              {availableStock <= 0 && !isOutOfStock && (
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                  <p className="text-orange-800 font-medium">Sepetinizde maksimum stok miktarı bulunuyor.</p>
                </div>
              )}
            </div>

            {/* Features */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 pt-6 border-t">
              <div className="flex items-center gap-3">
                <Truck className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="font-medium text-sm">Ücretsiz Kargo</p>
                  <p className="text-xs text-gray-600">500₺ üzeri</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Shield className="w-5 h-5 text-green-600" />
                <div>
                  <p className="font-medium text-sm">Güvenli Ödeme</p>
                  <p className="text-xs text-gray-600">SSL korumalı</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Award className="w-5 h-5 text-purple-600" />
                <div>
                  <p className="font-medium text-sm">CE Sertifikalı</p>
                  <p className="text-xs text-gray-600">Avrupa standartları</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <Tabs defaultValue="description" className="mb-12">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="description">Açıklama</TabsTrigger>
            <TabsTrigger value="specifications">Özellikler</TabsTrigger>
            <TabsTrigger value="certificates">Sertifikalar</TabsTrigger>
            <TabsTrigger value="reviews">Yorumlar ({product.reviewCount || 0})</TabsTrigger>
          </TabsList>

          <TabsContent value="description" className="mt-6">
            <div className="bg-white rounded-lg p-6">
              <div className="prose max-w-none">
                <div dangerouslySetInnerHTML={{ __html: product.description }} />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="specifications" className="mt-6">
            <ProductSpecifications specifications={product.specifications} />
          </TabsContent>

          <TabsContent value="certificates" className="mt-6">
            <ProductCertificates certificates={product.certificates} />
          </TabsContent>

          <TabsContent value="reviews" className="mt-6">
            <ProductReviews productId={product.id} />
          </TabsContent>
        </Tabs>

        {/* Related Products */}
        <RelatedProducts categoryId={product.categoryId} currentProductId={product.id} />
      </div>
    </div>
  )
}
