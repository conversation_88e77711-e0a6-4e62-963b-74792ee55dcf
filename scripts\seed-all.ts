#!/usr/bin/env tsx

/**
 * Comprehensive Database Seeder
 * 
 * This script seeds the database with:
 * 1. Categories (if not already present)
 * 2. Sample products with enhanced pricing structure
 * 3. Stock management data
 * 4. Sample discounts for testing
 * 
 * Usage:
 * npm run seed:all
 * or
 * npx tsx scripts/seed-all.ts
 */

import { PrismaClient } from '@prisma/client'
import { seedProducts } from '../prisma/seed-products'

const prisma = new PrismaClient()

// Categories data (in case they don't exist)
const categories = [
  {
    name: "Baş Koruma",
    slug: "bas-koruma",
    description: "Güvenlik baretleri ve kaskları",
    sortOrder: 1
  },
  {
    name: "<PERSON><PERSON>z Korum<PERSON>", 
    slug: "goz-koruma",
    description: "Güvenlik gözlükleri ve yüz koruyucuları",
    sortOrder: 2
  },
  {
    name: "<PERSON><PERSON> Koruma",
    slug: "kulak-koruma", 
    description: "Kulaklıklar ve kulak tıkaçları",
    sortOrder: 3
  },
  {
    name: "<PERSON><PERSON><PERSON> Koruma",
    slug: "solunum-koruma",
    description: "Maskeler ve solunum cihazları", 
    sortOrder: 4
  },
  {
    name: "El Koruma",
    slug: "el-koruma",
    description: "İş eldivenleri ve el koruyucuları",
    sortOrder: 5
  },
  {
    name: "Ayak Koruma",
    slug: "ayak-koruma",
    description: "Güvenlik ayakkabıları ve botları",
    sortOrder: 6
  },
  {
    name: "Vücut Koruma", 
    slug: "vucut-koruma",
    description: "İş kıyafetleri ve koruyucu tulumlar",
    sortOrder: 7
  },
  {
    name: "Yüksekte Çalışma",
    slug: "yuksekte-calisma",
    description: "Emniyet kemerleri ve yüksekte çalışma ekipmanları",
    sortOrder: 8
  }
]

async function seedCategories() {
  console.log('🏷️  Checking categories...')
  
  const existingCategories = await prisma.category.findMany()
  
  if (existingCategories.length > 0) {
    console.log(`✅ Found ${existingCategories.length} existing categories, skipping category seeding.`)
    return
  }
  
  console.log('🌱 Creating categories...')
  
  for (const categoryData of categories) {
    try {
      const category = await prisma.category.create({
        data: {
          name: categoryData.name,
          slug: categoryData.slug,
          description: categoryData.description,
          icon: '',
          parentId: null,
          productCount: 0,
          isActive: true,
          sortOrder: categoryData.sortOrder,
          seoTitle: `${categoryData.name} - İş Güvenliği Ekipmanları`,
          seoDescription: `${categoryData.description}. Kaliteli ve güvenilir ${categoryData.name.toLowerCase()} ürünleri.`,
          
          // Additional fields with defaults
          cacheKey: `cat_${categoryData.slug}_tr`,
          viewCount: 0,
          popularityScore: 0,
          searchKeywords: categoryData.name.toLowerCase(),
          isSearchable: true,
          categoryImage: null,
          colorCode: '#3B82F6',
          iconType: 'SVG',
          metaKeywords: categoryData.name.toLowerCase(),
          ogTitle: `${categoryData.name} - İş Güvenliği`,
          ogDescription: categoryData.description,
          ogImage: null,
          conversionRate: null,
          avgOrderValue: null,
          minOrderAmount: null,
          commissionRate: null,
          taxRate: null,
          isPromoted: false,
          isFeatured: false,
          adminNotes: null,
          approvalStatus: 'ONAYLANDI',
          version: 1,
          changeLog: null,
          createdBy: null,
          mobileIcon: null,
          mobileTemplate: 'VARSAYILAN'
        }
      })
      
      console.log(`  ✅ Created category: ${category.name}`)
    } catch (error) {
      console.error(`  ❌ Error creating category ${categoryData.name}:`, error)
    }
  }
}

async function createAdminUser() {
  console.log('👤 Checking admin user...')
  
  const existingAdmin = await prisma.user.findFirst({
    where: { role: 'SUPER_ADMIN' }
  })
  
  if (existingAdmin) {
    console.log('✅ Admin user already exists, skipping.')
    return
  }
  
  console.log('🌱 Creating admin user...')
  
  const bcrypt = require('bcryptjs')
  const hashedPassword = await bcrypt.hash('admin123', 12)
  
  const admin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: hashedPassword,
      name: 'System Administrator',
      role: 'SUPER_ADMIN',
      isActive: true
    }
  })
  
  console.log(`✅ Created admin user: ${admin.email}`)
  console.log(`🔑 Login credentials: <EMAIL> / admin123`)
}

async function main() {
  console.log('🚀 Starting comprehensive database seeding...\n')
  
  try {
    // Step 1: Create admin user
    await createAdminUser()
    console.log('')
    
    // Step 2: Ensure categories exist
    await seedCategories()
    console.log('')
    
    // Step 3: Seed products with enhanced data
    await seedProducts()
    console.log('')
    
    // Step 4: Final statistics
    const stats = await prisma.$transaction([
      prisma.category.count(),
      prisma.product.count(),
      prisma.productDiscount.count(),
      prisma.user.count()
    ])
    
    console.log('📊 Seeding Statistics:')
    console.log(`  Categories: ${stats[0]}`)
    console.log(`  Products: ${stats[1]}`)
    console.log(`  Discounts: ${stats[2]}`)
    console.log(`  Users: ${stats[3]}`)
    console.log('')
    console.log('🎉 Database seeding completed successfully!')
    console.log('')
    console.log('🔗 Next steps:')
    console.log('  1. Start the development server: npm run dev')
    console.log('  2. Login with: <EMAIL> / admin123')
    console.log('  3. Visit the admin panel to manage products')
    console.log('  4. Test the new pricing system with discounts')
    
  } catch (error) {
    console.error('❌ Error during seeding:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the seeder
main()
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
