import { fetcher } from "@/lib/utils"
import type { Product, ProductImage, ProductCreateInput, ProductUpdateInput } from "@/types"

const BASE_URL = "/api/products"

class ProductService {
  async getAllProducts(params: URLSearchParams): Promise<{ data: Product[]; pagination: any }> {
    try {
      console.log("ProductService.getAllProducts called with params:", params.toString())
      const response = await fetcher(`${BASE_URL}?${params.toString()}`)
      console.log("ProductService.getAllProducts response:", response)

      // API returns { success: true, data: { data: Product[], pagination: {...} } }
      // We need to extract the inner data object
      if (response.success && response.data) {
        return response.data
      }

      return { data: [], pagination: { total: 0, page: 1, limit: 10, totalPages: 0 } }
    } catch (error) {
      console.error("ProductService.getAllProducts error:", error)
      return { data: [], pagination: { total: 0, page: 1, limit: 10, totalPages: 0 } }
    }
  }

  async getProducts(filters?: {
    limit?: number
    sortBy?: string
    sortOrder?: string
    isActive?: boolean
    isFeatured?: boolean
    category?: string
    brand?: string
    minPrice?: number
    maxPrice?: number
    search?: string
  }): Promise<{ data: Product[]; pagination: any }> {
    try {
      console.log("ProductService.getProducts called with filters:", filters)

      const params = new URLSearchParams()

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== "") {
            params.append(key, value.toString())
          }
        })
      }

      console.log("ProductService.getProducts API call:", `${BASE_URL}?${params.toString()}`)
      const response = await fetcher(`${BASE_URL}?${params.toString()}`)
      console.log("ProductService.getProducts response:", response)

      // Response formatını kontrol et
      if (response && typeof response === "object") {
        // Eğer response.data varsa onu kullan, yoksa response'un kendisini kullan
        if (response.data && Array.isArray(response.data)) {
          return response
        } else if (Array.isArray(response)) {
          return {
            data: response,
            pagination: { total: response.length, page: 1, limit: filters?.limit || 10, totalPages: 1 },
          }
        }
      }

      // Fallback
      return { data: [], pagination: { total: 0, page: 1, limit: filters?.limit || 10, totalPages: 0 } }
    } catch (error) {
      console.error("ProductService.getProducts error:", error)
      return { data: [], pagination: { total: 0, page: 1, limit: filters?.limit || 10, totalPages: 0 } }
    }
  }

  async getProductById(id: string): Promise<Product | null> {
    try {
      console.log("ProductService.getProductById called with id:", id)
      const response = await fetcher(`${BASE_URL}/${id}`)
      console.log("ProductService.getProductById response:", response)
      return response.data || response
    } catch (error) {
      console.error("ProductService.getProductById error:", error)
      return null
    }
  }

  async getBySlug(slug: string): Promise<Product | null> {
    try {
      console.log("ProductService.getBySlug called with slug:", slug)
      const response = await fetcher(`${BASE_URL}/slug/${slug}`)
      console.log("ProductService.getBySlug response:", response)
      return response.data || response
    } catch (error) {
      console.error("ProductService.getBySlug error:", error)
      return null
    }
  }

  async getAll(params?: URLSearchParams): Promise<{ data: Product[]; pagination: any }> {
    try {
      const url = params ? `${BASE_URL}?${params.toString()}` : BASE_URL
      console.log("ProductService.getAll called with URL:", url)
      const response = await fetcher(url)
      console.log("ProductService.getAll response:", response)
      return response
    } catch (error) {
      console.error("ProductService.getAll error:", error)
      return { data: [], pagination: { total: 0, page: 1, limit: 10, totalPages: 0 } }
    }
  }

  async createProduct(data: ProductCreateInput): Promise<{ success: boolean; message: string; data?: Product }> {
    try {
      console.log("ProductService.createProduct called with data:", data)
      const response = await fetcher(BASE_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })
      console.log("ProductService.createProduct response:", response)
      return response
    } catch (error) {
      console.error("ProductService.createProduct error:", error)
      return { success: false, message: "Ürün oluşturulurken hata oluştu" }
    }
  }

  async updateProduct(
    id: string,
    data: ProductUpdateInput,
  ): Promise<{ success: boolean; message: string; data?: Product }> {
    try {
      console.log("ProductService.updateProduct called with id:", id, "data:", data)
      const response = await fetcher(`${BASE_URL}/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })
      console.log("ProductService.updateProduct response:", response)
      return response
    } catch (error) {
      console.error("ProductService.updateProduct error:", error)
      return { success: false, message: "Ürün güncellenirken hata oluştu" }
    }
  }

  async deleteProduct(id: string): Promise<{ success: boolean; message: string }> {
    try {
      console.log("ProductService.deleteProduct called with id:", id)
      const response = await fetcher(`${BASE_URL}/${id}`, {
        method: "DELETE",
      })
      console.log("ProductService.deleteProduct response:", response)
      return response
    } catch (error) {
      console.error("ProductService.deleteProduct error:", error)
      return { success: false, message: "Ürün silinirken hata oluştu" }
    }
  }

  async uploadImages(files: File[]): Promise<{ success: boolean; message: string; images?: ProductImage[] }> {
    try {
      console.log("ProductService.uploadImages called with files:", files.length)
      const formData = new FormData()
      files.forEach((file) => {
        formData.append("files", file)
      })

      const response = await fetcher("/api/upload", {
        method: "POST",
        body: formData,
      })
      console.log("ProductService.uploadImages response:", response)

      // Transform response to match expected format
      if (response.success && response.data) {
        const images: ProductImage[] = response.data.map((item: any) => ({
          id: item.id,
          url: item.url,
          alt: item.alt,
          title: item.title,
          sortOrder: item.sortOrder,
          isMain: item.isMain,
          size: item.size,
          width: item.width,
          height: item.height,
          format: item.format,
        }))

        return {
          success: true,
          message: response.message,
          images: images
        }
      }

      return response
    } catch (error) {
      console.error("ProductService.uploadImages error:", error)
      return { success: false, message: "Görseller yüklenirken hata oluştu" }
    }
  }

  async updateProductBulk(
    productIds: string[],
    updates: Partial<ProductUpdateInput>,
  ): Promise<{ success: boolean; message: string }> {
    try {
      console.log("ProductService.updateProductBulk called with ids:", productIds, "updates:", updates)
      const response = await fetcher(`${BASE_URL}/bulk-update`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ids: productIds, updates }),
      })
      console.log("ProductService.updateProductBulk response:", response)
      return response
    } catch (error) {
      console.error("ProductService.updateProductBulk error:", error)
      return { success: false, message: "Toplu güncelleme sırasında hata oluştu" }
    }
  }

  async deleteProductBulk(productIds: string[]): Promise<{ success: boolean; message: string }> {
    try {
      console.log("ProductService.deleteProductBulk called with ids:", productIds)
      const response = await fetcher(`${BASE_URL}/bulk-delete`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ids: productIds }),
      })
      console.log("ProductService.deleteProductBulk response:", response)
      return response
    } catch (error) {
      console.error("ProductService.deleteProductBulk error:", error)
      return { success: false, message: "Toplu silme sırasında hata oluştu" }
    }
  }

  async getBrands(): Promise<string[]> {
    try {
      console.log("ProductService.getBrands called")
      const response = await fetcher(`${BASE_URL}/brands`)
      console.log("ProductService.getBrands response:", response)
      return response.data || response || []
    } catch (error) {
      console.error("ProductService.getBrands error:", error)
      return []
    }
  }
}

// Default export olarak instance'ı export ediyoruz
export default new ProductService()

// Named export olarak da sınıfı export ediyoruz
export { ProductService }
