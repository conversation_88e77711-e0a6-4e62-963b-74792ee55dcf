import { Suspense } from "react"
import type { Metadata } from "next"
import ProductGrid from "@/components/products/product-grid"
import ProductFilters from "@/components/products/product-filters"
import ProductSort from "@/components/products/product-sort"
import ProductSearch from "@/components/products/product-search"
import { Skeleton } from "@/components/ui/skeleton"

export const metadata: Metadata = {
  title: "Ürünler | İş Güvenliği Mağazası",
  description:
    "İş güvenliği ürünleri, kişisel koruyucu donanımlar ve güvenlik ekipmanları. En uygun fiyatlarla kaliteli ürünler.",
  keywords: "i<PERSON> gü<PERSON>ğ<PERSON>, kiş<PERSON>l koruyucu donanım, gü<PERSON>lik ekipmanları, iş kıyafetleri",
}

interface ProductsPageProps {
  searchParams: {
    search?: string
    category?: string
    brand?: string
    minPrice?: string
    maxPrice?: string
    inStock?: string
    sort?: string
    page?: string
  }
}

export default function ProductsPage({ searchParams }: ProductsPageProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Başlık */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Ürünler</h1>
          <p className="text-gray-600">İş güvenliği ve kişisel koruyucu donanım ürünleri</p>
        </div>

        {/* Arama */}
        <div className="mb-6">
          <Suspense fallback={<Skeleton className="h-12 w-full max-w-md" />}>
            <ProductSearch initialValue={searchParams.search} />
          </Suspense>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Filtreler - Sol Sidebar */}
          <aside className="lg:w-64 flex-shrink-0">
            <div className="bg-white rounded-lg shadow-sm border p-6 sticky top-4">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Filtreler</h2>
              <Suspense
                fallback={
                  <div className="space-y-4">
                    <Skeleton className="h-8 w-full" />
                    <Skeleton className="h-32 w-full" />
                    <Skeleton className="h-24 w-full" />
                  </div>
                }
              >
                <ProductFilters searchParams={searchParams} />
              </Suspense>
            </div>
          </aside>

          {/* Ana İçerik */}
          <main className="flex-1">
            {/* Sıralama */}
            <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="text-sm text-gray-600">Ürünler gösteriliyor</div>
                <Suspense fallback={<Skeleton className="h-10 w-48" />}>
                  <ProductSort currentSort={searchParams.sort} />
                </Suspense>
              </div>
            </div>

            {/* Ürün Grid */}
            <Suspense
              fallback={
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {Array.from({ length: 12 }).map((_, i) => (
                    <div key={i} className="bg-white rounded-lg shadow-sm border overflow-hidden">
                      <Skeleton className="h-48 w-full" />
                      <div className="p-4 space-y-2">
                        <Skeleton className="h-4 w-3/4" />
                        <Skeleton className="h-4 w-1/2" />
                        <Skeleton className="h-6 w-1/3" />
                      </div>
                    </div>
                  ))}
                </div>
              }
            >
              <ProductGrid searchParams={searchParams} />
            </Suspense>
          </main>
        </div>
      </div>
    </div>
  )
}
