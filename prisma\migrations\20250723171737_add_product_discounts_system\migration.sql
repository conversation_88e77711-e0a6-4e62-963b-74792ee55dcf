/*
  Warnings:

  - You are about to drop the column `bannerImage` on the `categories` table. All the data in the column will be lost.
  - You are about to drop the column `barcode` on the `products` table. All the data in the column will be lost.
  - You are about to drop the column `costPrice` on the `products` table. All the data in the column will be lost.
  - You are about to drop the column `minStock` on the `products` table. All the data in the column will be lost.
  - You are about to drop the column `originalPrice` on the `products` table. All the data in the column will be lost.
  - You are about to drop the column `price` on the `products` table. All the data in the column will be lost.
  - You are about to drop the column `sku` on the `products` table. All the data in the column will be lost.
  - You are about to drop the column `stock` on the `products` table. All the data in the column will be lost.
  - You are about to drop the column `stockStatus` on the `products` table. All the data in the column will be lost.
  - Added the required column `basePrice` to the `products` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "TransitionType" AS ENUM ('FADE', 'SLIDE', 'ZOOM', 'FLIP', 'CUBE', 'COVERFLOW');

-- <PERSON><PERSON>Enum
CREATE TYPE "DeviceType" AS ENUM ('DESKTOP', 'MOBILE', 'TABLET');

-- CreateEnum
CREATE TYPE "ProductAttributeType" AS ENUM ('COLOR', 'SIZE', 'TEXT', 'NUMBER', 'BOOLEAN');

-- CreateEnum
CREATE TYPE "DiscountType" AS ENUM ('PERCENTAGE', 'FIXED_AMOUNT', 'BUY_X_GET_Y', 'FREE_SHIPPING', 'BULK_DISCOUNT', 'TIERED_DISCOUNT');

-- CreateEnum
CREATE TYPE "PriceChangeType" AS ENUM ('INCREASE', 'DECREASE', 'PROMOTION', 'COST_CHANGE', 'MARKET_ADJUSTMENT', 'SEASONAL_CHANGE', 'COMPETITOR_MATCH', 'CLEARANCE');

-- DropIndex
DROP INDEX "products_sku_key";

-- AlterTable
ALTER TABLE "categories" DROP COLUMN "bannerImage",
ADD COLUMN     "categoryImage" TEXT;

-- AlterTable
ALTER TABLE "products" DROP COLUMN "barcode",
DROP COLUMN "costPrice",
DROP COLUMN "minStock",
DROP COLUMN "originalPrice",
DROP COLUMN "price",
DROP COLUMN "sku",
DROP COLUMN "stock",
DROP COLUMN "stockStatus",
ADD COLUMN     "abcClassification" TEXT,
ADD COLUMN     "addToCartCount" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "ageRestriction" INTEGER,
ADD COLUMN     "alternativeProducts" JSONB,
ADD COLUMN     "arModelUrl" TEXT,
ADD COLUMN     "assemblyInstructions" TEXT,
ADD COLUMN     "availableLanguages" JSONB,
ADD COLUMN     "avgTimeOnPage" DOUBLE PRECISION,
ADD COLUMN     "baseCostPrice" DOUBLE PRECISION,
ADD COLUMN     "basePrice" DOUBLE PRECISION NOT NULL,
ADD COLUMN     "batchNumber" TEXT,
ADD COLUMN     "bounceRate" DOUBLE PRECISION,
ADD COLUMN     "bundleProducts" JSONB,
ADD COLUMN     "canonicalUrl" TEXT,
ADD COLUMN     "careInstructions" TEXT,
ADD COLUMN     "certifications" JSONB,
ADD COLUMN     "clickCount" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "complianceStandards" JSONB,
ADD COLUMN     "conversionRate" DOUBLE PRECISION,
ADD COLUMN     "crossSellProducts" JSONB,
ADD COLUMN     "defaultLanguage" TEXT NOT NULL DEFAULT 'tr',
ADD COLUMN     "demandForecast" DOUBLE PRECISION,
ADD COLUMN     "downloadableFiles" JSONB,
ADD COLUMN     "expiryDate" TIMESTAMP(3),
ADD COLUMN     "focusKeyword" TEXT,
ADD COLUMN     "fragile" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "freeShippingLimit" DOUBLE PRECISION,
ADD COLUMN     "hasVariants" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "hazardousMaterial" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "instructionManual" TEXT,
ADD COLUMN     "inventoryTurnover" DOUBLE PRECISION,
ADD COLUMN     "isDigital" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "isVirtual" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "lastPurchasedAt" TIMESTAMP(3),
ADD COLUMN     "lastViewedAt" TIMESTAMP(3),
ADD COLUMN     "leadTime" INTEGER,
ADD COLUMN     "lifecycle" TEXT,
ADD COLUMN     "localizedPricing" JSONB,
ADD COLUMN     "manufacturingDate" TIMESTAMP(3),
ADD COLUMN     "marketingNotes" TEXT,
ADD COLUMN     "parentProductId" TEXT,
ADD COLUMN     "popularityScore" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN     "profitMargin" DOUBLE PRECISION,
ADD COLUMN     "profitability" TEXT,
ADD COLUMN     "promotionTags" JSONB,
ADD COLUMN     "purchaseCount" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "qualityGrade" TEXT,
ADD COLUMN     "regionalAvailability" JSONB,
ADD COLUMN     "relatedProducts" JSONB,
ADD COLUMN     "reorderPoint" INTEGER,
ADD COLUMN     "reorderQuantity" INTEGER,
ADD COLUMN     "requiresSignature" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "reservedStock" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "retailPrice" DOUBLE PRECISION,
ADD COLUMN     "robotsDirective" TEXT DEFAULT 'index,follow',
ADD COLUMN     "safetyWarnings" JSONB,
ADD COLUMN     "searchRanking" DOUBLE PRECISION,
ADD COLUMN     "seasonalTags" JSONB,
ADD COLUMN     "seasonality" TEXT,
ADD COLUMN     "seoScore" DOUBLE PRECISION,
ADD COLUMN     "serialNumbers" JSONB,
ADD COLUMN     "shippingClass" TEXT,
ADD COLUMN     "shippingDimensions" JSONB,
ADD COLUMN     "shippingWeight" DOUBLE PRECISION,
ADD COLUMN     "stockLocation" TEXT,
ADD COLUMN     "structuredData" JSONB,
ADD COLUMN     "targetAudience" TEXT,
ADD COLUMN     "translations" JSONB,
ADD COLUMN     "trendingScore" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN     "upSellProducts" JSONB,
ADD COLUMN     "vatRate" DOUBLE PRECISION NOT NULL DEFAULT 18,
ADD COLUMN     "video360Url" TEXT,
ADD COLUMN     "videoUrl" TEXT,
ADD COLUMN     "viewCount" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "warrantyPeriod" INTEGER,
ADD COLUMN     "warrantyType" TEXT,
ADD COLUMN     "wholesalePrice" DOUBLE PRECISION,
ALTER COLUMN "taxRate" SET DEFAULT 18;

-- CreateTable
CREATE TABLE "category_banners" (
    "id" TEXT NOT NULL,
    "categoryId" TEXT NOT NULL,
    "imageUrl" TEXT NOT NULL,
    "imageAlt" TEXT,
    "mobileImageUrl" TEXT,
    "title" TEXT,
    "subtitle" TEXT,
    "description" TEXT,
    "ctaText" TEXT,
    "ctaUrl" TEXT,
    "displayOrder" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "startDate" TIMESTAMP(3),
    "endDate" TIMESTAMP(3),
    "displayDuration" INTEGER NOT NULL DEFAULT 5,
    "transitionType" "TransitionType" NOT NULL DEFAULT 'FADE',
    "backgroundColor" TEXT,
    "textColor" TEXT,
    "clickCount" INTEGER NOT NULL DEFAULT 0,
    "impressionCount" INTEGER NOT NULL DEFAULT 0,
    "ctr" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "priority" INTEGER NOT NULL DEFAULT 1,
    "targetAudience" TEXT,
    "deviceType" "DeviceType"[] DEFAULT ARRAY['DESKTOP', 'MOBILE', 'TABLET']::"DeviceType"[],
    "geoLocation" TEXT,
    "seasonalTags" TEXT,
    "conversionGoal" TEXT,
    "budgetAllocation" DOUBLE PRECISION,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdBy" TEXT,

    CONSTRAINT "category_banners_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "product_variants" (
    "id" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "sku" TEXT NOT NULL,
    "barcode" TEXT,
    "price" DOUBLE PRECISION NOT NULL,
    "originalPrice" DOUBLE PRECISION,
    "costPrice" DOUBLE PRECISION,
    "stock" INTEGER NOT NULL DEFAULT 0,
    "minStock" INTEGER NOT NULL DEFAULT 0,
    "stockStatus" "ProductStockStatus" NOT NULL DEFAULT 'IN_STOCK',
    "attributes" JSONB NOT NULL,
    "weight" DOUBLE PRECISION,
    "dimensions" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isDefault" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "product_variants_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "product_attributes" (
    "id" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" "ProductAttributeType" NOT NULL,
    "isRequired" BOOLEAN NOT NULL DEFAULT false,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "product_attributes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "product_attribute_options" (
    "id" TEXT NOT NULL,
    "attributeId" TEXT NOT NULL,
    "value" TEXT NOT NULL,
    "displayName" TEXT NOT NULL,
    "colorCode" TEXT,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "product_attribute_options_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "product_variant_images" (
    "id" TEXT NOT NULL,
    "variantId" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "alt" TEXT NOT NULL,
    "title" TEXT,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "isMain" BOOLEAN NOT NULL DEFAULT false,
    "size" INTEGER NOT NULL DEFAULT 0,
    "width" INTEGER NOT NULL DEFAULT 0,
    "height" INTEGER NOT NULL DEFAULT 0,
    "format" TEXT NOT NULL DEFAULT 'jpg',

    CONSTRAINT "product_variant_images_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "product_reviews" (
    "id" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "rating" INTEGER NOT NULL,
    "title" TEXT,
    "comment" TEXT,
    "reviewerName" TEXT,
    "reviewerEmail" TEXT,
    "isVerifiedPurchase" BOOLEAN NOT NULL DEFAULT false,
    "isApproved" BOOLEAN NOT NULL DEFAULT false,
    "isHelpful" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "product_reviews_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "product_tags" (
    "id" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "slug" TEXT NOT NULL,
    "type" TEXT,
    "color" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "product_tags_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "product_discounts" (
    "id" TEXT NOT NULL,
    "productId" TEXT,
    "variantId" TEXT,
    "categoryId" TEXT,
    "name" TEXT NOT NULL,
    "code" TEXT,
    "description" TEXT,
    "type" "DiscountType" NOT NULL,
    "value" DOUBLE PRECISION NOT NULL,
    "maxDiscount" DOUBLE PRECISION,
    "minOrderAmount" DOUBLE PRECISION,
    "buyQuantity" INTEGER,
    "getQuantity" INTEGER,
    "getDiscountPercent" DOUBLE PRECISION,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "usageLimit" INTEGER,
    "usageCount" INTEGER NOT NULL DEFAULT 0,
    "userLimit" INTEGER,
    "conditions" JSONB,
    "customerGroups" JSONB,
    "regions" JSONB,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "priority" INTEGER NOT NULL DEFAULT 0,
    "stackable" BOOLEAN NOT NULL DEFAULT false,
    "clickCount" INTEGER NOT NULL DEFAULT 0,
    "conversionCount" INTEGER NOT NULL DEFAULT 0,
    "totalSavings" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "createdBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "product_discounts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "discount_usage" (
    "id" TEXT NOT NULL,
    "discountId" TEXT NOT NULL,
    "userId" TEXT,
    "orderId" TEXT,
    "productId" TEXT,
    "variantId" TEXT,
    "originalAmount" DOUBLE PRECISION NOT NULL,
    "discountAmount" DOUBLE PRECISION NOT NULL,
    "finalAmount" DOUBLE PRECISION NOT NULL,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "sessionId" TEXT,
    "usedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "discount_usage_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "product_price_history" (
    "id" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "variantId" TEXT,
    "oldPrice" DOUBLE PRECISION NOT NULL,
    "newPrice" DOUBLE PRECISION NOT NULL,
    "changeType" "PriceChangeType" NOT NULL,
    "reason" TEXT,
    "discountId" TEXT,
    "basePrice" DOUBLE PRECISION,
    "costPrice" DOUBLE PRECISION,
    "margin" DOUBLE PRECISION,
    "effectiveDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "product_price_history_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "product_inventory_logs" (
    "id" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "variantId" TEXT,
    "changeType" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "previousStock" INTEGER NOT NULL,
    "newStock" INTEGER NOT NULL,
    "reason" TEXT,
    "reference" TEXT,
    "notes" TEXT,
    "createdBy" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "product_inventory_logs_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "category_banners_categoryId_displayOrder_idx" ON "category_banners"("categoryId", "displayOrder");

-- CreateIndex
CREATE INDEX "category_banners_isActive_startDate_endDate_idx" ON "category_banners"("isActive", "startDate", "endDate");

-- CreateIndex
CREATE UNIQUE INDEX "product_variants_sku_key" ON "product_variants"("sku");

-- CreateIndex
CREATE INDEX "product_variants_productId_idx" ON "product_variants"("productId");

-- CreateIndex
CREATE INDEX "product_variants_sku_idx" ON "product_variants"("sku");

-- CreateIndex
CREATE INDEX "product_attributes_productId_idx" ON "product_attributes"("productId");

-- CreateIndex
CREATE INDEX "product_attribute_options_attributeId_idx" ON "product_attribute_options"("attributeId");

-- CreateIndex
CREATE INDEX "product_variant_images_variantId_idx" ON "product_variant_images"("variantId");

-- CreateIndex
CREATE INDEX "product_reviews_productId_idx" ON "product_reviews"("productId");

-- CreateIndex
CREATE INDEX "product_reviews_rating_idx" ON "product_reviews"("rating");

-- CreateIndex
CREATE INDEX "product_reviews_isApproved_idx" ON "product_reviews"("isApproved");

-- CreateIndex
CREATE INDEX "product_tags_productId_idx" ON "product_tags"("productId");

-- CreateIndex
CREATE INDEX "product_tags_slug_idx" ON "product_tags"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "product_tags_productId_slug_key" ON "product_tags"("productId", "slug");

-- CreateIndex
CREATE INDEX "product_discounts_productId_idx" ON "product_discounts"("productId");

-- CreateIndex
CREATE INDEX "product_discounts_variantId_idx" ON "product_discounts"("variantId");

-- CreateIndex
CREATE INDEX "product_discounts_categoryId_idx" ON "product_discounts"("categoryId");

-- CreateIndex
CREATE INDEX "product_discounts_startDate_endDate_idx" ON "product_discounts"("startDate", "endDate");

-- CreateIndex
CREATE INDEX "product_discounts_isActive_idx" ON "product_discounts"("isActive");

-- CreateIndex
CREATE INDEX "product_discounts_priority_idx" ON "product_discounts"("priority");

-- CreateIndex
CREATE INDEX "product_discounts_code_idx" ON "product_discounts"("code");

-- CreateIndex
CREATE INDEX "discount_usage_discountId_idx" ON "discount_usage"("discountId");

-- CreateIndex
CREATE INDEX "discount_usage_userId_idx" ON "discount_usage"("userId");

-- CreateIndex
CREATE INDEX "discount_usage_orderId_idx" ON "discount_usage"("orderId");

-- CreateIndex
CREATE INDEX "discount_usage_usedAt_idx" ON "discount_usage"("usedAt");

-- CreateIndex
CREATE INDEX "product_price_history_productId_idx" ON "product_price_history"("productId");

-- CreateIndex
CREATE INDEX "product_price_history_variantId_idx" ON "product_price_history"("variantId");

-- CreateIndex
CREATE INDEX "product_price_history_effectiveDate_idx" ON "product_price_history"("effectiveDate");

-- CreateIndex
CREATE INDEX "product_price_history_changeType_idx" ON "product_price_history"("changeType");

-- CreateIndex
CREATE INDEX "product_inventory_logs_productId_idx" ON "product_inventory_logs"("productId");

-- CreateIndex
CREATE INDEX "product_inventory_logs_variantId_idx" ON "product_inventory_logs"("variantId");

-- CreateIndex
CREATE INDEX "product_inventory_logs_changeType_idx" ON "product_inventory_logs"("changeType");

-- CreateIndex
CREATE INDEX "product_inventory_logs_createdAt_idx" ON "product_inventory_logs"("createdAt");

-- CreateIndex
CREATE INDEX "products_slug_idx" ON "products"("slug");

-- CreateIndex
CREATE INDEX "products_categoryId_idx" ON "products"("categoryId");

-- CreateIndex
CREATE INDEX "products_brand_idx" ON "products"("brand");

-- CreateIndex
CREATE INDEX "products_isActive_idx" ON "products"("isActive");

-- CreateIndex
CREATE INDEX "products_isFeatured_idx" ON "products"("isFeatured");

-- CreateIndex
CREATE INDEX "products_popularityScore_idx" ON "products"("popularityScore");

-- CreateIndex
CREATE INDEX "products_createdAt_idx" ON "products"("createdAt");

-- CreateIndex
CREATE INDEX "products_publishedAt_idx" ON "products"("publishedAt");

-- AddForeignKey
ALTER TABLE "category_banners" ADD CONSTRAINT "category_banners_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES "categories"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "product_variants" ADD CONSTRAINT "product_variants_productId_fkey" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "product_attributes" ADD CONSTRAINT "product_attributes_productId_fkey" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "product_attribute_options" ADD CONSTRAINT "product_attribute_options_attributeId_fkey" FOREIGN KEY ("attributeId") REFERENCES "product_attributes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "product_variant_images" ADD CONSTRAINT "product_variant_images_variantId_fkey" FOREIGN KEY ("variantId") REFERENCES "product_variants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "product_reviews" ADD CONSTRAINT "product_reviews_productId_fkey" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "product_tags" ADD CONSTRAINT "product_tags_productId_fkey" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "product_discounts" ADD CONSTRAINT "product_discounts_productId_fkey" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "discount_usage" ADD CONSTRAINT "discount_usage_discountId_fkey" FOREIGN KEY ("discountId") REFERENCES "product_discounts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "product_price_history" ADD CONSTRAINT "product_price_history_productId_fkey" FOREIGN KEY ("productId") REFERENCES "products"("id") ON DELETE CASCADE ON UPDATE CASCADE;
