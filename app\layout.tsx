import type { Metadata } from 'next'
import './globals.css'
import AuthSessionProvider from '@/components/providers/session-provider'
import { Toaster } from 'sonner'

export const metadata: Metadata = {
  title: '<PERSON>ş Güvenliği E-Ticaret',
  description: 'Modern iş güvenliği ekipmanları e-ticaret platformu',
  generator: 'Next.js',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="tr">
      <body>
        <AuthSessionProvider>
          {children}
          <Toaster
            position="top-right"
            richColors
            closeButton
            duration={4000}
          />
        </AuthSessionProvider>
      </body>
    </html>
  )
}
