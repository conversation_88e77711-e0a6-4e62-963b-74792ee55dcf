import { StockManager } from '@/lib/services/StockManager'
import { CartManager } from '@/lib/services/CartManager'
import { prisma } from '@/lib/prisma'

/**
 * Scheduled maintenance jobs for cart and stock management
 * These should be run as cron jobs or scheduled tasks
 */

/**
 * Cleanup expired stock reservations and carts
 * Recommended: Run every 15 minutes
 */
export async function cleanupExpiredReservations() {
  console.log('🧹 Starting cleanup of expired reservations...')
  
  try {
    // 1. Cleanup expired stock reservations
    const releasedReservations = await StockManager.cleanupExpiredReservations()
    console.log(`✅ Released ${releasedReservations} expired stock reservations`)
    
    // 2. Cleanup expired carts
    const clearedCarts = await CartManager.clearExpiredCarts()
    console.log(`✅ Cleared ${clearedCarts} expired carts`)
    
    // 3. Update stock status for affected products
    await updateStockStatuses()
    
    console.log('✅ Cleanup completed successfully')
    
    return {
      success: true,
      releasedReservations,
      clearedCarts
    }
  } catch (error) {
    console.error('❌ Cleanup failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Update stock statuses for all products
 * Recommended: Run every hour
 */
export async function updateStockStatuses() {
  console.log('📊 Updating stock statuses...')
  
  try {
    const products = await prisma.product.findMany({
      where: { trackStock: true },
      select: {
        id: true,
        stockQuantity: true,
        reservedStock: true,
        minStockThreshold: true,
        stockStatus: true
      }
    })
    
    let updatedCount = 0
    
    for (const product of products) {
      const availableStock = product.stockQuantity - product.reservedStock
      let newStatus: 'IN_STOCK' | 'LOW_STOCK' | 'OUT_OF_STOCK'
      
      if (availableStock <= 0) {
        newStatus = 'OUT_OF_STOCK'
      } else if (availableStock <= product.minStockThreshold) {
        newStatus = 'LOW_STOCK'
      } else {
        newStatus = 'IN_STOCK'
      }
      
      if (newStatus !== product.stockStatus) {
        await prisma.product.update({
          where: { id: product.id },
          data: { stockStatus: newStatus }
        })
        updatedCount++
      }
    }
    
    console.log(`✅ Updated ${updatedCount} product stock statuses`)
    return { success: true, updatedCount }
  } catch (error) {
    console.error('❌ Stock status update failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Generate low stock alerts
 * Recommended: Run daily at 9 AM
 */
export async function generateLowStockAlerts() {
  console.log('⚠️  Generating low stock alerts...')
  
  try {
    const lowStockProducts = await StockManager.getLowStockProducts()
    
    if (lowStockProducts.length === 0) {
      console.log('✅ No low stock products found')
      return { success: true, alertCount: 0 }
    }
    
    // Log low stock products
    console.log(`⚠️  Found ${lowStockProducts.length} low stock products:`)
    lowStockProducts.forEach(product => {
      const availableStock = product.stockQuantity - product.reservedStock
      console.log(`  - ${product.name}: ${availableStock} available (threshold: ${product.minStockThreshold})`)
    })
    
    // Here you could send emails, notifications, or create alerts in your system
    // For now, we'll just log them
    
    return {
      success: true,
      alertCount: lowStockProducts.length,
      products: lowStockProducts
    }
  } catch (error) {
    console.error('❌ Low stock alert generation failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Optimize database performance
 * Recommended: Run daily at 2 AM
 */
export async function optimizeDatabase() {
  console.log('🔧 Optimizing database performance...')
  
  try {
    // 1. Clean up old inventory logs (keep last 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    
    const deletedLogs = await prisma.productInventoryLog.deleteMany({
      where: {
        createdAt: { lt: thirtyDaysAgo }
      }
    })
    
    console.log(`✅ Cleaned up ${deletedLogs.count} old inventory logs`)
    
    // 2. Clean up old price history (keep last 90 days)
    const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)
    
    const deletedPriceHistory = await prisma.productPriceHistory.deleteMany({
      where: {
        createdAt: { lt: ninetyDaysAgo }
      }
    })
    
    console.log(`✅ Cleaned up ${deletedPriceHistory.count} old price history records`)
    
    // 3. Update product popularity scores based on recent activity
    await updateProductPopularityScores()
    
    return {
      success: true,
      deletedLogs: deletedLogs.count,
      deletedPriceHistory: deletedPriceHistory.count
    }
  } catch (error) {
    console.error('❌ Database optimization failed:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Update product popularity scores
 * Internal helper function
 */
async function updateProductPopularityScores() {
  console.log('📈 Updating product popularity scores...')
  
  try {
    // This is a simplified popularity calculation
    // In a real system, you might consider:
    // - Recent views, purchases, cart additions
    // - Conversion rates, ratings, reviews
    // - Seasonal trends, etc.
    
    const products = await prisma.product.findMany({
      select: {
        id: true,
        viewCount: true,
        lastViewedAt: true,
        lastPurchasedAt: true
      }
    })
    
    let updatedCount = 0
    
    for (const product of products) {
      // Simple popularity calculation based on recent activity
      const now = Date.now()
      const lastViewed = product.lastViewedAt?.getTime() || 0
      const lastPurchased = product.lastPurchasedAt?.getTime() || 0
      
      const daysSinceViewed = (now - lastViewed) / (1000 * 60 * 60 * 24)
      const daysSincePurchased = (now - lastPurchased) / (1000 * 60 * 60 * 24)
      
      // Calculate popularity score (0-100)
      let popularityScore = 0
      
      if (daysSinceViewed < 7) popularityScore += 20
      if (daysSinceViewed < 30) popularityScore += 10
      if (daysSincePurchased < 7) popularityScore += 30
      if (daysSincePurchased < 30) popularityScore += 20
      if (product.viewCount > 100) popularityScore += 20
      
      await prisma.product.update({
        where: { id: product.id },
        data: { popularityScore }
      })
      
      updatedCount++
    }
    
    console.log(`✅ Updated ${updatedCount} product popularity scores`)
  } catch (error) {
    console.error('❌ Popularity score update failed:', error)
  }
}

/**
 * Health check for cart and stock system
 * Recommended: Run every 5 minutes
 */
export async function healthCheck() {
  try {
    // 1. Check database connectivity
    await prisma.$queryRaw`SELECT 1`
    
    // 2. Check for any stuck reservations
    const stuckReservations = await prisma.stockReservation.count({
      where: {
        status: 'ACTIVE',
        createdAt: { lt: new Date(Date.now() - 2 * 60 * 60 * 1000) } // 2 hours old
      }
    })
    
    // 3. Check for negative stock
    const negativeStock = await prisma.product.count({
      where: {
        OR: [
          { stockQuantity: { lt: 0 } },
          { reservedStock: { lt: 0 } }
        ]
      }
    })
    
    // 4. Check for orphaned cart items
    const orphanedCartItems = await prisma.cartItem.count({
      where: {
        cart: null
      }
    })
    
    const isHealthy = stuckReservations === 0 && negativeStock === 0 && orphanedCartItems === 0
    
    if (!isHealthy) {
      console.warn('⚠️  Health check issues detected:', {
        stuckReservations,
        negativeStock,
        orphanedCartItems
      })
    }
    
    return {
      success: true,
      healthy: isHealthy,
      issues: {
        stuckReservations,
        negativeStock,
        orphanedCartItems
      }
    }
  } catch (error) {
    console.error('❌ Health check failed:', error)
    return {
      success: false,
      healthy: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

/**
 * Run all maintenance tasks
 * Recommended: Use this for comprehensive maintenance
 */
export async function runAllMaintenanceTasks() {
  console.log('🚀 Starting comprehensive maintenance...')
  
  const results = {
    cleanup: await cleanupExpiredReservations(),
    stockStatus: await updateStockStatuses(),
    lowStockAlerts: await generateLowStockAlerts(),
    optimization: await optimizeDatabase(),
    healthCheck: await healthCheck()
  }
  
  console.log('✅ Comprehensive maintenance completed')
  return results
}

// Export individual functions for cron jobs
export {
  cleanupExpiredReservations as cleanup,
  updateStockStatuses as updateStock,
  generateLowStockAlerts as lowStockAlerts,
  optimizeDatabase as optimize,
  healthCheck as health,
  runAllMaintenanceTasks as runAll
}
