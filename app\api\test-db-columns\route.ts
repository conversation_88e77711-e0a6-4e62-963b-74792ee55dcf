import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Testing database columns directly...')

    // Test if the columns exist by using raw SQL
    const result = await prisma.$queryRaw`
      SELECT 
        id,
        name,
        "stockQuantity",
        "minStockThreshold", 
        "maxStockCapacity",
        "basePrice",
        "trackStock"
      FROM "products" 
      LIMIT 1
    `

    console.log('✅ Raw SQL query successful!')
    console.log('Sample data:', result)

    return NextResponse.json({
      success: true,
      message: 'Database columns exist and are accessible',
      sampleData: result,
      nextSteps: [
        'The database migration was successful',
        'The columns exist in the database',
        'Need to regenerate Prisma client to recognize new fields',
        'Run: npx prisma generate'
      ]
    })

  } catch (error: any) {
    console.error('❌ Database column test failed:', error)
    return NextResponse.json({
      success: false,
      error: error.message,
      details: 'Failed to access database columns'
    }, { status: 500 })
  }
}
