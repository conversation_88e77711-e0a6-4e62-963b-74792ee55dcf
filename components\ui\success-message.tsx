"use client"

import { <PERSON>Circle, X } from "lucide-react"
import { <PERSON><PERSON> } from "./button"
import { Card, CardContent } from "./card"

interface SuccessMessageProps {
  message: string
  onClose?: () => void
}

export function SuccessMessage({ message, onClose }: SuccessMessageProps) {
  return (
    <Card className="border-green-200 bg-green-50">
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
          <div className="flex-1">
            <p className="text-green-800 font-medium">Başarılı</p>
            <p className="text-green-700 text-sm mt-1">{message}</p>
          </div>
          {onClose && (
            <Button variant="ghost" size="icon" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
