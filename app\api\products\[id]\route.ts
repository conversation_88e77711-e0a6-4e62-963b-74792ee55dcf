import { type NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import type { Product } from "@/types"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const product = await prisma.product.findUnique({
      where: { id },
      include: {
        category: true,
        images: { orderBy: { sortOrder: 'asc' } },
        videos: { orderBy: { sortOrder: 'asc' } },
        specifications: { orderBy: { sortOrder: 'asc' } },
        certificates: true,
      },
    })

    if (!product) {
      return NextResponse.json(
        { success: false, error: "Ürün bulunamadı" },
        { status: 404 }
      )
    }

    // Transform to match existing interface
    const transformedProduct: Product = {
      id: product.id,
      name: product.name,
      slug: product.slug,
      description: product.description,
      shortDescription: product.shortDescription,
      sku: `PRD-${product.id.slice(-8)}`, // Generate SKU from ID
      barcode: null, // Not available at product level
      categoryId: product.categoryId,
      category: product.category,
      brand: product.brand,
      model: product.model,
      price: product.basePrice, // Use basePrice from Product model
      originalPrice: product.basePrice, // Same as price for now
      costPrice: product.baseCostPrice || 0,
      basePrice: product.basePrice, // Add basePrice field
      baseCostPrice: product.baseCostPrice,
      zakatAmount: product.zakatAmount,
      taxRate: product.taxRate,
      currency: product.currency,
      stockQuantity: product.stockQuantity,
      minStockThreshold: product.minStockThreshold,
      maxStockCapacity: product.maxStockCapacity,
      stockLocation: product.stockLocation,
      reorderPoint: product.reorderPoint,
      reorderQuantity: product.reorderQuantity,
      // Legacy fields for backward compatibility
      stock: product.stockQuantity,
      minStock: product.minStockThreshold,
      stockStatus: (product.stockStatus || 'IN_STOCK').toLowerCase() as "in_stock" | "out_of_stock" | "low_stock",
      trackStock: product.trackStock,
      images: product.images,
      videos: product.videos,
      specifications: product.specifications,
      certificates: product.certificates,
      seoTitle: product.seoTitle,
      seoDescription: product.seoDescription,
      metaKeywords: product.metaKeywords ? (typeof product.metaKeywords === 'string' ? JSON.parse(product.metaKeywords) : product.metaKeywords) : undefined,
      isActive: product.isActive,
      isFeatured: product.isFeatured,
      isNew: product.isNew,
      isOnSale: product.isOnSale,
      weight: product.weight,
      dimensions: product.dimensions ? (typeof product.dimensions === 'string' ? JSON.parse(product.dimensions) : product.dimensions) : undefined,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      publishedAt: product.publishedAt,
    }

    return NextResponse.json({
      success: true,
      data: transformedProduct,
    })
  } catch (error) {
    console.error("Get product error:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Ürün yüklenirken hata oluştu",
        details: error instanceof Error ? error.message : "Bilinmeyen hata",
      },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id },
    })

    if (!existingProduct) {
      return NextResponse.json(
        { success: false, error: "Ürün bulunamadı" },
        { status: 404 }
      )
    }

    // Update product
    const updatedProduct = await prisma.product.update({
      where: { id },
      data: {
        name: body.name,
        slug: body.slug || body.name?.toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .trim(),
        description: body.description,
        shortDescription: body.shortDescription,
        sku: body.sku,
        barcode: body.barcode,
        categoryId: body.categoryId,
        brand: body.brand,
        model: body.model,
        // Enhanced pricing
        basePrice: body.basePrice || body.price,
        originalPrice: body.originalPrice,
        baseCostPrice: body.baseCostPrice || body.costPrice,
        zakatAmount: body.zakatAmount,
        taxRate: body.taxRate,
        currency: body.currency,
        // Stock management
        trackStock: body.trackStock,
        stockQuantity: body.stockQuantity,
        minStockThreshold: body.minStockThreshold,
        maxStockCapacity: body.maxStockCapacity,
        stockLocation: body.stockLocation,
        reorderPoint: body.reorderPoint,
        reorderQuantity: body.reorderQuantity,
        // Legacy fields for backward compatibility
        price: body.basePrice || body.price,
        costPrice: body.baseCostPrice || body.costPrice,
        stock: body.stockQuantity || body.stock,
        minStock: body.minStockThreshold || body.minStock,
        stockStatus: body.stockStatus ? body.stockStatus.toUpperCase() : undefined,
        // Status fields
        isActive: body.isActive,
        isFeatured: body.isFeatured,
        isNew: body.isNew,
        isOnSale: body.isOnSale,
        // SEO fields
        seoTitle: body.seoTitle,
        seoDescription: body.seoDescription,
        metaKeywords: body.metaKeywords ? JSON.stringify(body.metaKeywords) : null,
        // Physical properties
        weight: body.weight,
        dimensions: body.dimensions ? JSON.stringify(body.dimensions) : null,
      },
      include: {
        category: true,
        images: { orderBy: { sortOrder: 'asc' } },
        videos: { orderBy: { sortOrder: 'asc' } },
        specifications: { orderBy: { sortOrder: 'asc' } },
        certificates: true,
      },
    })

    // Transform to match existing interface
    const transformedProduct: Product = {
      id: updatedProduct.id,
      name: updatedProduct.name,
      slug: updatedProduct.slug,
      description: updatedProduct.description,
      shortDescription: updatedProduct.shortDescription,
      sku: updatedProduct.sku,
      barcode: updatedProduct.barcode,
      categoryId: updatedProduct.categoryId,
      category: updatedProduct.category,
      brand: updatedProduct.brand,
      model: updatedProduct.model,
      price: updatedProduct.price,
      originalPrice: updatedProduct.originalPrice,
      costPrice: updatedProduct.costPrice,
      taxRate: updatedProduct.taxRate,
      currency: updatedProduct.currency,
      stock: updatedProduct.stock,
      minStock: updatedProduct.minStock,
      stockStatus: updatedProduct.stockStatus.toLowerCase() as "in_stock" | "out_of_stock" | "low_stock",
      trackStock: updatedProduct.trackStock,
      images: updatedProduct.images,
      videos: updatedProduct.videos,
      specifications: updatedProduct.specifications,
      certificates: updatedProduct.certificates,
      seoTitle: updatedProduct.seoTitle,
      seoDescription: updatedProduct.seoDescription,
      metaKeywords: updatedProduct.metaKeywords ? JSON.parse(updatedProduct.metaKeywords) : undefined,
      isActive: updatedProduct.isActive,
      isFeatured: updatedProduct.isFeatured,
      isNew: updatedProduct.isNew,
      isOnSale: updatedProduct.isOnSale,
      weight: updatedProduct.weight,
      dimensions: updatedProduct.dimensions ? JSON.parse(updatedProduct.dimensions) : undefined,
      createdAt: updatedProduct.createdAt,
      updatedAt: updatedProduct.updatedAt,
      publishedAt: updatedProduct.publishedAt,
    }

    return NextResponse.json({
      success: true,
      message: "Ürün başarıyla güncellendi",
      data: transformedProduct,
    })
  } catch (error) {
    console.error("Update product error:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Ürün güncellenirken hata oluştu",
        details: error instanceof Error ? error.message : "Bilinmeyen hata",
      },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id },
    })

    if (!existingProduct) {
      return NextResponse.json(
        { success: false, error: "Ürün bulunamadı" },
        { status: 404 }
      )
    }

    // Delete product (cascade will handle related records)
    await prisma.product.delete({
      where: { id },
    })

    return NextResponse.json({
      success: true,
      message: "Ürün başarıyla silindi",
    })
  } catch (error) {
    console.error("Delete product error:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Ürün silinirken hata oluştu",
        details: error instanceof Error ? error.message : "Bilinmeyen hata",
      },
      { status: 500 }
    )
  }
}
