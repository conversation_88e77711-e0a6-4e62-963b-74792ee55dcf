import { PrismaClient } from '@prisma/client'
import { mockCategories, mockProducts } from '../data/mock-data'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

// Yardımcı fonksiyonlar
function getRandomColor(): string {
  const colors = [
    '#FF6B35', '#F7931E', '#FFD23F', '#06FFA5',
    '#3BCEAC', '#0EAD69', '#3A86FF', '#8338EC',
    '#FF006E', '#FB5607', '#FFBE0B', '#8338EC'
  ]
  return colors[Math.floor(Math.random() * colors.length)]
}

async function main() {
  console.log('🌱 Starting database seed...')

  // Create admin user
  const hashedPassword = await bcrypt.hash('password', 12)
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      name: 'Admin User',
      role: 'ADMIN',
      isActive: true,
    },
  })
  console.log('✅ Admin user created:', adminUser.email)

  // Clear existing data
  await prisma.productCertificate.deleteMany()
  await prisma.productSpecification.deleteMany()
  await prisma.productVideo.deleteMany()
  await prisma.productImage.deleteMany()
  await prisma.product.deleteMany()
  await prisma.category.deleteMany()

  console.log('🗑️  Cleared existing data')

  // Seed categories
  const categoryMap = new Map<string, string>()
  
  for (const mockCategory of mockCategories) {
    const category = await prisma.category.create({
      data: {
        name: mockCategory.name,
        slug: mockCategory.slug,
        description: mockCategory.description,
        icon: mockCategory.icon || '',
        parentId: null, // We'll handle hierarchy in a second pass if needed
        productCount: 0, // Will be updated when products are created
        isActive: mockCategory.isActive,
        sortOrder: mockCategory.sortOrder,
        seoTitle: mockCategory.seoTitle,
        seoDescription: mockCategory.seoDescription
      },
    })
    categoryMap.set(mockCategory.id, category.id)
    console.log(`✅ Created category: ${category.name}`)
  }

  // Seed products
  for (const mockProduct of mockProducts) {
    const categoryId = categoryMap.get(mockProduct.category.id)
    if (!categoryId) {
      console.warn(`⚠️  Category not found for product: ${mockProduct.name}`)
      continue
    }

    const product = await prisma.product.create({
      data: {
        name: mockProduct.name,
        slug: mockProduct.slug,
        description: mockProduct.description,
        shortDescription: mockProduct.shortDescription || mockProduct.description.substring(0, 200),
        categoryId: categoryId,
        brand: mockProduct.brand || 'Unknown',
        model: mockProduct.model,
        basePrice: mockProduct.price,
        baseCostPrice: mockProduct.costPrice,
        taxRate: mockProduct.taxRate || 0,
        currency: mockProduct.currency || 'TRY',
        trackStock: mockProduct.trackStock !== false,
        isActive: mockProduct.isActive !== false,
        isFeatured: mockProduct.isFeatured || false,
        isNew: mockProduct.isNew || false,
        isOnSale: mockProduct.isOnSale || false,
        seoTitle: mockProduct.seoTitle,
        seoDescription: mockProduct.seoDescription,
        metaKeywords: mockProduct.metaKeywords ? JSON.stringify(mockProduct.metaKeywords) : null,
        weight: mockProduct.weight,
        dimensions: mockProduct.dimensions ? JSON.stringify(mockProduct.dimensions) : null,
        publishedAt: new Date(),
      },
    })

    // Create product images
    if (mockProduct.images && mockProduct.images.length > 0) {
      for (const [index, image] of mockProduct.images.entries()) {
        await prisma.productImage.create({
          data: {
            productId: product.id,
            url: image.url,
            alt: image.alt,
            title: image.title,
            sortOrder: index,
            isMain: image.isMain || index === 0,
            size: image.size || 0,
            width: image.width || 400,
            height: image.height || 400,
            format: image.format || 'jpg',
          },
        })
      }
    }

    // Create product specifications
    if (mockProduct.specifications && mockProduct.specifications.length > 0) {
      for (const [index, spec] of mockProduct.specifications.entries()) {
        await prisma.productSpecification.create({
          data: {
            productId: product.id,
            name: spec.name,
            value: spec.value,
            unit: spec.unit,
            sortOrder: index,
          },
        })
      }
    }

    // Create product certificates
    if (mockProduct.certificates && mockProduct.certificates.length > 0) {
      for (const cert of mockProduct.certificates) {
        await prisma.productCertificate.create({
          data: {
            productId: product.id,
            name: cert.name,
            issuer: cert.issuer || 'Unknown', // Default issuer if not provided
            number: cert.number || null,
            validUntil: cert.validUntil ? new Date(cert.validUntil) : null,
            documentUrl: cert.documentUrl || null,
          },
        })
      }
    }

    console.log(`✅ Created product: ${product.name}`)
  }

  // Update category product counts
  for (const [mockId, dbId] of categoryMap.entries()) {
    const productCount = await prisma.product.count({
      where: { categoryId: dbId }
    })
    
    await prisma.category.update({
      where: { id: dbId },
      data: { productCount }
    })
  }

  console.log('🎉 Database seed completed successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error during seed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
