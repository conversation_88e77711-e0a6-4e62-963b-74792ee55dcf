import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    console.log('🔧 Fixing trackStock values...')

    // Update all products to have trackStock = true
    const result = await prisma.product.updateMany({
      data: {
        trackStock: true
      }
    })

    console.log('✅ Updated products:', result.count)

    // Get sample products to verify
    const sampleProducts = await prisma.product.findMany({
      take: 5,
      select: {
        id: true,
        name: true,
        trackStock: true,
        categoryId: true,
        category: {
          select: {
            name: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: `${result.count} products updated with trackStock = true`,
      sampleProducts
    })

  } catch (error: any) {
    console.error('❌ Fix failed:', error)
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 })
  }
}
