import Link from "next/link"
import { ChevronRight, Home } from "lucide-react"
import type { Product } from "@/types"

interface ProductBreadcrumbProps {
  product: Product
}

export function ProductBreadcrumb({ product }: ProductBreadcrumbProps) {
  return (
    <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-8">
      <Link href="/" className="flex items-center hover:text-orange-600 transition-colors">
        <Home className="w-4 h-4 mr-1" />
        Ana Sayfa
      </Link>

      <ChevronRight className="w-4 h-4 text-gray-400" />

      <Link href="/urunler" className="hover:text-orange-600 transition-colors">
        <PERSON><PERSON><PERSON><PERSON><PERSON>
      </Link>

      <ChevronRight className="w-4 h-4 text-gray-400" />

      <Link href={`/urunler/kategori/${product.category.slug}`} className="hover:text-orange-600 transition-colors">
        {product.category.name}
      </Link>

      <ChevronRight className="w-4 h-4 text-gray-400" />

      <span className="text-gray-900 font-medium truncate max-w-xs">{product.name}</span>
    </nav>
  )
}
