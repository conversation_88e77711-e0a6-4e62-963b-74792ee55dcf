import { type NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import type { Product } from "@/types"

export async function GET(request: NextRequest, { params }: { params: { slug: string } }) {
  try {
    const { slug } = params

    if (!slug) {
      return NextResponse.json({ success: false, message: "Slug parametresi gerekli" }, { status: 400 })
    }

    const product = await prisma.product.findUnique({
      where: { slug },
      include: {
        category: true,
        images: { orderBy: { sortOrder: 'asc' } },
        videos: { orderBy: { sortOrder: 'asc' } },
        specifications: { orderBy: { sortOrder: 'asc' } },
        certificates: true,
      },
    })

    if (!product) {
      return NextResponse.json({ success: false, message: "Ürün bulunamadı" }, { status: 404 })
    }

    // Transform to match existing interface
    const transformedProduct: Product = {
      id: product.id,
      name: product.name,
      slug: product.slug,
      description: product.description,
      shortDescription: product.shortDescription,
      sku: `PRD-${product.id.slice(-8)}`, // Generate SKU from ID
      barcode: null, // Not available at product level
      categoryId: product.categoryId,
      category: product.category,
      brand: product.brand,
      model: product.model,
      price: product.basePrice, // Use basePrice from Product model
      originalPrice: product.basePrice, // Same as price for now
      costPrice: product.baseCostPrice || 0,
      basePrice: product.basePrice, // Add basePrice field
      baseCostPrice: product.baseCostPrice,
      zakatAmount: product.zakatAmount,
      taxRate: product.taxRate,
      currency: product.currency,
      stockQuantity: product.stockQuantity,
      minStockThreshold: product.minStockThreshold,
      maxStockCapacity: product.maxStockCapacity,
      stockLocation: product.stockLocation,
      reorderPoint: product.reorderPoint,
      reorderQuantity: product.reorderQuantity,
      // Legacy fields for backward compatibility
      stock: product.stockQuantity,
      minStock: product.minStockThreshold,
      stockStatus: (product.stockStatus || 'IN_STOCK').toLowerCase() as "in_stock" | "out_of_stock" | "low_stock",
      trackStock: product.trackStock,
      images: product.images,
      videos: product.videos,
      specifications: product.specifications,
      certificates: product.certificates,
      seoTitle: product.seoTitle,
      seoDescription: product.seoDescription,
      metaKeywords: product.metaKeywords ? (typeof product.metaKeywords === 'string' ? JSON.parse(product.metaKeywords) : product.metaKeywords) : undefined,
      isActive: product.isActive,
      isFeatured: product.isFeatured,
      isNew: product.isNew,
      isOnSale: product.isOnSale,
      weight: product.weight,
      dimensions: product.dimensions ? (typeof product.dimensions === 'string' ? JSON.parse(product.dimensions) : product.dimensions) : undefined,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt,
      publishedAt: product.publishedAt,
    }

    return NextResponse.json({
      success: true,
      data: transformedProduct,
      message: "Ürün başarıyla getirildi",
    })
  } catch (error) {
    console.error("Product slug API error:", error)
    return NextResponse.json({ success: false, message: "Sunucu hatası" }, { status: 500 })
  }
}
