import { fetcher } from "@/lib/utils"
import type { Product, ProductFilterParams } from "@/types"

const API_BASE_URL = "/api/products"

export const getProducts = async (filters?: Partial<ProductFilterParams>) => {
  try {
    const params = new URLSearchParams()

    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          params.append(key, value.toString())
        }
      })
    }

    const url = params.toString() ? `${API_BASE_URL}?${params.toString()}` : API_BASE_URL
    const response = await fetcher<{ data: Product[]; pagination: any }>(url)
    return response
  } catch (error) {
    console.error("getProducts error:", error)
    // Return empty data instead of throwing
    return {
      data: [],
      pagination: {
        page: 1,
        limit: 12,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      },
    }
  }
}

export const getProductById = async (id: string) => {
  try {
    const response = await fetcher<{ data: Product }>(`${API_BASE_URL}/${id}`)
    return response.data
  } catch (error) {
    console.error("getProductById error:", error)
    throw error
  }
}

export const getProductBySlug = async (slug: string) => {
  try {
    const response = await fetcher<{ data: Product }>(`${API_BASE_URL}/slug/${slug}`)
    return response.data
  } catch (error) {
    console.error("getProductBySlug error:", error)
    throw error
  }
}

export const createProduct = async (productData: Partial<Product>) => {
  try {
    const response = await fetcher<{ success: boolean; data: Product; message: string }>(API_BASE_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(productData),
    })
    return response
  } catch (error) {
    console.error("createProduct error:", error)
    throw error
  }
}

export const updateProduct = async (id: string, productData: Partial<Product>) => {
  try {
    const response = await fetcher<{ success: boolean; data: Product; message: string }>(`${API_BASE_URL}/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(productData),
    })
    return response
  } catch (error) {
    console.error("updateProduct error:", error)
    throw error
  }
}

export const deleteProduct = async (id: string) => {
  try {
    const response = await fetcher<{ success: boolean; message: string }>(`${API_BASE_URL}/${id}`, {
      method: "DELETE",
    })
    return response
  } catch (error) {
    console.error("deleteProduct error:", error)
    throw error
  }
}

export const bulkUpdateProducts = async (productIds: string[], updates: Partial<Product>) => {
  try {
    const response = await fetcher<{ success: boolean; message: string }>(`${API_BASE_URL}/bulk-update`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ ids: productIds, updates }),
    })
    return response
  } catch (error) {
    console.error("bulkUpdateProducts error:", error)
    throw error
  }
}

export const bulkDeleteProducts = async (productIds: string[]) => {
  try {
    const response = await fetcher<{ success: boolean; message: string }>(`${API_BASE_URL}/bulk-delete`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ ids: productIds }),
    })
    return response
  } catch (error) {
    console.error("bulkDeleteProducts error:", error)
    throw error
  }
}

export const getBrands = async () => {
  try {
    const response = await fetcher<{ data: string[] }>(`${API_BASE_URL}/brands`)
    return response.data || []
  } catch (error) {
    console.error("getBrands error:", error)
    return []
  }
}
