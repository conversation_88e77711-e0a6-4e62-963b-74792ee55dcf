{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "db:seed": "tsx prisma/seed.ts", "db:reset": "npx prisma migrate reset --force && npm run db:seed", "seed:all": "tsx scripts/seed-all.ts", "seed:products": "tsx prisma/seed-products.ts", "seed:comprehensive": "tsx scripts/seed-with-migration.ts", "seed:migration": "tsx scripts/apply-stock-migration.ts", "seed:verify": "tsx scripts/verify-seeding.ts", "maintenance:cleanup": "tsx scripts/run-maintenance.ts cleanup", "maintenance:stock": "tsx scripts/run-maintenance.ts stock", "maintenance:alerts": "tsx scripts/run-maintenance.ts alerts", "maintenance:optimize": "tsx scripts/run-maintenance.ts optimize", "maintenance:health": "tsx scripts/run-maintenance.ts health", "maintenance:all": "tsx scripts/run-maintenance.ts all"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "latest", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.9.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.12.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "latest", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "latest", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@types/pg": "^8.15.4", "autoprefixer": "^10.4.20", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "immer": "latest", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "14.2.16", "next-auth": "^4.24.11", "next-themes": "^0.4.4", "pg": "^8.16.3", "prisma": "^6.12.0", "react": "^18", "react-day-picker": "8.10.1", "react-dom": "^18", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sharp": "^0.34.3", "sonner": "^2.0.6", "swr": "latest", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "use-sync-external-store": "latest", "vaul": "^0.9.6", "zod": "^3.24.1", "zustand": "latest"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8.5", "tailwindcss": "^3.4.17", "tsx": "^4.20.3", "typescript": "^5"}}