import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import type { Category } from "@/types"

// GET /api/categories/slug/[slug] - Slug ile kategori getir
export async function GET(request: Request, { params }: { params: { slug: string } }) {
  try {
    const { slug } = params

    if (!slug) {
      return NextResponse.json({ success: false, message: "Kategori slug'ı gerekli" }, { status: 400 })
    }

    // Veritabanından kategoriyi getir
    const category = await prisma.category.findFirst({
      where: {
        slug: slug,
        isActive: true
      },
      include: {
        children: {
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' }
        },
        products: {
          select: { id: true }
        }
      }
    })

    if (!category) {
      return NextResponse.json({ success: false, message: "Kategori bulunamadı" }, { status: 404 })
    }

    // Transform to match existing interface
    const transformedCategory: Category = {
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      icon: category.icon,
      parentId: category.parentId,
      children: category.children,
      productCount: category.products.length,
      isActive: category.isActive,
      sortOrder: category.sortOrder,
      seoTitle: category.seoTitle,
      seoDescription: category.seoDescription,
      bannerImage: category.bannerImage,
      mobileIcon: category.mobileIcon,
      ogImage: category.ogImage,
      ogTitle: category.ogTitle,
      ogDescription: category.ogDescription,
      metaKeywords: category.metaKeywords,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt,
    }

    return NextResponse.json({
      success: true,
      data: transformedCategory,
    })
  } catch (error) {
    console.error("Category slug GET error:", error)
    return NextResponse.json({ success: false, message: "Kategori yüklenirken hata oluştu" }, { status: 500 })
  }
}
