const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function debugStockData() {
  console.log('🔍 Debugging Stock Data Flow...\n')

  try {
    // Step 1: Check what's actually in the database
    console.log('📊 Step 1: Database Stock Values')
    console.log('=' .repeat(50))
    
    const products = await prisma.product.findMany({
      select: {
        id: true,
        name: true,
        stockQuantity: true,
        minStockThreshold: true,
        maxStockCapacity: true,
        stockStatus: true,
        trackStock: true,
        basePrice: true,
        baseCostPrice: true
      },
      take: 5 // Just first 5 products
    })

    if (products.length === 0) {
      console.log('❌ No products found in database!')
      return
    }

    console.log(`Found ${products.length} products:`)
    products.forEach((product, index) => {
      console.log(`\n${index + 1}. ${product.name}`)
      console.log(`   ID: ${product.id}`)
      console.log(`   Stock Quantity: ${product.stockQuantity}`)
      console.log(`   Min Threshold: ${product.minStockThreshold}`)
      console.log(`   Max Capacity: ${product.maxStockCapacity}`)
      console.log(`   Stock Status: ${product.stockStatus}`)
      console.log(`   Track Stock: ${product.trackStock}`)
      console.log(`   Base Price: ${product.basePrice}`)
      console.log(`   Base Cost Price: ${product.baseCostPrice}`)
    })

    // Step 2: Test the API endpoint for the first product
    const firstProduct = products[0]
    console.log('\n🌐 Step 2: Testing Individual Product API')
    console.log('=' .repeat(50))
    console.log(`Testing API for product: ${firstProduct.name} (ID: ${firstProduct.id})`)
    
    // We'll use fetch to test the API
    const apiUrl = `http://localhost:3000/api/products/${firstProduct.id}`
    console.log(`API URL: ${apiUrl}`)
    
    try {
      const response = await fetch(apiUrl)
      
      if (!response.ok) {
        console.log(`❌ API Error: ${response.status} ${response.statusText}`)
        const errorText = await response.text()
        console.log(`Error details: ${errorText}`)
        return
      }

      const apiData = await response.json()
      console.log('\n✅ API Response received')
      console.log('Stock-related fields in API response:')
      console.log(`   stockQuantity: ${apiData.stockQuantity}`)
      console.log(`   minStockThreshold: ${apiData.minStockThreshold}`)
      console.log(`   maxStockCapacity: ${apiData.maxStockCapacity}`)
      console.log(`   stockStatus: ${apiData.stockStatus}`)
      console.log(`   trackStock: ${apiData.trackStock}`)
      console.log(`   stock (legacy): ${apiData.stock}`)
      console.log(`   minStock (legacy): ${apiData.minStock}`)
      console.log(`   price: ${apiData.price}`)
      console.log(`   basePrice: ${apiData.basePrice}`)
      console.log(`   costPrice: ${apiData.costPrice}`)
      console.log(`   baseCostPrice: ${apiData.baseCostPrice}`)

      // Step 3: Compare database vs API
      console.log('\n🔄 Step 3: Database vs API Comparison')
      console.log('=' .repeat(50))
      
      const comparisons = [
        { field: 'stockQuantity', db: firstProduct.stockQuantity, api: apiData.stockQuantity },
        { field: 'minStockThreshold', db: firstProduct.minStockThreshold, api: apiData.minStockThreshold },
        { field: 'maxStockCapacity', db: firstProduct.maxStockCapacity, api: apiData.maxStockCapacity },
        { field: 'stockStatus', db: firstProduct.stockStatus, api: apiData.stockStatus },
        { field: 'trackStock', db: firstProduct.trackStock, api: apiData.trackStock },
        { field: 'basePrice', db: firstProduct.basePrice, api: apiData.basePrice }
      ]

      let hasDiscrepancies = false
      comparisons.forEach(({ field, db, api }) => {
        const match = db === api || (field === 'stockStatus' && db?.toLowerCase() === api)
        const status = match ? '✅' : '❌'
        console.log(`${status} ${field}: DB=${db} | API=${api}`)
        if (!match) hasDiscrepancies = true
      })

      if (hasDiscrepancies) {
        console.log('\n⚠️  DISCREPANCIES FOUND between database and API!')
      } else {
        console.log('\n✅ Database and API values match perfectly!')
      }

      // Step 4: Show the complete API response structure
      console.log('\n📋 Step 4: Complete API Response Structure')
      console.log('=' .repeat(50))
      console.log('Full API response keys:', Object.keys(apiData))
      
      // Return the first product ID for further testing
      return firstProduct.id

    } catch (fetchError) {
      console.log(`❌ Failed to fetch from API: ${fetchError.message}`)
      console.log('This might indicate the server is not running or API endpoint is broken')
      return firstProduct.id
    }

  } catch (error) {
    console.error('❌ Debug failed:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the debug
debugStockData()
  .then((productId) => {
    if (productId) {
      console.log(`\n🎯 Next Steps:`)
      console.log(`1. Test the admin panel edit form for product ID: ${productId}`)
      console.log(`2. Check browser developer tools for JavaScript errors`)
      console.log(`3. Verify form field population in the frontend component`)
      console.log(`\n💡 Open: http://localhost:3000/admin/urunler`)
      console.log(`Then click Edit on the first product to test the form.`)
    }
  })
  .catch((error) => {
    console.error('Script failed:', error)
    process.exit(1)
  })
