import { fetcher } from "@/lib/utils"
import type { Category, CategoryFilter } from "@/types"

const BASE_URL = "/api/categories"

class CategoryServiceClass {
  async getAll(): Promise<Category[]> {
    const response = await fetcher(BASE_URL)
    return response.data || response
  }

  // Alias for getAll to match hook expectations
  async getCategories(filter?: CategoryFilter): Promise<Category[]> {
    let url = BASE_URL
    if (filter) {
      const params = new URLSearchParams()
      if (filter.search) params.append('search', filter.search)
      if (filter.isActive !== undefined) params.append('isActive', filter.isActive.toString())
      if (filter.parentId !== undefined) {
        params.append('parentId', filter.parentId === null ? 'null' : filter.parentId)
      }
      if (filter.sortBy) params.append('sortBy', filter.sortBy)
      if (filter.sortOrder) params.append('sortOrder', filter.sortOrder)
      if (filter.page) params.append('page', filter.page.toString())
      if (filter.limit) params.append('limit', filter.limit.toString())

      const queryString = params.toString()
      if (queryString) url += `?${queryString}`
    }

    console.log('📡 CategoryService: Making request to:', url)
    const response = await fetcher(url)
    console.log('📥 CategoryService: Received response:', {
      hasData: !!response.data,
      dataLength: response.data?.length || 0,
      hasPagination: !!response.pagination,
      totalFromPagination: response.pagination?.total || 0,
      success: response.success,
      responseKeys: Object.keys(response || {})
    })

    return response.data || response
  }

  // Alias for getById to match hook expectations
  async getCategory(id: string): Promise<Category> {
    return this.getById(id)
  }

  async getMainCategories(): Promise<Category[]> {
    const response = await fetcher(`${BASE_URL}/main`)
    return response.data || response
  }

  async getSubCategories(parentId: string): Promise<Category[]> {
    const response = await fetcher(`${BASE_URL}?parentId=${parentId}`)
    return response.data || response
  }

  async getById(id: string): Promise<Category> {
    console.log('🔍 CategoryService.getById: Fetching category', id)
    const response = await fetcher(`${BASE_URL}/${id}`)

    console.log('🔍 CategoryService.getById: API Response')
    console.log('   - Success:', response.success)
    console.log('   - Has Data:', !!response.data)
    console.log('   - Category ID:', response.data?.id)
    console.log('   - Category Name:', response.data?.name)
    console.log('   - Category Image:', response.data?.categoryImage)
    console.log('   - Has Category Image:', !!response.data?.categoryImage)
    console.log('   - Category Image Type:', typeof response.data?.categoryImage)
    console.log('   - All Response Keys:', Object.keys(response.data || {}))

    return response.data
  }

  async getBySlug(slug: string): Promise<Category> {
    const response = await fetcher(`${BASE_URL}/slug/${slug}`)
    return response.data
  }

  async create(data: Partial<Category>): Promise<{ success: boolean; message: string; data?: Category }> {
    const response = await fetcher(BASE_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    })
    return response
  }

  // Alias for create to match hook expectations
  async createCategory(data: any): Promise<{ success: boolean; message: string; data?: Category }> {
    return this.create(data)
  }

  async update(id: string, data: Partial<Category>): Promise<{ success: boolean; message: string; data?: Category }> {
    const response = await fetcher(`${BASE_URL}/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    })
    return response
  }

  // Alias for update to match hook expectations
  async updateCategory(id: string, data: any): Promise<{ success: boolean; message: string; data?: Category }> {
    return this.update(id, data)
  }

  async delete(id: string): Promise<{ success: boolean; message: string }> {
    const response = await fetcher(`${BASE_URL}/${id}`, {
      method: "DELETE",
    })
    return response
  }

  // Alias for delete to match hook expectations
  async deleteCategory(id: string): Promise<{ success: boolean; message: string }> {
    return this.delete(id)
  }

  async reorder(categories: { id: string; sortOrder: number }[]): Promise<{ success: boolean; message: string }> {
    const response = await fetcher(`${BASE_URL}/reorder`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ categories }),
    })
    return response
  }
}

// Create and export a singleton instance
const categoryServiceInstance = new CategoryServiceClass()
export { categoryServiceInstance as CategoryService }

// Convenience functions
export const getCategories = () => categoryServiceInstance.getAll()
export const getMainCategories = () => categoryServiceInstance.getMainCategories()
export const getCategoryById = (id: string) => categoryServiceInstance.getById(id)
export const getCategoryBySlug = (slug: string) => categoryServiceInstance.getBySlug(slug)
