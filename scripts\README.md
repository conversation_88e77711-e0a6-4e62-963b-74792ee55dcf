# 🌱 Database Seeding Scripts

This directory contains comprehensive database seeding scripts designed to resolve the stock data inconsistency issue and populate your e-commerce database with realistic product data.

## 🚀 Quick Start

```bash
# Complete setup (recommended)
npm run seed:comprehensive

# Verify results
npm run seed:verify
```

## 📁 Script Overview

### **Main Scripts**

| Script | Purpose | Usage |
|--------|---------|-------|
| `seed-with-migration.ts` | Complete database setup | `npm run seed:comprehensive` |
| `comprehensive-seed.ts` | Product catalog creation | `npx tsx scripts/comprehensive-seed.ts` |
| `apply-stock-migration.ts` | Database migration only | `npm run seed:migration` |
| `verify-seeding.ts` | Verify seeding results | `npm run seed:verify` |

### **Legacy Scripts**

| Script | Purpose | Usage |
|--------|---------|-------|
| `seed-all.ts` | Original comprehensive seeder | `npm run seed:all` |

## 🎯 What Gets Created

### **Database Structure**
- ✅ Stock management fields migration
- ✅ 8 product categories
- ✅ 80-96 products (10-12 per category)
- ✅ 240-384 product images
- ✅ 320-480 product specifications
- ✅ 80-192 product certificates

### **Stock Data**
- ✅ Realistic stock quantities (0-100 units)
- ✅ Appropriate minimum thresholds (5-20 units)
- ✅ Maximum capacity limits (40-200 units)
- ✅ Calculated stock status (IN_STOCK/LOW_STOCK/OUT_OF_STOCK)

### **Product Information**
- ✅ Turkish product names and descriptions
- ✅ Realistic pricing in TRY (5-600 TL)
- ✅ Popular safety equipment brands
- ✅ Technical specifications
- ✅ Safety certificates (CE, TSE, ISO)

## 🔧 Individual Script Usage

### **Complete Setup**
```bash
# Applies migration + seeds everything + verifies
npm run seed:comprehensive
```

### **Migration Only**
```bash
# Just applies the stock management fields migration
npm run seed:migration
```

### **Products Only**
```bash
# Seeds products (requires existing categories and migration)
npx tsx scripts/comprehensive-seed.ts
```

### **Verification**
```bash
# Checks if seeding was successful
npm run seed:verify
```

## 📊 Expected Output

### **Successful Seeding**
```
🎉 Database setup completed successfully!

📊 Setup Summary:
  ✅ Stock management migration: Applied
  ✅ Categories: 8 available
  ✅ Products: Comprehensive catalog created
  ✅ Data integrity: Verified

🎯 Stock Distribution:
  In Stock: 65 products
  Low Stock: 25 products
  Out of Stock: 8 products
```

### **Verification Results**
```
✅ Verification completed successfully!

🎯 Stock Data Inconsistency Status: RESOLVED
💡 Both product detail page and edit form will now show consistent stock data.
```

## 🐛 Troubleshooting

### **Migration Issues**
```bash
# If migration fails
npx prisma db push
npm run seed:comprehensive
```

### **Category Issues**
```bash
# If categories are missing
npm run seed:all  # Creates categories first
```

### **Memory Issues**
```bash
# Reduce products per category in comprehensive-seed.ts
# Change: getRandomInt(10, 12) to getRandomInt(6, 8)
```

### **Database Connection**
```bash
# Check your .env file
DATABASE_URL="postgresql://username:password@localhost:5432/database"
```

## 🔄 Re-seeding

### **Complete Reset**
```bash
npm run db:reset
npm run seed:comprehensive
```

### **Products Only**
```bash
# Clears existing products and creates new ones
npx tsx scripts/comprehensive-seed.ts
```

## 📈 Customization

### **Stock Scenarios**
Edit `stockScenarios` in `comprehensive-seed.ts`:
```typescript
const stockScenarios = [
  { name: "Custom", stockQuantity: 50, minStockThreshold: 10, status: "IN_STOCK" }
]
```

### **Product Templates**
Add new product types in `productTemplates` object:
```typescript
"New Category": [
  {
    name: "Product Template",
    description: "Product description",
    models: ["Model1", "Model2"],
    priceRange: { min: 10, max: 100 }
  }
]
```

### **Brands**
Modify `turkishBrands` array to add/remove brands:
```typescript
const turkishBrands = ["Brand1", "Brand2", "Brand3"]
```

## ✅ Success Indicators

After seeding, you should see:

1. **Admin Panel**: Products with realistic stock quantities
2. **Product Detail**: Actual stock numbers (not "Evet")
3. **Product Edit**: Same stock numbers as detail page
4. **API Responses**: Include stockQuantity, minStockThreshold, stockStatus
5. **Database**: No null stock values

## 🎯 Issue Resolution

This seeding specifically resolves:

- ❌ **Before**: Product detail shows "Evet", edit form shows "0"
- ✅ **After**: Both show actual numbers like "45 units"

The stock data inconsistency between product detail page and edit form is completely resolved with this comprehensive seeding approach.
