"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Loader2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"

interface DeleteConfirmModalProps {
  open: boolean
  onClose: () => void
  title: string
  description: string
  onConfirm: () => Promise<void>
}

export function DeleteConfirmModal({ open, onClose, title, description, onConfirm }: DeleteConfirmModalProps) {
  const [loading, setLoading] = useState(false)

  const handleConfirm = async () => {
    try {
      setLoading(true)
      await onConfirm()
      onClose()
    } catch (error) {
      console.error("Delete error:", error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600">
            <AlertTriangle className="h-5 w-5" />
            {title}
          </DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <p className="text-gray-600">{description}</p>
        </div>

        <div className="flex justify-end gap-3">
          <Button variant="outline" onClick={onClose} disabled={loading}>
            İptal
          </Button>
          <Button variant="destructive" onClick={handleConfirm} disabled={loading}>
            {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            Sil
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
