import { initializeStorage } from './file-storage'

let initialized = false

export async function initializeApp() {
  if (initialized) return
  
  try {
    console.log('🚀 Initializing application...')
    
    // Initialize file storage
    await initializeStorage()
    console.log('✅ File storage initialized')
    
    initialized = true
    console.log('🎉 Application initialized successfully')
  } catch (error) {
    console.error('❌ Failed to initialize application:', error)
    throw error
  }
}

// Auto-initialize in development
if (process.env.NODE_ENV === 'development') {
  initializeApp().catch(console.error)
}
