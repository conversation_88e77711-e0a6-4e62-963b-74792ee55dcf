import { promises as fs } from 'fs'
import { existsSync } from 'fs'
import path from 'path'

export interface StorageConfig {
  baseDir: string
  maxFileSize: number
  allowedTypes: string[]
  subdirectories: string[]
}

export class FileStorage {
  private config: StorageConfig

  constructor(config: StorageConfig) {
    this.config = config
  }

  // Initialize storage directories
  async initialize(): Promise<void> {
    try {
      // Create base directory if it doesn't exist
      if (!existsSync(this.config.baseDir)) {
        await fs.mkdir(this.config.baseDir, { recursive: true })
      }

      // Create subdirectories
      for (const subdir of this.config.subdirectories) {
        const fullPath = path.join(this.config.baseDir, subdir)
        if (!existsSync(fullPath)) {
          await fs.mkdir(fullPath, { recursive: true })
        }
      }

      console.log('File storage initialized successfully')
    } catch (error) {
      console.error('Failed to initialize file storage:', error)
      throw error
    }
  }

  // Get full path for a file
  getFilePath(filename: string, subdirectory?: string): string {
    if (subdirectory) {
      return path.join(this.config.baseDir, subdirectory, filename)
    }
    return path.join(this.config.baseDir, filename)
  }

  // Get public URL for a file
  getPublicUrl(filename: string, subdirectory?: string): string {
    const relativePath = this.config.baseDir.replace(process.cwd(), '').replace(/\\/g, '/')
    if (subdirectory) {
      return `${relativePath}/${subdirectory}/${filename}`.replace('/public', '')
    }
    return `${relativePath}/${filename}`.replace('/public', '')
  }

  // Check if file exists
  async fileExists(filename: string, subdirectory?: string): Promise<boolean> {
    const filePath = this.getFilePath(filename, subdirectory)
    return existsSync(filePath)
  }

  // Save file to storage
  async saveFile(buffer: Buffer, filename: string, subdirectory?: string): Promise<string> {
    const filePath = this.getFilePath(filename, subdirectory)
    
    try {
      await fs.writeFile(filePath, buffer)
      return this.getPublicUrl(filename, subdirectory)
    } catch (error) {
      console.error('Failed to save file:', error)
      throw new Error(`Failed to save file: ${filename}`)
    }
  }

  // Delete file from storage
  async deleteFile(filename: string, subdirectory?: string): Promise<void> {
    const filePath = this.getFilePath(filename, subdirectory)
    
    try {
      if (existsSync(filePath)) {
        await fs.unlink(filePath)
      }
    } catch (error) {
      console.error('Failed to delete file:', error)
      throw new Error(`Failed to delete file: ${filename}`)
    }
  }

  // Delete multiple files
  async deleteFiles(filenames: string[], subdirectory?: string): Promise<void> {
    const deletePromises = filenames.map(filename => 
      this.deleteFile(filename, subdirectory).catch(error => {
        console.warn(`Failed to delete ${filename}:`, error)
      })
    )
    
    await Promise.all(deletePromises)
  }

  // Get file info
  async getFileInfo(filename: string, subdirectory?: string): Promise<{
    size: number
    createdAt: Date
    modifiedAt: Date
  } | null> {
    const filePath = this.getFilePath(filename, subdirectory)
    
    try {
      const stats = await fs.stat(filePath)
      return {
        size: stats.size,
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime,
      }
    } catch (error) {
      return null
    }
  }

  // List files in directory
  async listFiles(subdirectory?: string): Promise<string[]> {
    const dirPath = subdirectory 
      ? path.join(this.config.baseDir, subdirectory)
      : this.config.baseDir
    
    try {
      const files = await fs.readdir(dirPath)
      return files.filter(file => {
        const filePath = path.join(dirPath, file)
        return fs.stat(filePath).then(stats => stats.isFile()).catch(() => false)
      })
    } catch (error) {
      console.error('Failed to list files:', error)
      return []
    }
  }

  // Clean up old files (older than specified days)
  async cleanupOldFiles(days: number, subdirectory?: string): Promise<number> {
    const dirPath = subdirectory 
      ? path.join(this.config.baseDir, subdirectory)
      : this.config.baseDir
    
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - days)
    
    let deletedCount = 0
    
    try {
      const files = await fs.readdir(dirPath)
      
      for (const file of files) {
        const filePath = path.join(dirPath, file)
        const stats = await fs.stat(filePath)
        
        if (stats.isFile() && stats.mtime < cutoffDate) {
          await fs.unlink(filePath)
          deletedCount++
        }
      }
    } catch (error) {
      console.error('Failed to cleanup old files:', error)
    }
    
    return deletedCount
  }

  // Get storage usage statistics
  async getStorageStats(subdirectory?: string): Promise<{
    totalFiles: number
    totalSize: number
    averageFileSize: number
  }> {
    const dirPath = subdirectory 
      ? path.join(this.config.baseDir, subdirectory)
      : this.config.baseDir
    
    let totalFiles = 0
    let totalSize = 0
    
    try {
      const files = await fs.readdir(dirPath)
      
      for (const file of files) {
        const filePath = path.join(dirPath, file)
        const stats = await fs.stat(filePath)
        
        if (stats.isFile()) {
          totalFiles++
          totalSize += stats.size
        }
      }
    } catch (error) {
      console.error('Failed to get storage stats:', error)
    }
    
    return {
      totalFiles,
      totalSize,
      averageFileSize: totalFiles > 0 ? totalSize / totalFiles : 0,
    }
  }
}

// Default storage configurations
export const productImageStorage = new FileStorage({
  baseDir: path.join(process.cwd(), 'public/uploads/products'),
  maxFileSize: 5 * 1024 * 1024, // 5MB
  allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  subdirectories: ['thumbnails', 'medium', 'original'],
})

export const categoryImageStorage = new FileStorage({
  baseDir: path.join(process.cwd(), 'public/uploads/categories'),
  maxFileSize: 2 * 1024 * 1024, // 2MB
  allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/svg+xml'],
  subdirectories: ['icons', 'banners'],
})

export const documentStorage = new FileStorage({
  baseDir: path.join(process.cwd(), 'public/uploads/documents'),
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  subdirectories: ['certificates', 'manuals', 'datasheets'],
})

// Initialize all storage systems
export async function initializeStorage(): Promise<void> {
  await Promise.all([
    productImageStorage.initialize(),
    categoryImageStorage.initialize(),
    documentStorage.initialize(),
  ])
}
