import { NextRequest, NextResponse } from 'next/server'
import { writeFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'
import sharp from 'sharp'
import { createSuccessResponse, createErrorResponse, handleAsyncOperation } from '@/lib/validation-middleware'

const UPLOAD_DIR = path.join(process.cwd(), 'public/uploads/products')
const MAX_FILE_SIZE = 5 * 1024 * 1024 // 5MB
const ALLOWED_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']

// Ensure upload directory exists
async function ensureUploadDir() {
  if (!existsSync(UPLOAD_DIR)) {
    await mkdir(UPLOAD_DIR, { recursive: true })
  }
}

// Generate unique filename
function generateFileName(originalName: string): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 15)
  const extension = path.extname(originalName).toLowerCase()
  return `${timestamp}-${random}${extension}`
}

// Process image with Sharp
async function processImage(buffer: Buffer, filename: string) {
  const baseName = path.parse(filename).name
  const processedImages = []

  // Original size (optimized)
  const originalBuffer = await sharp(buffer)
    .jpeg({ quality: 90, progressive: true })
    .toBuffer()
  
  const originalPath = path.join(UPLOAD_DIR, `${baseName}-original.jpg`)
  await writeFile(originalPath, originalBuffer)
  
  const originalMetadata = await sharp(originalBuffer).metadata()
  processedImages.push({
    size: 'original',
    filename: `${baseName}-original.jpg`,
    url: `/uploads/products/${baseName}-original.jpg`,
    width: originalMetadata.width || 0,
    height: originalMetadata.height || 0,
    fileSize: originalBuffer.length
  })

  // Thumbnail (300x300)
  const thumbnailBuffer = await sharp(buffer)
    .resize(300, 300, { fit: 'cover', position: 'center' })
    .jpeg({ quality: 85 })
    .toBuffer()
  
  const thumbnailPath = path.join(UPLOAD_DIR, `${baseName}-thumb.jpg`)
  await writeFile(thumbnailPath, thumbnailBuffer)
  
  processedImages.push({
    size: 'thumbnail',
    filename: `${baseName}-thumb.jpg`,
    url: `/uploads/products/${baseName}-thumb.jpg`,
    width: 300,
    height: 300,
    fileSize: thumbnailBuffer.length
  })

  // Medium (800x600)
  const mediumBuffer = await sharp(buffer)
    .resize(800, 600, { fit: 'inside', withoutEnlargement: true })
    .jpeg({ quality: 85 })
    .toBuffer()
  
  const mediumPath = path.join(UPLOAD_DIR, `${baseName}-medium.jpg`)
  await writeFile(mediumPath, mediumBuffer)
  
  const mediumMetadata = await sharp(mediumBuffer).metadata()
  processedImages.push({
    size: 'medium',
    filename: `${baseName}-medium.jpg`,
    url: `/uploads/products/${baseName}-medium.jpg`,
    width: mediumMetadata.width || 0,
    height: mediumMetadata.height || 0,
    fileSize: mediumBuffer.length
  })

  return processedImages
}

export async function POST(request: NextRequest) {
  try {
    await ensureUploadDir()

    const formData = await request.formData()
    const files = formData.getAll('files') as File[]

    if (!files || files.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No files uploaded' },
        { status: 400 }
      )
    }

    const uploadedImages = []

    for (const file of files) {
      // Validate file type
      if (!ALLOWED_TYPES.includes(file.type)) {
        return NextResponse.json(
          { success: false, error: `Invalid file type: ${file.type}. Allowed types: ${ALLOWED_TYPES.join(', ')}` },
          { status: 400 }
        )
      }

      // Validate file size
      if (file.size > MAX_FILE_SIZE) {
        return NextResponse.json(
          { success: false, error: `File too large: ${file.name}. Maximum size: ${MAX_FILE_SIZE / 1024 / 1024}MB` },
          { status: 400 }
        )
      }

      // Convert file to buffer
      const bytes = await file.arrayBuffer()
      const buffer = Buffer.from(bytes)

      // Generate filename
      const filename = generateFileName(file.name)

      try {
        // Process image with different sizes
        const processedImages = await processImage(buffer, filename)
        
        // Create image record for database
        const imageRecord = {
          id: `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          originalName: file.name,
          filename: processedImages[0].filename, // Use original size as main
          url: processedImages[0].url,
          alt: path.parse(file.name).name,
          title: path.parse(file.name).name,
          sortOrder: 0,
          isMain: uploadedImages.length === 0, // First image is main
          size: processedImages[0].fileSize,
          width: processedImages[0].width,
          height: processedImages[0].height,
          format: 'jpg',
          variants: processedImages
        }

        uploadedImages.push(imageRecord)
      } catch (error) {
        console.error('Error processing image:', error)
        return NextResponse.json(
          { success: false, error: `Error processing image: ${file.name}` },
          { status: 500 }
        )
      }
    }

    return NextResponse.json({
      success: true,
      message: `Successfully uploaded ${uploadedImages.length} image(s)`,
      data: uploadedImages
    })

  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle file deletion
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const filename = searchParams.get('filename')

    if (!filename) {
      return NextResponse.json(
        { success: false, error: 'Filename is required' },
        { status: 400 }
      )
    }

    // Delete all variants of the image
    const baseName = path.parse(filename).name.replace(/-original$|-thumb$|-medium$/, '')
    const variants = ['original', 'thumb', 'medium']
    
    for (const variant of variants) {
      const filePath = path.join(UPLOAD_DIR, `${baseName}-${variant}.jpg`)
      try {
        const fs = await import('fs/promises')
        await fs.unlink(filePath)
      } catch (error) {
        // File might not exist, continue
        console.warn(`Could not delete ${filePath}:`, error)
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Image deleted successfully'
    })

  } catch (error) {
    console.error('Delete error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
