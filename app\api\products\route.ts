import { type NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { withValidation, createSuccessResponse, createErrorResponse, handleAsyncOperation } from "@/lib/validation-middleware"
import { productQuerySchema, createProductSchema } from "@/lib/validations"
import type { Product } from "@/types"

const getProductsHandler = withValidation(
  async (request: NextRequest, context: any, { query }: { query: any }) => {
    const operation = await handleAsyncOperation(async () => {
      console.log("Products API called with validated query:", query)

      // Build where clause for Prisma
      const where: any = {}

      // Search filter
      if (query.search) {
        where.OR = [
          { name: { contains: query.search, mode: 'insensitive' } },
          { description: { contains: query.search, mode: 'insensitive' } },
          { brand: { contains: query.search, mode: 'insensitive' } },
        ]
      }

      // Category filter
      if (query.categoryId) {
        where.categoryId = query.categoryId
      } else if (query.category) {
        where.category = {
          OR: [
            { slug: query.category },
            { name: { contains: query.category, mode: 'insensitive' } }
          ]
        }
      }

      // Brand filter
      if (query.brand) {
        where.brand = { equals: query.brand, mode: 'insensitive' }
      }

      // Price filters
      if (query.minPrice > 0 || query.maxPrice < 999999) {
        where.price = {}
        if (query.minPrice > 0) where.price.gte = query.minPrice
        if (query.maxPrice < 999999) where.price.lte = query.maxPrice
      }

      // Boolean filters
      if (query.isActive !== undefined) {
        where.isActive = query.isActive === "true"
      }
      if (query.isFeatured !== undefined) {
        where.isFeatured = query.isFeatured === "true"
      }
      if (query.isNew !== undefined) {
        where.isNew = query.isNew === "true"
      }
      if (query.isOnSale !== undefined) {
        where.isOnSale = query.isOnSale === "true"
      }
      if (query.trackStock !== undefined) {
        where.trackStock = query.trackStock === "true"
      }

      // Build orderBy for Prisma
      const orderBy: any = {}
      orderBy[query.sortBy] = query.sortOrder

      // Get total count
      const total = await prisma.product.count({ where })

      // Get paginated products with relations
      const products = await prisma.product.findMany({
        where,
        orderBy,
        skip: (query.page - 1) * query.limit,
        take: query.limit,
        include: {
          category: true,
          images: {
            orderBy: { sortOrder: 'asc' }
          },
          videos: {
            orderBy: { sortOrder: 'asc' }
          },
          specifications: {
            orderBy: { sortOrder: 'asc' }
          },
          certificates: true,
          discounts: {
            orderBy: { priority: 'desc' }
          },
        },
      })

      // Transform Prisma data to match existing Product interface
      const transformedProducts: Product[] = products.map(product => {
        try {
          return {
        id: product.id,
        name: product.name,
        slug: product.slug,
        description: product.description,
        shortDescription: product.shortDescription,
        sku: `PRD-${product.id.slice(-8)}`, // Generate SKU from ID
        barcode: null, // Not available at product level
        categoryId: product.categoryId,
        category: product.category,
        brand: product.brand,
        model: product.model,
        price: product.basePrice, // Use basePrice from Product model
        originalPrice: product.basePrice, // Same as price for now
        costPrice: product.baseCostPrice || 0,
        taxRate: product.taxRate,
        currency: product.currency,
        stockQuantity: product.stockQuantity,
        minStockThreshold: product.minStockThreshold,
        // Legacy fields for backward compatibility
        stock: product.stockQuantity,
        minStock: product.minStockThreshold,
        stockStatus: (product.stockStatus || 'IN_STOCK').toLowerCase() as "in_stock" | "out_of_stock" | "low_stock",
        trackStock: product.trackStock,
        images: product.images,
        videos: product.videos,
        specifications: product.specifications,
        certificates: product.certificates,
        discounts: product.discounts?.map(discount => ({
          ...discount,
          conditions: discount.conditions ? (typeof discount.conditions === 'string' ? JSON.parse(discount.conditions) : discount.conditions) : null,
          customerGroups: discount.customerGroups ? (typeof discount.customerGroups === 'string' ? JSON.parse(discount.customerGroups) : discount.customerGroups) : null,
          regions: discount.regions ? (typeof discount.regions === 'string' ? JSON.parse(discount.regions) : discount.regions) : null,
        })),
        seoTitle: product.seoTitle,
        seoDescription: product.seoDescription,
        metaKeywords: product.metaKeywords ? (typeof product.metaKeywords === 'string' ? JSON.parse(product.metaKeywords) : product.metaKeywords) : undefined,
        isActive: product.isActive,
        isFeatured: product.isFeatured,
        isNew: product.isNew,
        isOnSale: product.isOnSale,
        weight: product.weight,
        dimensions: product.dimensions ? (typeof product.dimensions === 'string' ? JSON.parse(product.dimensions) : product.dimensions) : undefined,
        createdAt: product.createdAt,
        updatedAt: product.updatedAt,
        publishedAt: product.publishedAt,
          }
        } catch (error) {
          console.error('Error transforming product:', product.id, error)
          return null
        }
      }).filter(Boolean) as Product[]

      const totalPages = Math.ceil(total / query.limit)

      return {
        data: transformedProducts,
        pagination: {
          total,
          page: query.page,
          limit: query.limit,
          totalPages,
          hasNext: query.page < totalPages,
          hasPrev: query.page > 1,
        },
      }
    }, 'Failed to fetch products')

    if (!operation.success) {
      return createErrorResponse(operation.error, 500)
    }

    return createSuccessResponse(operation.data)
  },
  { querySchema: productQuerySchema }
)

export const GET = getProductsHandler

const createProductHandler = withValidation(
  async (request: NextRequest, context: any, { body }: { body: any }) => {
    const operation = await handleAsyncOperation(async () => {
      console.log("Create product request:", body)

      // Generate slug if not provided
      const slug = body.slug || body.name.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .trim()

      // Create product in database
      const product = await prisma.product.create({
        data: {
          name: body.name,
          slug: slug,
          description: body.description,
          shortDescription: body.shortDescription || body.description.substring(0, 200),
          sku: body.sku || `SKU-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          barcode: body.barcode,
          categoryId: body.categoryId,
          brand: body.brand,
          model: body.model,
          // Enhanced pricing
          basePrice: body.basePrice || body.price,
          originalPrice: body.originalPrice,
          baseCostPrice: body.baseCostPrice || body.costPrice,
          zakatAmount: body.zakatAmount,
          taxRate: body.taxRate || 0,
          currency: body.currency || 'TRY',
          // Stock management
          trackStock: body.trackStock !== false,
          stockQuantity: body.stockQuantity,
          minStockThreshold: body.minStockThreshold,
          maxStockCapacity: body.maxStockCapacity,
          stockLocation: body.stockLocation,
          reorderPoint: body.reorderPoint,
          reorderQuantity: body.reorderQuantity,
          // Legacy fields for backward compatibility
          price: body.basePrice || body.price,
          costPrice: body.baseCostPrice || body.costPrice,
          stock: body.stockQuantity || body.stock || 0,
          minStock: body.minStockThreshold || body.minStock || 0,
          stockStatus: body.stockStatus ? body.stockStatus.toUpperCase() : 'IN_STOCK',
          // Status fields
          isActive: body.isActive !== false,
          isFeatured: body.isFeatured || false,
          isNew: body.isNew || false,
          isOnSale: body.isOnSale || false,
          // SEO fields
          seoTitle: body.seoTitle,
          seoDescription: body.seoDescription,
          metaKeywords: body.metaKeywords ? JSON.stringify(body.metaKeywords) : null,
          // Physical properties
          weight: body.weight,
          dimensions: body.dimensions ? JSON.stringify(body.dimensions) : null,
          publishedAt: new Date(),
        },
        include: {
          category: true,
          images: true,
          videos: true,
          specifications: true,
          certificates: true,
        },
      })

    // Create related data if provided
    if (body.images && body.images.length > 0) {
      await prisma.productImage.createMany({
        data: body.images.map((image: any, index: number) => ({
          productId: product.id,
          url: image.url,
          alt: image.alt,
          title: image.title,
          sortOrder: index,
          isMain: image.isMain || index === 0,
          size: image.size || 0,
          width: image.width || 400,
          height: image.height || 400,
          format: image.format || 'jpg',
        })),
      })
    }

    if (body.specifications && body.specifications.length > 0) {
      await prisma.productSpecification.createMany({
        data: body.specifications.map((spec: any, index: number) => ({
          productId: product.id,
          name: spec.name,
          value: spec.value,
          unit: spec.unit,
          sortOrder: index,
        })),
      })
    }

    if (body.certificates && body.certificates.length > 0) {
      await prisma.productCertificate.createMany({
        data: body.certificates.map((cert: any) => ({
          productId: product.id,
          name: cert.name,
          issuer: cert.issuer || 'Unknown',
          number: cert.number,
          validUntil: cert.validUntil ? new Date(cert.validUntil) : null,
          documentUrl: cert.documentUrl,
        })),
      })
    }

    // Create discounts if provided
    if (body.discounts && body.discounts.length > 0) {
      await prisma.productDiscount.createMany({
        data: body.discounts.map((discount: any) => ({
          productId: product.id,
          name: discount.name,
          code: discount.code,
          description: discount.description,
          type: discount.type,
          value: discount.value,
          maxDiscount: discount.maxDiscount,
          minOrderAmount: discount.minOrderAmount,
          buyQuantity: discount.buyQuantity,
          getQuantity: discount.getQuantity,
          getDiscountPercent: discount.getDiscountPercent,
          startDate: new Date(discount.startDate),
          endDate: new Date(discount.endDate),
          usageLimit: discount.usageLimit,
          userLimit: discount.userLimit,
          conditions: discount.conditions ? JSON.stringify(discount.conditions) : null,
          customerGroups: discount.customerGroups ? JSON.stringify(discount.customerGroups) : null,
          regions: discount.regions ? JSON.stringify(discount.regions) : null,
          isActive: discount.isActive !== false,
          priority: discount.priority || 0,
          stackable: discount.stackable || false,
          usageCount: 0,
          clickCount: 0,
          conversionCount: 0,
          totalSavings: 0,
        })),
      })
    }

    // Get the complete product with all relations
    const completeProduct = await prisma.product.findUnique({
      where: { id: product.id },
      include: {
        category: true,
        images: { orderBy: { sortOrder: 'asc' } },
        videos: { orderBy: { sortOrder: 'asc' } },
        specifications: { orderBy: { sortOrder: 'asc' } },
        certificates: true,
      },
    })

    // Transform to match existing interface
    const transformedProduct: Product = {
      id: completeProduct!.id,
      name: completeProduct!.name,
      slug: completeProduct!.slug,
      description: completeProduct!.description,
      shortDescription: completeProduct!.shortDescription,
      sku: completeProduct!.sku,
      barcode: completeProduct!.barcode,
      categoryId: completeProduct!.categoryId,
      category: completeProduct!.category,
      brand: completeProduct!.brand,
      model: completeProduct!.model,
      price: completeProduct!.price,
      originalPrice: completeProduct!.originalPrice,
      costPrice: completeProduct!.costPrice,
      taxRate: completeProduct!.taxRate,
      currency: completeProduct!.currency,
      stock: completeProduct!.stock,
      minStock: completeProduct!.minStock,
      stockStatus: "in_stock" as "in_stock" | "out_of_stock" | "low_stock",
      trackStock: completeProduct!.trackStock,
      images: completeProduct!.images,
      videos: completeProduct!.videos,
      specifications: completeProduct!.specifications,
      certificates: completeProduct!.certificates,
      seoTitle: completeProduct!.seoTitle,
      seoDescription: completeProduct!.seoDescription,
      metaKeywords: completeProduct!.metaKeywords ? JSON.parse(completeProduct!.metaKeywords) : undefined,
      isActive: completeProduct!.isActive,
      isFeatured: completeProduct!.isFeatured,
      isNew: completeProduct!.isNew,
      isOnSale: completeProduct!.isOnSale,
      weight: completeProduct!.weight,
      dimensions: completeProduct!.dimensions ? JSON.parse(completeProduct!.dimensions) : undefined,
      createdAt: completeProduct!.createdAt,
      updatedAt: completeProduct!.updatedAt,
      publishedAt: completeProduct!.publishedAt,
    }

      return transformedProduct
    }, 'Failed to create product')

    if (!operation.success) {
      return createErrorResponse(operation.error, 500)
    }

    return createSuccessResponse(operation.data, "Ürün başarıyla oluşturuldu", 201)
  },
  { bodySchema: createProductSchema }
)

export const POST = createProductHandler
