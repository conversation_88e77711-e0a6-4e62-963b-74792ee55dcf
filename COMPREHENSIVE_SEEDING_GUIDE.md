# 🌱 Comprehensive Database Seeding Guide

This guide provides complete instructions for populating your e-commerce database with a realistic product catalog that resolves the stock data inconsistency issue.

## 🎯 **What This Seeding Accomplishes**

### **Stock Data Consistency Fix**
- ✅ Applies database migration for new stock management fields
- ✅ Creates products with consistent `stockQuantity`, `minStockThreshold`, and `stockStatus` data
- ✅ Ensures product detail page and edit form show identical stock information
- ✅ Resolves the "Evet" vs "0" stock display inconsistency

### **Comprehensive Product Catalog**
- 📦 **80-96 Products Total** (10-12 products per category)
- 🏷️ **8 Categories** evenly distributed
- 💰 **Realistic Turkish Pricing** (5-600 TL range)
- 📊 **5 Stock Scenarios** (High, Normal, Low, Critical, Out of Stock)
- 🏭 **15 Popular Brands** (3M, MSA, Uvex, Honeywell, etc.)
- 🇹🇷 **Turkish Content** (product names, descriptions)

## 🚀 **Quick Start (Recommended)**

### **Option 1: Complete Setup (One Command)**
```bash
# This handles everything: migration + categories + products
npm run seed:comprehensive
```

### **Option 2: Step by Step**
```bash
# Step 1: Apply stock management migration
npm run seed:migration

# Step 2: Seed comprehensive product catalog
npx tsx scripts/comprehensive-seed.ts
```

## 📋 **Detailed Process**

### **Step 1: Database Migration**
The seeding process first ensures your database has the required stock management fields:

```sql
-- Fields that will be added/verified:
ALTER TABLE "Product" ADD COLUMN IF NOT EXISTS "stockQuantity" INTEGER DEFAULT 0;
ALTER TABLE "Product" ADD COLUMN IF NOT EXISTS "minStockThreshold" INTEGER DEFAULT 0;
ALTER TABLE "Product" ADD COLUMN IF NOT EXISTS "maxStockCapacity" INTEGER DEFAULT 100;
ALTER TABLE "Product" ADD COLUMN IF NOT EXISTS "stockStatus" "ProductStockStatus" DEFAULT 'IN_STOCK';
```

### **Step 2: Category Verification**
Ensures all 8 categories exist:
- Baş Koruma (Head Protection)
- Göz Koruma (Eye Protection)  
- Kulak Koruma (Ear Protection)
- Solunum Koruma (Respiratory Protection)
- El Koruma (Hand Protection)
- Ayak Koruma (Foot Protection)
- Vücut Koruma (Body Protection)
- Yüksekte Çalışma (Working at Height)

### **Step 3: Product Creation**
For each category, creates 10-12 products with:

#### **Stock Management Data**
```typescript
// 5 different stock scenarios distributed across products:
{ stockQuantity: 85, minStockThreshold: 15, status: "IN_STOCK" }     // High Stock
{ stockQuantity: 45, minStockThreshold: 10, status: "IN_STOCK" }     // Normal Stock  
{ stockQuantity: 8,  minStockThreshold: 10, status: "LOW_STOCK" }    // Low Stock
{ stockQuantity: 3,  minStockThreshold: 5,  status: "LOW_STOCK" }    // Critical Stock
{ stockQuantity: 0,  minStockThreshold: 5,  status: "OUT_OF_STOCK" } // Out of Stock
```

#### **Product Information**
- **Names**: `{Brand} {Product Type} {Model}` (e.g., "3M Güvenlik Bareti H-700")
- **Descriptions**: Detailed Turkish descriptions with technical details
- **Pricing**: Realistic TRY pricing with 40% profit margins
- **Images**: Placeholder images with product names
- **Specifications**: Technical specs relevant to each product type
- **Certificates**: CE, TSE, ISO certifications

## 📊 **Expected Results**

After seeding, you'll have:

### **Database Statistics**
- 📂 **8 Categories** with proper SEO and metadata
- 📦 **80-96 Products** with complete information
- 🖼️ **240-384 Product Images** (2-4 per product)
- 📋 **320-480 Specifications** (4-5 per product)
- 🏆 **80-192 Certificates** (1-2 per product)

### **Stock Distribution**
- 🟢 **~60% In Stock** (Normal + High stock)
- 🟡 **~30% Low Stock** (Low + Critical stock)
- 🔴 **~10% Out of Stock**

### **Pricing Range**
- 💰 **Minimum**: 5 TL (kulak tıkacı)
- 💰 **Maximum**: 600 TL (yüksekte çalışma ekipmanı)
- 💰 **Average**: ~120 TL per product

## 🧪 **Testing the Fix**

After seeding, verify the stock consistency fix:

### **1. Product Detail Page Test**
```bash
# Visit any product detail page
http://localhost:3000/admin/urunler
# Click on any product
# Check stock status display
```

### **2. Product Edit Form Test**
```bash
# Click "Edit" on the same product
# Verify stock quantity matches the detail page
# Both should show the same values now
```

### **3. API Response Test**
```bash
# Check API response includes both new and legacy fields
curl http://localhost:3000/api/products/[product-id]
# Should include: stockQuantity, minStockThreshold, stockStatus
```

## 🔧 **Troubleshooting**

### **Migration Issues**
```bash
# If migration fails, try manual Prisma push:
npx prisma db push

# Or reset and start fresh:
npx prisma db reset
npm run seed:comprehensive
```

### **Category Issues**
```bash
# If categories are missing:
npx tsx scripts/seed-all.ts  # Creates categories first
```

### **Permission Issues**
```bash
# Ensure database permissions:
# PostgreSQL user needs CREATE, INSERT, UPDATE permissions
```

### **Memory Issues**
```bash
# If seeding fails due to memory:
# Reduce products per category in scripts/comprehensive-seed.ts
# Change: getRandomInt(10, 12) to getRandomInt(6, 8)
```

## 📁 **File Structure**

```
scripts/
├── comprehensive-seed.ts      # Main seeding logic
├── apply-stock-migration.ts   # Migration application
├── seed-with-migration.ts     # Combined migration + seeding
└── seed-all.ts               # Legacy seeder (still works)

prisma/
├── migrations/
│   └── add_stock_management_fields.sql  # Stock fields migration
├── seed.ts                   # Basic seeder
└── seed-products.ts          # Product-only seeder
```

## 🎯 **Success Indicators**

You'll know the seeding worked when:

1. ✅ **Admin Panel**: Product list shows realistic stock quantities
2. ✅ **Product Detail**: Stock status displays correctly (not just "Evet")
3. ✅ **Product Edit**: Stock fields show actual numbers (not 0)
4. ✅ **Consistency**: Detail page and edit form show identical values
5. ✅ **API**: Responses include both new and legacy stock fields
6. ✅ **Database**: All products have non-null stock values

## 🔄 **Maintenance**

### **Re-seeding**
```bash
# To completely refresh the catalog:
npm run db:reset
npm run seed:comprehensive
```

### **Adding More Products**
```bash
# Run seeding again (it clears existing products first):
npm run seed:comprehensive
```

### **Updating Stock Scenarios**
Edit `stockScenarios` array in `scripts/comprehensive-seed.ts` to customize stock distributions.

---

## 🎉 **Final Result**

After running this comprehensive seeding:

- ❌ **Before**: Product detail shows "Evet", edit form shows "0" (inconsistent)
- ✅ **After**: Both show actual stock quantities like "45 units" (consistent)

The stock data inconsistency issue will be completely resolved with realistic, consistent data across your entire product catalog!
