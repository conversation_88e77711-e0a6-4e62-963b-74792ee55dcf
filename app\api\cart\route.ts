import { NextRequest, NextResponse } from 'next/server'
import { CartManager } from '@/lib/services/CartManager'
import { z } from 'zod'

// Validation schemas
const addItemSchema = z.object({
  productId: z.string().min(1, 'Product ID is required'),
  quantity: z.number().int().min(1, 'Quantity must be at least 1').max(100, 'Quantity cannot exceed 100')
})

const updateItemSchema = z.object({
  cartItemId: z.string().min(1, 'Cart item ID is required'),
  quantity: z.number().int().min(0, 'Quantity must be non-negative').max(100, 'Quantity cannot exceed 100')
})

const removeItemSchema = z.object({
  cartItemId: z.string().min(1, 'Cart item ID is required')
})

/**
 * Get cart summary
 * GET /api/cart?sessionId=xxx
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')
    
    if (!sessionId) {
      return NextResponse.json(
        { success: false, error: 'Session ID is required' },
        { status: 400 }
      )
    }
    
    const cartSummary = await CartManager.getCartSummary(sessionId)
    
    return NextResponse.json({
      success: true,
      data: cartSummary
    })
  } catch (error) {
    console.error('Get cart error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to get cart',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * Add item to cart
 * POST /api/cart
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { sessionId, userId, ...itemData } = body
    
    if (!sessionId) {
      return NextResponse.json(
        { success: false, error: 'Session ID is required' },
        { status: 400 }
      )
    }
    
    // Validate item data
    const validationResult = addItemSchema.safeParse(itemData)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid item data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }
    
    const result = await CartManager.addItem(
      sessionId,
      validationResult.data,
      userId
    )
    
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }
    
    return NextResponse.json({
      success: true,
      message: 'Item added to cart successfully',
      data: result.cartItem
    })
  } catch (error) {
    console.error('Add to cart error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to add item to cart',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * Update cart item quantity
 * PUT /api/cart
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { sessionId, ...updateData } = body
    
    if (!sessionId) {
      return NextResponse.json(
        { success: false, error: 'Session ID is required' },
        { status: 400 }
      )
    }
    
    // Validate update data
    const validationResult = updateItemSchema.safeParse(updateData)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid update data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }
    
    const result = await CartManager.updateItemQuantity(
      sessionId,
      validationResult.data.cartItemId,
      validationResult.data.quantity
    )
    
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }
    
    return NextResponse.json({
      success: true,
      message: 'Cart item updated successfully'
    })
  } catch (error) {
    console.error('Update cart item error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update cart item',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * Remove item from cart
 * DELETE /api/cart
 */
export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json()
    const { sessionId, ...removeData } = body
    
    if (!sessionId) {
      return NextResponse.json(
        { success: false, error: 'Session ID is required' },
        { status: 400 }
      )
    }
    
    // Validate remove data
    const validationResult = removeItemSchema.safeParse(removeData)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid remove data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }
    
    const result = await CartManager.removeItem(
      sessionId,
      validationResult.data.cartItemId
    )
    
    if (!result.success) {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      )
    }
    
    return NextResponse.json({
      success: true,
      message: 'Item removed from cart successfully'
    })
  } catch (error) {
    console.error('Remove cart item error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to remove cart item',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
