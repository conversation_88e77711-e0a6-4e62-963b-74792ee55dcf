import { type NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { slugify, calculatePagination } from "@/lib/utils"
import type { Category, CategoryFilter } from "@/types"

// GET /api/categories - Kategorileri listele
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)

    const filter: CategoryFilter = {
      search: searchParams.get("search") || undefined,
      isActive: searchParams.get("isActive") ? searchParams.get("isActive") === "true" : undefined,
      parentId: searchParams.has("parentId") ? (searchParams.get("parentId") === "null" ? null : searchParams.get("parentId")) : undefined,
      sortBy: (searchParams.get("sortBy") as any) || "sortOrder",
      sortOrder: (searchParams.get("sortOrder") as "asc" | "desc") || "asc",
      page: Number.parseInt(searchParams.get("page") || "1"),
      limit: Number.parseInt(searchParams.get("limit") || "10"),
    }

    // Build where clause for Prisma
    const where: any = {}

    // Search filter
    if (filter.search) {
      where.OR = [
        { name: { contains: filter.search, mode: 'insensitive' } },
        { description: { contains: filter.search, mode: 'insensitive' } },
      ]
    }

    // Active filter
    if (filter.isActive !== undefined) {
      where.isActive = filter.isActive
    }

    // Parent ID filter
    if (filter.parentId !== undefined) {
      if (filter.parentId === null || filter.parentId === "") {
        // Main categories (parentId is null)
        where.parentId = null
      } else {
        // Subcategories with specific parent
        where.parentId = filter.parentId
      }
    }

    // Build orderBy for Prisma
    const orderBy: any = {}
    orderBy[filter.sortBy!] = filter.sortOrder

    // Get total count
    const total = await prisma.category.count({ where })

    // Get paginated categories with children
    const categories = await prisma.category.findMany({
      where,
      orderBy,
      skip: (filter.page! - 1) * filter.limit!,
      take: filter.limit!,
      include: {
        children: {
          orderBy: { sortOrder: 'asc' }
        },
        products: {
          select: { id: true }
        }
      },
    })

    // Transform data to match existing interface
    const transformedCategories: Category[] = categories.map(category => ({
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      icon: category.icon,
      parentId: category.parentId,
      children: category.children,
      productCount: category.products.length,
      isActive: category.isActive,
      sortOrder: category.sortOrder,
      seoTitle: category.seoTitle,
      seoDescription: category.seoDescription,

      // Performans optimizasyonu
      cacheKey: category.cacheKey,
      viewCount: category.viewCount,
      popularityScore: category.popularityScore,

      // Indexing & Search
      searchKeywords: category.searchKeywords,
      isSearchable: category.isSearchable,

      // Görsel & UI/UX
      categoryImage: category.categoryImage,
      colorCode: category.colorCode,
      iconType: category.iconType,

      // Advanced SEO
      metaKeywords: category.metaKeywords,
      ogTitle: category.ogTitle,
      ogDescription: category.ogDescription,
      ogImage: category.ogImage,

      // Analytics & Tracking
      conversionRate: category.conversionRate,
      avgOrderValue: category.avgOrderValue,

      // Business Rules
      minOrderAmount: category.minOrderAmount,
      commissionRate: category.commissionRate,
      taxRate: category.taxRate,

      // Admin Features
      isPromoted: category.isPromoted,
      isFeatured: category.isFeatured,
      adminNotes: category.adminNotes,
      approvalStatus: category.approvalStatus,

      // Audit & History
      version: category.version,
      changeLog: category.changeLog,
      createdBy: category.createdBy,

      // Mobile Optimization
      mobileIcon: category.mobileIcon,
      mobileTemplate: category.mobileTemplate,

      createdAt: category.createdAt,
      updatedAt: category.updatedAt,
    }))

    const pagination = calculatePagination(filter.page!, filter.limit!, total)

    console.log('✅ Categories fetched successfully:', {
      totalCategories: transformedCategories.length,
      totalCount: total,
      currentPage: pagination.page,
      totalPages: pagination.totalPages,
      sampleCategories: transformedCategories.slice(0, 2).map(c => ({ id: c.id, name: c.name, isActive: c.isActive }))
    })

    const response = {
      data: transformedCategories,
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total: pagination.total,
        totalPages: pagination.totalPages,
        hasNext: pagination.hasNext,
        hasPrev: pagination.hasPrev,
      },
      success: true
    }

    console.log('📤 API Response structure:', {
      dataLength: response.data?.length || 0,
      paginationTotal: response.pagination?.total || 0,
      success: response.success
    })

    return NextResponse.json(response)
  } catch (error) {
    console.error("Categories GET error:", error)
    return NextResponse.json({ success: false, message: "Kategoriler yüklenirken hata oluştu" }, { status: 500 })
  }
}

// POST /api/categories - Yeni kategori oluştur
export async function POST(request: NextRequest) {
  try {
    const data = await request.json()

    // Validation
    if (!data.name || !data.description) {
      return NextResponse.json({ success: false, message: "Kategori adı ve açıklama zorunludur" }, { status: 400 })
    }

    // Generate slug
    const slug = slugify(data.name)

    // Check slug uniqueness
    const existingCategory = await prisma.category.findUnique({
      where: { slug }
    })

    if (existingCategory) {
      return NextResponse.json({ success: false, message: "Bu isimde bir kategori zaten mevcut" }, { status: 400 })
    }

    // Safely parse numeric values
    const parseNumber = (value: any, defaultValue: number = 0): number => {
      if (value === null || value === undefined || value === '') return defaultValue
      const parsed = Number(value)
      return isNaN(parsed) ? defaultValue : parsed
    }

    // Safely handle enum values
    const safeEnumValue = (value: any, validValues: string[], defaultValue?: string) => {
      if (!value) return defaultValue || null
      return validValues.includes(value) ? value : (defaultValue || null)
    }

    // Create new category
    const newCategory = await prisma.category.create({
      data: {
        name: data.name,
        slug,
        description: data.description,
        icon: data.icon || '',
        parentId: data.parentId || null,
        productCount: 0,
        isActive: data.isActive !== false,
        sortOrder: parseNumber(data.sortOrder, 0),
        seoTitle: data.seoTitle || null,
        seoDescription: data.seoDescription || null,

        // Performans optimizasyonu
        cacheKey: data.cacheKey || `cat_${slug}_tr`,
        viewCount: parseNumber(data.viewCount, 0),
        popularityScore: parseNumber(data.popularityScore, 0),

        // Indexing & Search
        searchKeywords: data.searchKeywords || null,
        isSearchable: data.isSearchable !== false,

        // Görsel & UI/UX
        categoryImage: data.categoryImage || null,
        colorCode: data.colorCode || '#3B82F6',
        iconType: safeEnumValue(data.iconType, ['SVG', 'PNG', 'FONT_ICON', 'EMOJI']),

        // Advanced SEO
        metaKeywords: data.metaKeywords || null,
        ogTitle: data.ogTitle || null,
        ogDescription: data.ogDescription || null,
        ogImage: data.ogImage || null,

        // Analytics & Tracking
        conversionRate: data.conversionRate ? parseNumber(data.conversionRate) : null,
        avgOrderValue: data.avgOrderValue ? parseNumber(data.avgOrderValue) : null,

        // Business Rules
        minOrderAmount: data.minOrderAmount ? parseNumber(data.minOrderAmount) : null,
        commissionRate: data.commissionRate ? parseNumber(data.commissionRate) : null,
        taxRate: data.taxRate ? parseNumber(data.taxRate) : null,

        // Admin Features
        isPromoted: data.isPromoted === true,
        isFeatured: data.isFeatured === true,
        adminNotes: data.adminNotes || null,
        approvalStatus: safeEnumValue(data.approvalStatus, ['TASLAK', 'BEKLEMEDE', 'ONAYLANDI', 'REDDEDILDI'], 'ONAYLANDI'),

        // Audit & History
        version: parseNumber(data.version, 1),
        changeLog: data.changeLog || JSON.stringify([{
          date: new Date().toISOString(),
          action: 'created',
          user: 'system',
          changes: 'Kategori oluşturuldu'
        }]),
        createdBy: data.createdBy || 'system',

        // Mobile Optimization
        mobileIcon: data.mobileIcon || null,
        mobileTemplate: safeEnumValue(data.mobileTemplate, ['VARSAYILAN', 'GRID', 'LISTE', 'KART', 'BANNER']),
      },
      include: {
        children: true,
        products: {
          select: { id: true }
        }
      }
    })

    // Transform to match existing interface
    const transformedCategory: Category = {
      id: newCategory.id,
      name: newCategory.name,
      slug: newCategory.slug,
      description: newCategory.description,
      icon: newCategory.icon,
      parentId: newCategory.parentId,
      children: newCategory.children,
      productCount: newCategory.products.length,
      isActive: newCategory.isActive,
      sortOrder: newCategory.sortOrder,
      seoTitle: newCategory.seoTitle,
      seoDescription: newCategory.seoDescription,

      // Performans optimizasyonu
      cacheKey: newCategory.cacheKey,
      viewCount: newCategory.viewCount,
      popularityScore: newCategory.popularityScore,

      // Indexing & Search
      searchKeywords: newCategory.searchKeywords,
      isSearchable: newCategory.isSearchable,

      // Görsel & UI/UX
      categoryImage: newCategory.categoryImage,
      colorCode: newCategory.colorCode,
      iconType: newCategory.iconType,

      // Advanced SEO
      metaKeywords: newCategory.metaKeywords,
      ogTitle: newCategory.ogTitle,
      ogDescription: newCategory.ogDescription,
      ogImage: newCategory.ogImage,

      // Analytics & Tracking
      conversionRate: newCategory.conversionRate,
      avgOrderValue: newCategory.avgOrderValue,

      // Business Rules
      minOrderAmount: newCategory.minOrderAmount,
      commissionRate: newCategory.commissionRate,
      taxRate: newCategory.taxRate,

      // Admin Features
      isPromoted: newCategory.isPromoted,
      isFeatured: newCategory.isFeatured,
      adminNotes: newCategory.adminNotes,
      approvalStatus: newCategory.approvalStatus,

      // Audit & History
      version: newCategory.version,
      changeLog: newCategory.changeLog,
      createdBy: newCategory.createdBy,

      // Mobile Optimization
      mobileIcon: newCategory.mobileIcon,
      mobileTemplate: newCategory.mobileTemplate,

      createdAt: newCategory.createdAt,
      updatedAt: newCategory.updatedAt,
    }

    return NextResponse.json({
      success: true,
      data: transformedCategory,
      message: "Kategori başarıyla oluşturuldu",
    })
  } catch (error) {
    console.error("Categories POST error:", error)
    return NextResponse.json({ success: false, message: "Kategori oluşturulurken hata oluştu" }, { status: 500 })
  }
}
