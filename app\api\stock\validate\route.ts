import { NextRequest, NextResponse } from 'next/server'
import { StockManager } from '@/lib/services/StockManager'
import { z } from 'zod'

const validateStockSchema = z.object({
  productId: z.string().min(1, 'Product ID is required'),
  quantity: z.number().int().min(1, 'Quantity must be at least 1')
})

const bulkValidateSchema = z.object({
  items: z.array(z.object({
    productId: z.string().min(1, 'Product ID is required'),
    quantity: z.number().int().min(1, 'Quantity must be at least 1')
  })).min(1, 'At least one item is required')
})

/**
 * Validate stock for a single product
 * GET /api/stock/validate?productId=xxx&quantity=1
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const productId = searchParams.get('productId')
    const quantityStr = searchParams.get('quantity')
    
    if (!productId || !quantityStr) {
      return NextResponse.json(
        { success: false, error: 'Product ID and quantity are required' },
        { status: 400 }
      )
    }
    
    const quantity = parseInt(quantityStr, 10)
    if (isNaN(quantity) || quantity < 1) {
      return NextResponse.json(
        { success: false, error: 'Invalid quantity' },
        { status: 400 }
      )
    }
    
    const validation = await StockManager.validateStock(productId, quantity)
    const stockInfo = await StockManager.getStockInfo(productId)
    
    return NextResponse.json({
      success: true,
      data: {
        isValid: validation.isValid,
        availableStock: validation.availableStock,
        requestedQuantity: validation.requestedQuantity,
        stockInfo,
        error: validation.error
      }
    })
  } catch (error) {
    console.error('Stock validation error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to validate stock',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * Bulk validate stock for multiple products
 * POST /api/stock/validate
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate request body
    const validationResult = bulkValidateSchema.safeParse(body)
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }
    
    const { items } = validationResult.data
    const results = []
    let hasErrors = false
    
    for (const item of items) {
      try {
        const validation = await StockManager.validateStock(item.productId, item.quantity)
        const stockInfo = await StockManager.getStockInfo(item.productId)
        
        results.push({
          productId: item.productId,
          quantity: item.quantity,
          isValid: validation.isValid,
          availableStock: validation.availableStock,
          stockInfo,
          error: validation.error
        })
        
        if (!validation.isValid) {
          hasErrors = true
        }
      } catch (error) {
        results.push({
          productId: item.productId,
          quantity: item.quantity,
          isValid: false,
          availableStock: 0,
          stockInfo: null,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
        hasErrors = true
      }
    }
    
    return NextResponse.json({
      success: true,
      data: {
        results,
        hasErrors,
        summary: {
          totalItems: items.length,
          validItems: results.filter(r => r.isValid).length,
          invalidItems: results.filter(r => !r.isValid).length
        }
      }
    })
  } catch (error) {
    console.error('Bulk stock validation error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to validate stock',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
