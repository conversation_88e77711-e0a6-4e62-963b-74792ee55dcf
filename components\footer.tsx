import Link from "next/link"
import { Facebook, Twitter, Instagram, Linkedin, Mail, Phone, MapPin } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

export function Footer() {
  return (
    <footer className="bg-slate-900 text-white">
      <div className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div>
            <div className="flex items-center gap-2 mb-6">
              <div className="bg-orange-500 text-white p-2 rounded-lg">
                <div className="w-8 h-8 flex items-center justify-center font-bold text-lg">İG</div>
              </div>
              <div>
                <div className="font-bold text-xl">İş Güvenliği</div>
                <div className="text-sm text-slate-400">Malzemeleri</div>
              </div>
            </div>
            <p className="text-slate-300 mb-6 leading-relaxed">
              25 yıldır iş güvenliği alanında hizmet veren, kaliteli ürünler ve güvenilir hizmet sunan lider firmayız.
            </p>
            <div className="flex gap-4">
              <Button size="icon" variant="ghost" className="hover:bg-orange-500">
                <Facebook className="h-5 w-5" />
              </Button>
              <Button size="icon" variant="ghost" className="hover:bg-orange-500">
                <Twitter className="h-5 w-5" />
              </Button>
              <Button size="icon" variant="ghost" className="hover:bg-orange-500">
                <Instagram className="h-5 w-5" />
              </Button>
              <Button size="icon" variant="ghost" className="hover:bg-orange-500">
                <Linkedin className="h-5 w-5" />
              </Button>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Hızlı Linkler</h3>
            <div className="space-y-3">
              <Link href="/hakkimizda" className="block text-slate-300 hover:text-orange-400 transition-colors">
                Hakkımızda
              </Link>
              <Link href="/urunler" className="block text-slate-300 hover:text-orange-400 transition-colors">
                Ürünler
              </Link>
              <Link href="/markalar" className="block text-slate-300 hover:text-orange-400 transition-colors">
                Markalar
              </Link>
              <Link href="/kampanyalar" className="block text-slate-300 hover:text-orange-400 transition-colors">
                Kampanyalar
              </Link>
              <Link href="/kurumsal-satislar" className="block text-slate-300 hover:text-orange-400 transition-colors">
                Kurumsal Satışlar
              </Link>
              <Link href="/bayi-basvuru" className="block text-slate-300 hover:text-orange-400 transition-colors">
                Bayi Başvuru
              </Link>
            </div>
          </div>

          {/* Customer Service */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Müşteri Hizmetleri</h3>
            <div className="space-y-3">
              <Link href="/iletisim" className="block text-slate-300 hover:text-orange-400 transition-colors">
                İletişim
              </Link>
              <Link href="/siparis-takibi" className="block text-slate-300 hover:text-orange-400 transition-colors">
                Sipariş Takibi
              </Link>
              <Link href="/iade-degisim" className="block text-slate-300 hover:text-orange-400 transition-colors">
                İade & Değişim
              </Link>
              <Link href="/sss" className="block text-slate-300 hover:text-orange-400 transition-colors">
                Sıkça Sorulan Sorular
              </Link>
              <Link
                href="/gizlilik-politikasi"
                className="block text-slate-300 hover:text-orange-400 transition-colors"
              >
                Gizlilik Politikası
              </Link>
              <Link href="/kullanim-kosullari" className="block text-slate-300 hover:text-orange-400 transition-colors">
                Kullanım Koşulları
              </Link>
            </div>
          </div>

          {/* Contact & Newsletter */}
          <div>
            <h3 className="text-lg font-semibold mb-6">İletişim</h3>
            <div className="space-y-4 mb-6">
              <div className="flex items-center gap-3">
                <Phone className="h-5 w-5 text-orange-400" />
                <span className="text-slate-300">0850 123 45 67</span>
              </div>
              <div className="flex items-center gap-3">
                <Mail className="h-5 w-5 text-orange-400" />
                <span className="text-slate-300"><EMAIL></span>
              </div>
              <div className="flex items-start gap-3">
                <MapPin className="h-5 w-5 text-orange-400 mt-1" />
                <span className="text-slate-300">
                  Organize Sanayi Bölgesi
                  <br />
                  1. Cadde No: 123
                  <br />
                  İstanbul, Türkiye
                </span>
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-3">Bülten Aboneliği</h4>
              <div className="flex gap-2">
                <Input placeholder="E-posta adresiniz" className="bg-slate-800 border-slate-700 text-white" />
                <Button className="bg-orange-500 hover:bg-orange-600">Abone Ol</Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-slate-800">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-slate-400 text-sm">© 2024 İş Güvenliği Malzemeleri. Tüm hakları saklıdır.</div>
            <div className="flex gap-6 text-sm">
              <Link href="/gizlilik-politikasi" className="text-slate-400 hover:text-orange-400">
                Gizlilik Politikası
              </Link>
              <Link href="/kullanim-kosullari" className="text-slate-400 hover:text-orange-400">
                Kullanım Koşulları
              </Link>
              <Link href="/cerez-politikasi" className="text-slate-400 hover:text-orange-400">
                Çerez Politikası
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
