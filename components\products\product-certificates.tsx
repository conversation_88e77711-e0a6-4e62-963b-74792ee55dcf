import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ExternalLink, FileText, Calendar } from "lucide-react"
import type { ProductCertificate } from "@/types"

interface ProductCertificatesProps {
  certificates: ProductCertificate[]
}

export function ProductCertificates({ certificates }: ProductCertificatesProps) {
  if (!certificates || certificates.length === 0) {
    return (
      <div className="text-center py-8">
        <FileText className="w-12 h-12 text-gray-300 mx-auto mb-4" />
        <p className="text-gray-500">Bu ürün için sertifika bilgisi bulunmamaktadır.</p>
      </div>
    )
  }

  const activeCertificates = certificates.filter((cert) => cert.isActive !== false)

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {activeCertificates.map((certificate, index) => (
        <Card key={certificate.id || index}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">{certificate.name}</CardTitle>
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                Aktif
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-600">Veren Kurum:</span>
                <span className="text-sm text-gray-900">{certificate.issuer}</span>
              </div>

              {certificate.certificateNumber && (
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Sertifika No:</span>
                  <span className="text-sm text-gray-900 font-mono">{certificate.certificateNumber}</span>
                </div>
              )}

              {certificate.issueDate && (
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Veriliş Tarihi:</span>
                  <span className="text-sm text-gray-900 flex items-center">
                    <Calendar className="w-3 h-3 mr-1" />
                    {new Date(certificate.issueDate).toLocaleDateString("tr-TR")}
                  </span>
                </div>
              )}

              {certificate.expiryDate && (
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-600">Geçerlilik:</span>
                  <span className="text-sm text-gray-900 flex items-center">
                    <Calendar className="w-3 h-3 mr-1" />
                    {new Date(certificate.expiryDate).toLocaleDateString("tr-TR")}
                  </span>
                </div>
              )}
            </div>

            {certificate.documentUrl && (
              <Button variant="outline" size="sm" className="w-full bg-transparent" asChild>
                <a href={certificate.documentUrl} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Sertifikayı Görüntüle
                </a>
              </Button>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
