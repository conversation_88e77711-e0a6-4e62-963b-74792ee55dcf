"use client"

import { useState, useEffect, use<PERSON>emo, use<PERSON><PERSON>back, memo } from "react"
import {
  Plus,
  Search,
  Filter,
  Grid,
  List,
  Edit,
  Trash2,
  MoreHorizontal,
  GripVertical,
  Eye,
  RefreshCw,
  Download,
  Upload,
  Settings,
  Package,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsContent } from "@/components/ui/tabs"
import { useDeleteCategory } from "@/lib/hooks/use-categories" // Removed useCategories - using direct fetch
import { CategoryService } from "@/lib/api/categories"
import { formatDate, debounce } from "@/lib/utils"
import { CategoryFormModal } from "@/components/admin/category-form-modal"
import { CategoryDetailModal } from "@/components/admin/category-detail-modal"
import { DeleteConfirmModal } from "@/components/admin/delete-confirm-modal"
import { CategoryBulkActions } from "@/components/admin/category-bulk-actions"
import { CategoryStats } from "@/components/admin/category-stats"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { ErrorMessage } from "@/components/ui/error-message"
import { SuccessMessage } from "@/components/ui/success-message"
import type { Category } from "@/types"
import Link from "next/link"
import { useRouter } from "next/navigation"

export default function CategoriesPage() {
  const router = useRouter()
  // View and filter states
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [searchQuery, setSearchQuery] = useState("")
  const [debouncedSearch, setDebouncedSearch] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [parentFilter, setParentFilter] = useState<string>("all")
  const [sortBy, setSortBy] = useState<string>("sortOrder")
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc")
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(12)

  // Modal states
  const [showAddModal, setShowAddModal] = useState(false)
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [viewingCategory, setViewingCategory] = useState<Category | null>(null)
  const [deletingCategory, setDeletingCategory] = useState<Category | null>(null)

  // Selection states
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [showBulkActions, setShowBulkActions] = useState(false)

  // Feedback states
  const [successMessage, setSuccessMessage] = useState<string>("")
  const [errorMessage, setErrorMessage] = useState<string>("")

  // Debounced search
  const debouncedSearchHandler = debounce((query: string) => {
    setDebouncedSearch(query)
    setCurrentPage(1)
  }, 300)

  useEffect(() => {
    debouncedSearchHandler(searchQuery)
  }, [searchQuery])

  // API calls
  // Memoize filter object to prevent unnecessary re-renders
  const categoryFilter = useMemo(() => ({
    search: debouncedSearch || undefined,
    isActive: statusFilter === "all" ? undefined : statusFilter === "active",
    parentId: parentFilter === "all" ? undefined : parentFilter === "main" ? null : parentFilter,
    sortBy: sortBy as any,
    sortOrder,
    page: currentPage,
    limit: pageSize,
  }), [debouncedSearch, statusFilter, parentFilter, sortBy, sortOrder, currentPage, pageSize])

  // GERÇEK API FETCH - DOĞRU YAPILMIŞ
  const [categoriesData, setCategoriesData] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchCategories = async () => {
    console.log('🚀 FETCH: Starting with filter:', categoryFilter)
    try {
      setLoading(true)
      setError(null)

      const result = await CategoryService.getCategories(categoryFilter)
      console.log('✅ FETCH: Success! Raw result:', result)
      console.log('✅ FETCH: Result type:', typeof result, 'Array?', Array.isArray(result))

      // API response'u düzgün parse et
      const categories = Array.isArray(result) ? result : (result?.data || [])
      console.log('✅ FETCH: Parsed categories:', categories.length, 'items')

      setCategoriesData(categories)
    } catch (err) {
      console.error('❌ FETCH: Error:', err)
      setError(err instanceof Error ? err.message : "Kategoriler yüklenirken hata oluştu")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    console.log('🔄 EFFECT: Filter changed, fetching...')
    fetchCategories()
  }, [JSON.stringify(categoryFilter)])

  const refetch = () => {
    console.log('🔄 REFETCH: Manual refetch')
    fetchCategories()
  }

  // Debug filter changes
  useEffect(() => {
    console.log('🔍 Filter changed:', {
      search: searchQuery,
      debouncedSearch,
      statusFilter,
      parentFilter,
      sortBy,
      sortOrder,
      categoryFilter
    })
    console.log('🔄 Filter will trigger useCategories refetch')
  }, [searchQuery, debouncedSearch, statusFilter, parentFilter, sortBy, sortOrder, categoryFilter])

  // Debug logging
  console.log('🔍 Categories Debug:', {
    categoriesData,
    loading,
    error,
    categoryFilter
  })

  const { mutate: deleteCategory, loading: deleting } = useDeleteCategory()

  // categoriesData zaten array, data property'si yok
  const categories = Array.isArray(categoriesData) ? categoriesData : []
  const pagination = null // Şimdilik pagination'ı devre dışı bırak

  // DEBUG: UI'da ne gösteriyoruz?
  console.log('🎨 UI DEBUG:', {
    categoriesDataType: typeof categoriesData,
    categoriesDataIsArray: Array.isArray(categoriesData),
    categoriesDataLength: categoriesData?.length,
    categoriesLength: categories.length,
    loading,
    error,
    firstCategory: categories[0]
  })

  // Event handlers
  const handleAddCategory = () => {
    setShowAddModal(true)
  }

  const handleEditCategory = (category: Category) => {
    setEditingCategory(category)
  }

  const handleViewCategory = (category: Category) => {
    setViewingCategory(category)
  }

  const handleDeleteCategory = (category: Category) => {
    setDeletingCategory(category)
  }

  const handleConfirmDelete = async () => {
    if (!deletingCategory) return

    try {
      await deleteCategory(deletingCategory.id)
      setSuccessMessage(`"${deletingCategory.name}" kategorisi başarıyla silindi.`)
      setDeletingCategory(null)
      refetch()
    } catch (error) {
      setErrorMessage("Kategori silinirken bir hata oluştu.")
    }
  }

  const handleModalClose = () => {
    setShowAddModal(false)
    setEditingCategory(null)
    setViewingCategory(null)
    setDeletingCategory(null)
    // Don't refetch immediately - let the success callback handle it
  }

  const handleFormSuccess = (message: string) => {
    setSuccessMessage(message)
    // Delay refetch to allow modal to close properly and prevent UI conflicts
    setTimeout(() => {
      refetch()
    }, 300)
  }

  const handleCategorySelect = (categoryId: string, selected: boolean) => {
    if (selected) {
      setSelectedCategories([...selectedCategories, categoryId])
    } else {
      setSelectedCategories(selectedCategories.filter((id) => id !== categoryId))
    }
  }

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedCategories(categories.map((cat) => cat.id))
    } else {
      setSelectedCategories([])
    }
  }

  const handleBulkAction = (action: string) => {
    setShowBulkActions(true)
  }

  const handleRefresh = () => {
    refetch()
    setSuccessMessage("Kategoriler yenilendi.")
  }

  const clearMessages = () => {
    setSuccessMessage("")
    setErrorMessage("")
  }

  // Loading state
  if (loading && !categories.length) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" message="Kategoriler yükleniyor..." />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Success/Error Messages */}
      {successMessage && <SuccessMessage message={successMessage} onClose={clearMessages} />}
      {errorMessage && <ErrorMessage message={errorMessage} onClose={clearMessages} />}

      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Kategori Yönetimi</h1>
          <p className="text-gray-600 mt-2">Ürün kategorilerini oluşturun, düzenleyin ve yönetin</p>
        </div>
        <div className="flex items-center gap-3">
          <Button variant="outline" onClick={handleRefresh} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`} />
            Yenile
          </Button>
          <Link href="/admin/kategoriler/siralama">
            <Button variant="outline" className="flex items-center gap-2 bg-transparent">
              <GripVertical className="h-4 w-4" />
              Sırala
            </Button>
          </Link>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Settings className="h-4 w-4 mr-2" />
                İşlemler
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Download className="h-4 w-4 mr-2" />
                Dışa Aktar
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Upload className="h-4 w-4 mr-2" />
                İçe Aktar
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button onClick={handleAddCategory} className="bg-orange-500 hover:bg-orange-600">
            <Plus className="h-4 w-4 mr-2" />
            Yeni Kategori
          </Button>
        </div>
      </div>

      {/* Category Stats */}
      <CategoryStats categories={categories} />

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Search and Quick Filters */}
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Kategori adı, açıklama veya slug ile ara..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="flex flex-wrap gap-2">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Durum" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tüm Durumlar</SelectItem>
                    <SelectItem value="active">Aktif</SelectItem>
                    <SelectItem value="inactive">Pasif</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={parentFilter} onValueChange={setParentFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Kategori Tipi" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tüm Kategoriler</SelectItem>
                    <SelectItem value="main">Ana Kategoriler</SelectItem>
                    <SelectItem value="sub">Alt Kategoriler</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Sırala" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">İsim</SelectItem>
                    <SelectItem value="productCount">Ürün Sayısı</SelectItem>
                    <SelectItem value="createdAt">Oluşturma Tarihi</SelectItem>
                    <SelectItem value="updatedAt">Güncelleme Tarihi</SelectItem>
                    <SelectItem value="sortOrder">Sıra</SelectItem>
                  </SelectContent>
                </Select>

                <Button variant="outline" onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}>
                  {sortOrder === "asc" ? "A-Z" : "Z-A"}
                </Button>
              </div>
            </div>

            {/* View Mode and Bulk Actions */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                {selectedCategories.length > 0 && (
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">{selectedCategories.length} kategori seçildi</Badge>
                    <Button size="sm" variant="outline" onClick={() => handleBulkAction("activate")}>
                      Toplu İşlem
                    </Button>
                  </div>
                )}
              </div>

              <div className="flex items-center gap-2">
                <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>
                  <SelectTrigger className="w-20">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="12">12</SelectItem>
                    <SelectItem value="24">24</SelectItem>
                    <SelectItem value="48">48</SelectItem>
                    <SelectItem value="96">96</SelectItem>
                  </SelectContent>
                </Select>

                <div className="flex border rounded-lg">
                  <Button
                    variant={viewMode === "grid" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("grid")}
                    className="rounded-r-none"
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("list")}
                    className="rounded-l-none"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Categories Display */}
      {error ? (
        <ErrorMessage message={`Kategoriler yüklenirken hata oluştu: ${error}`} onRetry={refetch} />
      ) : categories.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <div className="text-gray-400 mb-4">
              <Filter className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchQuery || statusFilter !== "all" || parentFilter !== "all"
                ? "Arama kriterlerinize uygun kategori bulunamadı"
                : "Henüz kategori eklenmemiş"}
            </h3>
            <p className="text-gray-600 mb-4">
              {searchQuery || statusFilter !== "all" || parentFilter !== "all"
                ? "Farklı filtreler deneyebilir veya yeni kategori ekleyebilirsiniz."
                : "İlk kategoriyi ekleyerek başlayın."}
            </p>
            <div className="flex items-center justify-center gap-3">
              {(searchQuery || statusFilter !== "all" || parentFilter !== "all") && (
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchQuery("")
                    setStatusFilter("all")
                    setParentFilter("all")
                  }}
                >
                  Filtreleri Temizle
                </Button>
              )}
              <Button onClick={handleAddCategory} className="bg-orange-500 hover:bg-orange-600">
                <Plus className="h-4 w-4 mr-2" />
                {categories.length === 0 ? "İlk Kategoriyi Ekle" : "Yeni Kategori Ekle"}
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as "grid" | "list")}>
          <TabsContent value="grid" className="mt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {categories.map((category) => (
                <CategoryGridItem
                  key={`category-${category.id}-${category.updatedAt}`}
                  category={category}
                  selected={selectedCategories.includes(category.id)}
                  onSelect={(selected) => handleCategorySelect(category.id, selected)}
                  onView={() => handleViewCategory(category)}
                  onEdit={() => handleEditCategory(category)}
                  onDelete={() => handleDeleteCategory(category)}
                />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="list" className="mt-0">
            <CategoryListView
              categories={categories}
              selectedCategories={selectedCategories}
              onSelectAll={handleSelectAll}
              onSelect={handleCategorySelect}
              onView={handleViewCategory}
              onEdit={handleEditCategory}
              onDelete={handleDeleteCategory}
            />
          </TabsContent>
        </Tabs>
      )}

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <CategoryPagination pagination={pagination} currentPage={currentPage} onPageChange={setCurrentPage} />
      )}

      {/* Modals */}
      <CategoryFormModal
        open={showAddModal}
        onClose={handleModalClose}
        category={null}
        onSuccess={handleFormSuccess}
        onError={(message) => setErrorMessage(message)}
      />

      <CategoryFormModal
        open={!!editingCategory}
        onClose={handleModalClose}
        category={editingCategory}
        onSuccess={handleFormSuccess}
        onError={(message) => setErrorMessage(message)}
      />

      <CategoryDetailModal
        open={!!viewingCategory}
        onClose={() => setViewingCategory(null)}
        category={viewingCategory}
        onEdit={() => {
          setEditingCategory(viewingCategory)
          setViewingCategory(null)
        }}
        onDelete={() => {
          setDeletingCategory(viewingCategory)
          setViewingCategory(null)
        }}
      />

      <DeleteConfirmModal
        open={!!deletingCategory}
        onClose={() => setDeletingCategory(null)}
        title="Kategoriyi Sil"
        description={`"${deletingCategory?.name}" kategorisini silmek istediğinizden emin misiniz? Bu işlem geri alınamaz ve kategori altındaki tüm ürünler etkilenebilir.`}
        onConfirm={handleConfirmDelete}
        loading={deleting}
        destructive
      />

      <CategoryBulkActions
        open={showBulkActions}
        onClose={() => setShowBulkActions(false)}
        selectedCategories={selectedCategories}
        onSuccess={(message) => {
          setSuccessMessage(message)
          setSelectedCategories([])
          refetch()
        }}
        onError={(message) => setErrorMessage(message)}
      />
    </div>
  )
}

// Grid Item Component - Memoized to prevent unnecessary re-renders
const CategoryGridItem = memo(function CategoryGridItem({
  category,
  selected,
  onSelect,
  onView,
  onEdit,
  onDelete,
}: {
  category: Category
  selected: boolean
  onSelect: (selected: boolean) => void
  onView: () => void
  onEdit: () => void
  onDelete: () => void
}) {
  // Use stable key to prevent dropdown state loss
  const [dropdownOpen, setDropdownOpen] = useState(false)
  return (
    <Card
      className={`hover:shadow-lg transition-all duration-200 cursor-pointer ${
        selected ? "ring-2 ring-orange-500 bg-orange-50" : ""
      }`}
    >
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            <input
              type="checkbox"
              checked={selected}
              onChange={(e) => {
                e.stopPropagation()
                onSelect(e.target.checked)
              }}
              className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              onClick={(e) => e.stopPropagation()}
            />
            <div className="bg-orange-50 p-3 rounded-lg">
              <div className="w-6 h-6 bg-orange-500 rounded"></div>
            </div>
          </div>
          <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
              <Button variant="ghost" size="icon">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation()
                  setDropdownOpen(false)
                  onView()
                }}
              >
                <Eye className="h-4 w-4 mr-2" />
                Görüntüle
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation()
                  setDropdownOpen(false)
                  onEdit()
                }}
              >
                <Edit className="h-4 w-4 mr-2" />
                Düzenle
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation()
                  setDropdownOpen(false)
                  // Navigate to products page filtered by this category
                  window.location.href = `/admin/urunler?kategori=${category.id}`
                }}
              >
                <Package className="h-4 w-4 mr-2" />
                Ürünler
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={(e) => {
                  e.stopPropagation()
                  setDropdownOpen(false)
                  onDelete()
                }}
                className="text-red-600"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Sil
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <div className="space-y-3" onClick={onView}>
          <div>
            <h3 className="font-semibold text-gray-900 line-clamp-1">{category.name}</h3>
            <p className="text-sm text-gray-600 mt-1 line-clamp-2">{category.description}</p>
          </div>

          <div className="flex items-center justify-between">
            <Badge variant={category.isActive ? "success" : "secondary"}>{category.isActive ? "Aktif" : "Pasif"}</Badge>
            <span className="text-sm text-gray-500">{category.productCount} ürün</span>
          </div>

          <div className="flex items-center justify-between text-xs text-gray-500">
            <span>Sıra: {category.sortOrder}</span>
            <span>{formatDate(category.createdAt)}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
})

// List View Component
function CategoryListView({
  categories,
  selectedCategories,
  onSelectAll,
  onSelect,
  onView,
  onEdit,
  onDelete,
}: {
  categories: Category[]
  selectedCategories: string[]
  onSelectAll: (selected: boolean) => void
  onSelect: (categoryId: string, selected: boolean) => void
  onView: (category: Category) => void
  onEdit: (category: Category) => void
  onDelete: (category: Category) => void
}) {
  const allSelected = categories.length > 0 && selectedCategories.length === categories.length
  const someSelected = selectedCategories.length > 0 && selectedCategories.length < categories.length

  return (
    <Card>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50 border-b">
              <tr>
                <th className="text-left p-4 w-12">
                  <input
                    type="checkbox"
                    checked={allSelected}
                    ref={(el) => {
                      if (el) el.indeterminate = someSelected
                    }}
                    onChange={(e) => onSelectAll(e.target.checked)}
                    className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                  />
                </th>
                <th className="text-left p-4 font-medium text-gray-900">Kategori</th>
                <th className="text-left p-4 font-medium text-gray-900">Açıklama</th>
                <th className="text-left p-4 font-medium text-gray-900">Durum</th>
                <th className="text-left p-4 font-medium text-gray-900">Ürün Sayısı</th>
                <th className="text-left p-4 font-medium text-gray-900">Sıra</th>
                <th className="text-left p-4 font-medium text-gray-900">Oluşturma Tarihi</th>
                <th className="text-right p-4 font-medium text-gray-900">İşlemler</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {categories.map((category) => (
                <tr
                  key={category.id}
                  className={`hover:bg-gray-50 cursor-pointer ${
                    selectedCategories.includes(category.id) ? "bg-orange-50" : ""
                  }`}
                  onClick={() => onView(category)}
                >
                  <td className="p-4" onClick={(e) => e.stopPropagation()}>
                    <input
                      type="checkbox"
                      checked={selectedCategories.includes(category.id)}
                      onChange={(e) => onSelect(category.id, e.target.checked)}
                      className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                    />
                  </td>
                  <td className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="bg-orange-50 p-2 rounded-lg">
                        <div className="w-4 h-4 bg-orange-500 rounded"></div>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{category.name}</div>
                        <div className="text-sm text-gray-500">/{category.slug}</div>
                      </div>
                    </div>
                  </td>
                  <td className="p-4">
                    <div className="max-w-xs truncate text-gray-600">{category.description}</div>
                  </td>
                  <td className="p-4">
                    <Badge variant={category.isActive ? "success" : "secondary"}>
                      {category.isActive ? "Aktif" : "Pasif"}
                    </Badge>
                  </td>
                  <td className="p-4 text-gray-600">{category.productCount}</td>
                  <td className="p-4 text-gray-600">{category.sortOrder}</td>
                  <td className="p-4 text-gray-600">{formatDate(category.createdAt)}</td>
                  <td className="p-4" onClick={(e) => e.stopPropagation()}>
                    <div className="flex items-center justify-end gap-2">
                      <Button variant="ghost" size="icon" onClick={() => onView(category)}>
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" onClick={() => onEdit(category)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => onDelete(category)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  )
}

// Pagination Component
function CategoryPagination({
  pagination,
  currentPage,
  onPageChange,
}: {
  pagination: any
  currentPage: number
  onPageChange: (page: number) => void
}) {
  const { totalPages, hasNext, hasPrev, total } = pagination

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">Toplam {total} kategori</div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={() => onPageChange(currentPage - 1)} disabled={!hasPrev}>
              Önceki
            </Button>
            <span className="text-sm text-gray-600">
              {currentPage} / {totalPages}
            </span>
            <Button variant="outline" size="sm" onClick={() => onPageChange(currentPage + 1)} disabled={!hasNext}>
              Sonraki
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
