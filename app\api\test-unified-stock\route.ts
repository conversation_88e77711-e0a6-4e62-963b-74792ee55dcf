import { NextRequest, NextResponse } from 'next/server'
import { UnifiedStockService } from '@/lib/services/UnifiedStockService'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  const testResults = []
  let allTestsPassed = true

  try {
    console.log('🧪 Starting Unified Stock System Tests...')

    // Test 1: UnifiedStockService - Single Product
    console.log('\n📋 Test 1: UnifiedStockService - Single Product')
    try {
      // Get a test product
      const testProduct = await prisma.product.findFirst({
        where: { trackStock: true }
      })

      if (!testProduct) {
        throw new Error('No products with trackStock=true found')
      }

      const stockInfo = await UnifiedStockService.getProductStock(testProduct.id)
      
      if (!stockInfo) {
        throw new Error('Stock info not returned')
      }

      console.log('✅ Single product stock info:', {
        productId: stockInfo.id,
        stockQuantity: stockInfo.stockQuantity,
        availableStock: stockInfo.availableStock,
        stockStatus: stockInfo.stockStatus,
        indicators: stockInfo.stockIndicators
      })

      testResults.push({
        test: 'UnifiedStockService - Single Product',
        status: 'PASSED',
        details: {
          productId: stockInfo.id,
          stockQuantity: stockInfo.stockQuantity,
          stockStatus: stockInfo.stockStatus
        }
      })
    } catch (error: any) {
      console.log('❌ Single product test failed:', error.message)
      testResults.push({
        test: 'UnifiedStockService - Single Product',
        status: 'FAILED',
        error: error.message
      })
      allTestsPassed = false
    }

    // Test 2: UnifiedStockService - Multiple Products
    console.log('\n📋 Test 2: UnifiedStockService - Multiple Products')
    try {
      const testProducts = await prisma.product.findMany({
        where: { trackStock: true },
        take: 3,
        select: { id: true, name: true }
      })

      if (testProducts.length === 0) {
        throw new Error('No products found for multiple test')
      }

      const productIds = testProducts.map(p => p.id)
      const stockInfoMap = await UnifiedStockService.getMultipleProductsStock(productIds)

      console.log('✅ Multiple products stock info:', Object.keys(stockInfoMap).length, 'products')

      testResults.push({
        test: 'UnifiedStockService - Multiple Products',
        status: 'PASSED',
        details: {
          requestedProducts: productIds.length,
          returnedProducts: Object.keys(stockInfoMap).length
        }
      })
    } catch (error: any) {
      console.log('❌ Multiple products test failed:', error.message)
      testResults.push({
        test: 'UnifiedStockService - Multiple Products',
        status: 'FAILED',
        error: error.message
      })
      allTestsPassed = false
    }

    // Test 3: Stock Status Calculation
    console.log('\n📋 Test 3: Stock Status Calculation')
    try {
      const testCases = [
        { stock: 0, threshold: 5, expected: 'OUT_OF_STOCK' },
        { stock: 3, threshold: 5, expected: 'LOW_STOCK' },
        { stock: 10, threshold: 5, expected: 'IN_STOCK' },
        { stock: 5, threshold: 5, expected: 'LOW_STOCK' }
      ]

      for (const testCase of testCases) {
        const result = UnifiedStockService.calculateStockStatus(
          testCase.stock, 
          testCase.threshold
        )
        
        if (result !== testCase.expected) {
          throw new Error(`Stock calculation failed: ${testCase.stock}/${testCase.threshold} expected ${testCase.expected}, got ${result}`)
        }
      }

      console.log('✅ Stock status calculations correct')

      testResults.push({
        test: 'Stock Status Calculation',
        status: 'PASSED',
        details: {
          testCases: testCases.length
        }
      })
    } catch (error: any) {
      console.log('❌ Stock calculation test failed:', error.message)
      testResults.push({
        test: 'Stock Status Calculation',
        status: 'FAILED',
        error: error.message
      })
      allTestsPassed = false
    }

    // Test 4: Stock API Endpoint
    console.log('\n📋 Test 4: Stock API Endpoint')
    try {
      const testProduct = await prisma.product.findFirst({
        where: { trackStock: true }
      })

      if (!testProduct) {
        throw new Error('No test product found')
      }

      const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/stock?productId=${testProduct.id}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(`API returned ${response.status}: ${result.error}`)
      }

      if (!result.success || !result.data) {
        throw new Error('API response format incorrect')
      }

      console.log('✅ Stock API endpoint working')

      testResults.push({
        test: 'Stock API Endpoint',
        status: 'PASSED',
        details: {
          productId: testProduct.id,
          apiResponse: 'Valid'
        }
      })
    } catch (error: any) {
      console.log('❌ Stock API test failed:', error.message)
      testResults.push({
        test: 'Stock API Endpoint',
        status: 'FAILED',
        error: error.message
      })
      allTestsPassed = false
    }

    // Test 5: Stock Validation
    console.log('\n📋 Test 5: Stock Validation')
    try {
      const validationTests = [
        { current: 10, requested: 5, operation: 'subtract' as const, shouldPass: true },
        { current: 10, requested: 15, operation: 'subtract' as const, shouldPass: false },
        { current: 10, requested: 5, operation: 'add' as const, shouldPass: true },
        { current: 10, requested: -5, operation: 'set' as const, shouldPass: false }
      ]

      for (const test of validationTests) {
        const result = UnifiedStockService.validateStockOperation(
          test.current,
          test.requested,
          test.operation
        )

        if (result.isValid !== test.shouldPass) {
          throw new Error(`Validation test failed: ${JSON.stringify(test)}`)
        }
      }

      console.log('✅ Stock validation working correctly')

      testResults.push({
        test: 'Stock Validation',
        status: 'PASSED',
        details: {
          testCases: validationTests.length
        }
      })
    } catch (error: any) {
      console.log('❌ Stock validation test failed:', error.message)
      testResults.push({
        test: 'Stock Validation',
        status: 'FAILED',
        error: error.message
      })
      allTestsPassed = false
    }

    console.log('\n🎯 Unified Stock System Test Summary:')
    console.log(`Total Tests: ${testResults.length}`)
    console.log(`Passed: ${testResults.filter(t => t.status === 'PASSED').length}`)
    console.log(`Failed: ${testResults.filter(t => t.status === 'FAILED').length}`)

    return NextResponse.json({
      success: allTestsPassed,
      message: allTestsPassed ? 'All unified stock tests passed!' : 'Some tests failed',
      summary: {
        total: testResults.length,
        passed: testResults.filter(t => t.status === 'PASSED').length,
        failed: testResults.filter(t => t.status === 'FAILED').length
      },
      results: testResults,
      systemStatus: {
        unifiedStockService: 'Active',
        stockAPI: 'Active',
        stockValidation: 'Active',
        realTimeSync: 'Ready'
      }
    })

  } catch (error: any) {
    console.error('❌ Unified stock test suite failed:', error)
    return NextResponse.json({
      success: false,
      error: 'Unified stock test suite execution failed',
      details: error.message,
      results: testResults
    }, { status: 500 })
  }
}
