'use client'

import { <PERSON>ert<PERSON><PERSON>gle, Shield, ShieldOff } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'

export function DevModeIndicator() {
  // Only show in development mode when auth is bypassed
  const isDev = process.env.NODE_ENV === 'development'
  const skipAuth = process.env.NEXT_PUBLIC_SKIP_AUTH === 'true'

  if (!isDev || !skipAuth) {
    return null
  }

  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-orange-500 text-white px-4 py-2 text-center text-sm font-medium">
      <div className="flex items-center justify-center gap-2">
        <ShieldOff className="h-4 w-4" />
        <span>
          🔓 DEVELOPMENT MODE: Authentication Bypassed - DO NOT USE IN PRODUCTION
        </span>
        <ShieldOff className="h-4 w-4" />
      </div>
    </div>
  )
}

export function DevModeAlert() {
  const isDev = process.env.NODE_ENV === 'development'
  const skipAuth = process.env.NEXT_PUBLIC_SKIP_AUTH === 'true'

  if (!isDev || !skipAuth) {
    return null
  }

  return (
    <Alert className="mb-6 border-orange-200 bg-orange-50">
      <AlertTriangle className="h-4 w-4 text-orange-600" />
      <AlertDescription className="text-orange-800">
        <strong>Development Mode Active:</strong> Authentication is currently bypassed for easier testing. 
        This feature is automatically disabled in production environments.
        <br />
        <span className="text-sm mt-1 block">
          To re-enable authentication, set <code>SKIP_AUTH="false"</code> in your .env file.
        </span>
      </AlertDescription>
    </Alert>
  )
}
