import { type NextRequest, NextResponse } from "next/server"
import { mockCategories } from "@/data/mock-data"

interface ReorderRequest {
  categories: { id: string; sortOrder: number }[]
}

// PUT /api/categories/reorder - <PERSON><PERSON><PERSON> sırasını güncelle
export async function PUT(request: NextRequest) {
  try {
    const { categories: orderUpdates }: ReorderRequest = await request.json()

    if (!orderUpdates || !Array.isArray(orderUpdates)) {
      return NextResponse.json({ success: false, message: "Geçersiz sıralama verisi" }, { status: 400 })
    }

    // Update sort order for each category
    for (const update of orderUpdates) {
      const categoryIndex = mockCategories.findIndex((cat) => cat.id === update.id)
      if (categoryIndex !== -1) {
        mockCategories[categoryIndex] = {
          ...mockCategories[categoryIndex],
          sortOrder: update.sortOrder,
          updatedAt: new Date(),
        }
      }
    }

    // Sort categories by new order
    mockCategories.sort((a, b) => a.sortOrder - b.sortOrder)

    return NextResponse.json({
      success: true,
      message: "<PERSON>gori sıralaması başarıyla güncellendi",
      data: null,
    })
  } catch (error) {
    console.error("Category reorder error:", error)
    return NextResponse.json({ success: false, message: "Sıralama güncellenirken hata oluştu" }, { status: 500 })
  }
}
