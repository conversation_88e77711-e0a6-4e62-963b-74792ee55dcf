"use client"

import use<PERSON><PERSON> from "swr"
import useSWRMutation from "swr/mutation"
import ProductService from "@/lib/services/ProductService"
import type { ProductFilterParams, ProductCreateInput, ProductUpdateInput } from "@/types"

// SWR key for products
const PRODUCTS_KEY = "/api/products"

// Hook to fetch products with filters
export function useProducts(params?: ProductFilterParams) {
  const queryParams = new URLSearchParams()
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, String(value))
      }
    })
  }
  const url = `${PRODUCTS_KEY}?${queryParams.toString()}`

  const { data, error, isLoading, mutate } = useSWR(url, () => ProductService.getAllProducts(queryParams), {
    revalidateOnFocus: false,
    revalidateOnReconnect: false,
  })

  return {
    data: data,
    loading: isLoading,
    error: error,
    refetch: mutate,
  }
}

// Hook to fetch a single product by ID
export function useProduct(id: string | null) {
  const { data, error, isLoading, mutate } = useSWR(
    id ? `${PRODUCTS_KEY}/${id}` : null,
    () => ProductService.getProductById(id!),
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
    },
  )

  return {
    data: data,
    loading: isLoading,
    error: error,
    refetch: mutate,
  }
}

// Hook to create a product
export function useCreateProduct() {
  const { trigger, isMutating, error } = useSWRMutation(
    PRODUCTS_KEY,
    async (url: string, { arg }: { arg: ProductCreateInput }) => {
      return ProductService.createProduct(arg)
    },
    {
      populateCache: true, // Optimistically update cache
      revalidate: true, // Revalidate after mutation
    },
  )

  return {
    mutate: trigger,
    loading: isMutating,
    error: error,
  }
}

// Hook to update a product
export function useUpdateProduct() {
  const { trigger, isMutating, error } = useSWRMutation(
    PRODUCTS_KEY, // This key is used for revalidation, not the actual endpoint
    async (url: string, { arg }: { arg: { id: string; data: ProductUpdateInput } }) => {
      return ProductService.updateProduct(arg.id, arg.data)
    },
    {
      populateCache: true,
      revalidate: true,
    },
  )

  return {
    mutate: trigger,
    loading: isMutating,
    error: error,
  }
}

// Hook to delete a product
export function useDeleteProduct() {
  const { trigger, isMutating, error } = useSWRMutation(
    PRODUCTS_KEY,
    async (url: string, { arg }: { arg: string }) => {
      return ProductService.deleteProduct(arg)
    },
    {
      populateCache: true,
      revalidate: true,
    },
  )

  return {
    mutate: trigger,
    loading: isMutating,
    error: error,
  }
}

// Hook for bulk product updates
export function useUpdateProductBulk() {
  const { trigger, isMutating, error } = useSWRMutation(
    PRODUCTS_KEY, // Revalidate the main products list
    async (url: string, { arg }: { arg: { productIds: string[]; data: Partial<ProductUpdateInput> } }) => {
      return ProductService.updateProductBulk(arg.productIds, arg.data)
    },
    {
      populateCache: true,
      revalidate: true,
    },
  )

  return {
    mutate: trigger,
    loading: isMutating,
    error: error,
  }
}

// Hook for bulk product deletion
export function useDeleteProductBulk() {
  const { trigger, isMutating, error } = useSWRMutation(
    PRODUCTS_KEY, // Revalidate the main products list
    async (url: string, { arg }: { arg: string[] }) => {
      return ProductService.deleteProductBulk(arg)
    },
    {
      populateCache: true,
      revalidate: true,
    },
  )

  return {
    mutate: trigger,
    loading: isMutating,
    error: error,
  }
}
