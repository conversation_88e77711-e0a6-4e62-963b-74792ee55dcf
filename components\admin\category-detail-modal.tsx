"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"
import { formatDate, formatNumber } from "@/lib/utils"
import { Edit, Trash2, Eye, EyeOff, Calendar, Hash, Package, Globe } from "lucide-react"
import type { Category } from "@/types"

interface CategoryDetailModalProps {
  open: boolean
  onClose: () => void
  category: Category | null
  onEdit: () => void
  onDelete: () => void
}

export function CategoryDetailModal({ open, onClose, category, onEdit, onDelete }: CategoryDetailModalProps) {
  if (!category) return null

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="bg-orange-50 p-2 rounded-lg">
              <div className="w-6 h-6 bg-orange-500 rounded"></div>
            </div>
            {category.name}
            <Badge variant={category.isActive ? "success" : "secondary"}>
              {category.isActive ? (
                <>
                  <Eye className="h-3 w-3 mr-1" />
                  Aktif
                </>
              ) : (
                <>
                  <EyeOff className="h-3 w-3 mr-1" />
                  Pasif
                </>
              )}
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Basic Information */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Temel Bilgiler</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Kategori Adı</label>
                <p className="text-gray-900 font-medium">{category.name}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Slug</label>
                <p className="text-gray-900 font-mono text-sm">/{category.slug}</p>
              </div>
              <div className="md:col-span-2">
                <label className="text-sm font-medium text-gray-500">Açıklama</label>
                <p className="text-gray-900">{category.description}</p>
              </div>
            </div>
          </div>

          <Separator />

          {/* Statistics */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">İstatistikler</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <Package className="h-8 w-8 text-orange-500 mx-auto mb-2" />
                <div className="text-2xl font-bold text-gray-900">{formatNumber(category.productCount)}</div>
                <div className="text-sm text-gray-600">Ürün Sayısı</div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <Hash className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                <div className="text-2xl font-bold text-gray-900">{category.sortOrder}</div>
                <div className="text-sm text-gray-600">Sıra Numarası</div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <Calendar className="h-8 w-8 text-green-500 mx-auto mb-2" />
                <div className="text-sm font-bold text-gray-900">{formatDate(category.createdAt)}</div>
                <div className="text-sm text-gray-600">Oluşturma Tarihi</div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <Calendar className="h-8 w-8 text-purple-500 mx-auto mb-2" />
                <div className="text-sm font-bold text-gray-900">{formatDate(category.updatedAt)}</div>
                <div className="text-sm text-gray-600">Son Güncelleme</div>
              </div>
            </div>
          </div>

          {/* SEO Information */}
          {(category.seoTitle || category.seoDescription) && (
            <>
              <Separator />
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  SEO Bilgileri
                </h3>
                <div className="space-y-4">
                  {category.seoTitle && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">SEO Başlık</label>
                      <p className="text-gray-900">{category.seoTitle}</p>
                      <p className="text-xs text-gray-500 mt-1">{category.seoTitle.length}/60 karakter</p>
                    </div>
                  )}
                  {category.seoDescription && (
                    <div>
                      <label className="text-sm font-medium text-gray-500">SEO Açıklama</label>
                      <p className="text-gray-900">{category.seoDescription}</p>
                      <p className="text-xs text-gray-500 mt-1">{category.seoDescription.length}/160 karakter</p>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {/* Hierarchy Information */}
          {category.parentId && (
            <>
              <Separator />
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Hiyerarşi</h3>
                <div>
                  <label className="text-sm font-medium text-gray-500">Üst Kategori</label>
                  <p className="text-gray-900">Ana Kategori</p>
                </div>
              </div>
            </>
          )}

          {/* Sub Categories */}
          {category.children && category.children.length > 0 && (
            <>
              <Separator />
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Alt Kategoriler</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {category.children.map((child) => (
                    <div key={child.id} className="flex items-center gap-2 p-2 bg-gray-50 rounded">
                      <div className="w-3 h-3 bg-orange-400 rounded"></div>
                      <span className="text-sm text-gray-900">{child.name}</span>
                      <Badge variant="secondary" className="ml-auto text-xs">
                        {child.productCount}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3 border-t pt-6">
          <Button variant="outline" onClick={onClose}>
            Kapat
          </Button>
          <Button variant="outline" onClick={onEdit}>
            <Edit className="h-4 w-4 mr-2" />
            Düzenle
          </Button>
          <Button variant="destructive" onClick={onDelete}>
            <Trash2 className="h-4 w-4 mr-2" />
            Sil
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
