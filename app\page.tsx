import { Header } from "@/components/header"
import { HeroSection } from "@/components/hero-section"
import { CategoryGrid } from "@/components/category-grid"
import { PopularProducts } from "@/components/popular-products"
import { TrustBadges } from "@/components/trust-badges"
import { Footer } from "@/components/footer"
import { Suspense } from "react"
import { CategoryGridSkeleton } from "@/components/skeletons/category-grid-skeleton"
import { PopularProductsSkeleton } from "@/components/skeletons/popular-products-skeleton"

export const metadata = {
  title: "İş Güvenliği Malzemeleri - Türkiye'nin Lideri | İş Güvenliği Platformu",
  description:
    "25 yıllık deneyimimizle iş güvenliği malzemelerinin en kaliteli ve uygun fiyatlı çözümlerini sunuyoruz. Binlerce ürün, hızlı teslimat.",
  keywords: ["iş güvenliği", "güvenlik malzemeleri", "baret", "eldiven", "aya<PERSON><PERSON><PERSON>", "maske", "güvenlik gözlüğü"],
  openGraph: {
    title: "İş Güvenliği Malzemeleri - Türkiye'nin Lideri",
    description:
      "25 yıllık deneyimimizle iş güvenliği malzemelerinin en kaliteli ve uygun fiyatlı çözümlerini sunuyoruz.",
    type: "website",
    locale: "tr_TR",
  },
}

export default function HomePage() {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      <main>
        <HeroSection />
        <Suspense fallback={<CategoryGridSkeleton />}>
          <CategoryGrid />
        </Suspense>
        <Suspense fallback={<PopularProductsSkeleton />}>
          <PopularProducts />
        </Suspense>
        <TrustBadges />
      </main>
      <Footer />
    </div>
  )
}
