"use client"

import { useState, useEffect } from "react"
import { BannerCarousel, type CarouselItem } from "@/components/ui/banner-carousel"
import { getActiveCategoryBanners, trackBannerClick } from "@/lib/api/category-banners"
import { getCategoryImageUrl } from "@/lib/category-image-utils"
import type { Category, CategoryBanner, DeviceType } from "@/types"
import { cn } from "@/lib/utils"
import { ExternalLink } from "lucide-react"

interface CategoryBannerCarouselProps {
  category: Category
  className?: string
  height?: "sm" | "md" | "lg" | "xl"
  showPlayPause?: boolean
  autoplay?: boolean
}

const heightClasses = {
  sm: "h-32 md:h-40",
  md: "h-48 md:h-64", 
  lg: "h-64 md:h-80",
  xl: "h-80 md:h-96"
}

export function CategoryBannerCarousel({ 
  category, 
  className,
  height = "lg",
  showPlayPause = false,
  autoplay = true
}: CategoryBannerCarouselProps) {
  const [banners, setBanners] = useState<CategoryBanner[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [deviceType, setDeviceType] = useState<DeviceType>('DESKTOP')

  // Detect device type
  useEffect(() => {
    const detectDeviceType = (): DeviceType => {
      if (typeof window === 'undefined') return 'DESKTOP'
      
      const width = window.innerWidth
      if (width < 768) return 'MOBILE'
      if (width < 1024) return 'TABLET'
      return 'DESKTOP'
    }

    const handleResize = () => {
      setDeviceType(detectDeviceType())
    }

    setDeviceType(detectDeviceType())
    window.addEventListener('resize', handleResize)
    
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Load banners
  useEffect(() => {
    const loadBanners = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const activeBanners = await getActiveCategoryBanners(category.id, {
          deviceType,
          prioritySort: true
        })
        
        setBanners(activeBanners)
      } catch (err) {
        console.error('Error loading category banners:', err)
        setError('Banner\'lar yüklenirken hata oluştu')
      } finally {
        setLoading(false)
      }
    }

    loadBanners()
  }, [category.id, deviceType])

  // Handle banner click
  const handleBannerClick = async (item: CarouselItem, index: number) => {
    const banner = banners[index]
    if (!banner) return

    try {
      // Track click
      await trackBannerClick(category.id, banner.id, {
        deviceType,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent
      })

      // Navigate to CTA URL if exists
      if (banner.ctaUrl) {
        if (banner.ctaUrl.startsWith('http')) {
          window.open(banner.ctaUrl, '_blank', 'noopener,noreferrer')
        } else {
          window.location.href = banner.ctaUrl
        }
      }
    } catch (err) {
      console.error('Error tracking banner click:', err)
    }
  }

  // Create carousel items from banners
  const carouselItems: CarouselItem[] = banners.map((banner, index) => ({
    id: banner.id,
    duration: banner.displayDuration,
    content: (
      <BannerItem 
        banner={banner} 
        deviceType={deviceType}
        height={height}
      />
    )
  }))

  // Loading state
  if (loading) {
    return (
      <div className={cn(heightClasses[height], "bg-gray-200 animate-pulse rounded-lg mb-8", className)}>
        <div className="w-full h-full flex items-center justify-center">
          <div className="text-gray-500">Banner'lar yükleniyor...</div>
        </div>
      </div>
    )
  }

  // Error state - fallback to original banner if available
  if (error) {
    // Fallback to original bannerImage if exists
    if (category.bannerImage) {
      return (
        <div className={cn("relative mb-8 rounded-lg overflow-hidden shadow-lg", heightClasses[height], className)}>
          <img
            src={getCategoryImageUrl(category.bannerImage, deviceType === 'MOBILE' ? 'medium' : 'large')}
            alt={`${category.name} banner`}
            className="w-full h-full object-cover"
            loading="eager"
          />

          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />

          {/* Content */}
          <div className="absolute inset-0 flex items-end">
            <div className="p-6 md:p-8 text-white w-full">
              <div className="max-w-4xl">
                <h1 className="text-2xl md:text-4xl lg:text-5xl font-bold mb-3 leading-tight">
                  {category.name}
                </h1>
                <p className="text-base md:text-lg lg:text-xl opacity-90 mb-4 leading-relaxed">
                  {category.description}
                </p>

                {/* Stats */}
                <div className="flex items-center gap-4 md:gap-6 text-sm md:text-base opacity-80">
                  {category.productCount > 0 && (
                    <span className="flex items-center gap-2">
                      <svg className="w-4 h-4 md:w-5 md:h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5zM10 18V8l5-3.5V15a1 1 0 01-1 1h-4z" clipRule="evenodd" />
                      </svg>
                      {category.productCount} ürün
                    </span>
                  )}

                  {category.children && category.children.length > 0 && (
                    <span className="flex items-center gap-2">
                      <svg className="w-4 h-4 md:w-5 md:h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                      </svg>
                      {category.children.length} alt kategori
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    }

    // No fallback available - show error
    return (
      <div className={cn(heightClasses[height], "bg-red-50 border border-red-200 rounded-lg mb-8 flex items-center justify-center", className)}>
        <div className="text-red-600 text-center">
          <p className="font-medium">Banner Hatası</p>
          <p className="text-sm">{error}</p>
        </div>
      </div>
    )
  }

  // No banners - fallback to original banner or hide
  if (carouselItems.length === 0) {
    // Fallback to original bannerImage if exists
    if (category.bannerImage) {
      return (
        <div className={cn("relative mb-8 rounded-lg overflow-hidden shadow-lg", heightClasses[height], className)}>
          <img 
            src={getCategoryImageUrl(category.bannerImage, deviceType === 'MOBILE' ? 'medium' : 'large')}
            alt={`${category.name} banner`}
            className="w-full h-full object-cover"
            loading="eager"
          />
          
          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
          
          {/* Content */}
          <div className="absolute inset-0 flex items-end">
            <div className="p-6 md:p-8 text-white w-full">
              <div className="max-w-4xl">
                <h1 className="text-2xl md:text-4xl lg:text-5xl font-bold mb-3 leading-tight">
                  {category.name}
                </h1>
                <p className="text-base md:text-lg lg:text-xl opacity-90 mb-4 leading-relaxed">
                  {category.description}
                </p>
                
                {/* Stats */}
                <div className="flex items-center gap-4 md:gap-6 text-sm md:text-base opacity-80">
                  {category.productCount > 0 && (
                    <span className="flex items-center gap-2">
                      <svg className="w-4 h-4 md:w-5 md:h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 2L3 7v11a2 2 0 002 2h10a2 2 0 002-2V7l-7-5zM10 18V8l5-3.5V15a1 1 0 01-1 1h-4z" clipRule="evenodd" />
                      </svg>
                      {category.productCount} ürün
                    </span>
                  )}
                  
                  {category.children && category.children.length > 0 && (
                    <span className="flex items-center gap-2">
                      <svg className="w-4 h-4 md:w-5 md:h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                      </svg>
                      {category.children.length} alt kategori
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    }
    
    // No banners at all - return null to hide completely
    return null
  }

  // Single banner - no carousel needed
  if (carouselItems.length === 1) {
    return (
      <div 
        className={cn("mb-8 cursor-pointer", className)}
        onClick={() => handleBannerClick(carouselItems[0], 0)}
      >
        {carouselItems[0].content}
      </div>
    )
  }

  // Multiple banners - show carousel
  return (
    <div className={cn("mb-8", className)}>
      <BannerCarousel
        items={carouselItems}
        autoplay={autoplay}
        showPlayPause={showPlayPause}
        onItemClick={handleBannerClick}
        className={heightClasses[height]}
      />
    </div>
  )
}

// Individual banner item component
interface BannerItemProps {
  banner: CategoryBanner
  deviceType: DeviceType
  height: "sm" | "md" | "lg" | "xl"
}

function BannerItem({ banner, deviceType, height }: BannerItemProps) {
  const imageUrl = deviceType === 'MOBILE' && banner.mobileImageUrl 
    ? banner.mobileImageUrl 
    : banner.imageUrl

  const customStyles = {
    backgroundColor: banner.backgroundColor,
    color: banner.textColor
  }

  return (
    <div 
      className="relative w-full h-full overflow-hidden rounded-lg"
      style={banner.backgroundColor ? { backgroundColor: banner.backgroundColor } : undefined}
    >
      {/* Background Image */}
      <img 
        src={imageUrl}
        alt={banner.imageAlt || banner.title || 'Banner'}
        className="w-full h-full object-cover"
        loading="lazy"
      />
      
      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
      
      {/* Content */}
      <div className="absolute inset-0 flex items-end">
        <div className="p-4 md:p-6 lg:p-8 text-white w-full" style={banner.textColor ? { color: banner.textColor } : undefined}>
          <div className="max-w-4xl">
            {banner.title && (
              <h2 className={cn(
                "font-bold mb-2 md:mb-3 leading-tight",
                height === 'sm' ? "text-lg md:text-xl" :
                height === 'md' ? "text-xl md:text-2xl lg:text-3xl" :
                height === 'lg' ? "text-2xl md:text-3xl lg:text-4xl" :
                "text-3xl md:text-4xl lg:text-5xl"
              )}>
                {banner.title}
              </h2>
            )}
            
            {banner.subtitle && (
              <h3 className={cn(
                "font-medium mb-2 opacity-90",
                height === 'sm' ? "text-sm md:text-base" :
                "text-base md:text-lg lg:text-xl"
              )}>
                {banner.subtitle}
              </h3>
            )}
            
            {banner.description && (
              <p className={cn(
                "opacity-80 mb-4 leading-relaxed",
                height === 'sm' ? "text-xs md:text-sm" :
                "text-sm md:text-base lg:text-lg"
              )}>
                {banner.description}
              </p>
            )}
            
            {/* CTA Button */}
            {banner.ctaText && banner.ctaUrl && (
              <button className="inline-flex items-center gap-2 bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white px-4 py-2 md:px-6 md:py-3 rounded-lg font-medium transition-colors duration-200">
                {banner.ctaText}
                <ExternalLink className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
