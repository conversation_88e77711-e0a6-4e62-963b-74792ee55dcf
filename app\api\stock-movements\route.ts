import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { UnifiedStockService } from '@/lib/services/UnifiedStockService'
import { z } from 'zod'

// Validation schemas
const StockMovementCreateSchema = z.object({
  productId: z.string().min(1, 'Product ID is required'),
  variantId: z.string().optional(),
  movementType: z.enum(['in', 'out', 'adjustment'], {
    errorMap: () => ({ message: 'Movement type must be in, out, or adjustment' })
  }),
  quantity: z.number().int().min(1, 'Quantity must be a positive integer'),
  reason: z.enum(['purchase', 'sale', 'damage', 'adjustment', 'return', 'transfer']).optional(),
  reference: z.string().optional(),
  unitCost: z.number().positive().optional(),
  supplier: z.string().optional(),
  notes: z.string().optional(),
  createdBy: z.string().optional()
})

const StockMovementQuerySchema = z.object({
  productId: z.string().optional(),
  movementType: z.enum(['in', 'out', 'adjustment']).optional(),
  reason: z.enum(['purchase', 'sale', 'damage', 'adjustment', 'return', 'transfer']).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  limit: z.string().transform(val => parseInt(val) || 50).optional(),
  offset: z.string().transform(val => parseInt(val) || 0).optional()
})

// GET /api/stock-movements - List stock movements with filtering
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = StockMovementQuerySchema.parse(Object.fromEntries(searchParams))

    // Build where clause
    const where: any = {}
    
    if (query.productId) {
      where.productId = query.productId
    }
    
    if (query.movementType) {
      where.movementType = query.movementType
    }
    
    if (query.reason) {
      where.reason = query.reason
    }
    
    if (query.startDate || query.endDate) {
      where.createdAt = {}
      if (query.startDate) {
        where.createdAt.gte = new Date(query.startDate)
      }
      if (query.endDate) {
        where.createdAt.lte = new Date(query.endDate)
      }
    }

    // Get stock movements with product info
    const stockMovements = await prisma.stockMovement.findMany({
      where,
      include: {
        product: {
          select: {
            id: true,
            name: true,
            slug: true,
            stockQuantity: true,
            minStockThreshold: true,
            maxStockCapacity: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: query.limit || 50,
      skip: query.offset || 0
    })

    // Get total count for pagination
    const totalCount = await prisma.stockMovement.count({ where })

    return NextResponse.json({
      success: true,
      data: stockMovements,
      pagination: {
        total: totalCount,
        limit: query.limit || 50,
        offset: query.offset || 0,
        hasMore: (query.offset || 0) + (query.limit || 50) < totalCount
      }
    })

  } catch (error: any) {
    console.error('❌ Stock movements GET error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Validation error',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      error: 'Failed to fetch stock movements',
      details: error.message
    }, { status: 500 })
  }
}

// POST /api/stock-movements - Create new stock movement
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = StockMovementCreateSchema.parse(body)

    // Start transaction for atomic stock update
    const result = await prisma.$transaction(async (tx) => {
      // Get current product stock
      const product = await tx.product.findUnique({
        where: { id: validatedData.productId },
        select: {
          id: true,
          name: true,
          stockQuantity: true,
          minStockThreshold: true,
          maxStockCapacity: true,
          trackStock: true
        }
      })

      if (!product) {
        throw new Error('Product not found')
      }

      if (!product.trackStock) {
        throw new Error('Stock tracking is not enabled for this product')
      }

      const currentStock = product.stockQuantity || 0
      let newStock = currentStock

      // Calculate new stock based on movement type
      if (validatedData.movementType === 'in') {
        newStock = currentStock + validatedData.quantity
      } else if (validatedData.movementType === 'out') {
        newStock = currentStock - validatedData.quantity
        if (newStock < 0) {
          throw new Error(`Insufficient stock. Current: ${currentStock}, Requested: ${validatedData.quantity}`)
        }
      } else if (validatedData.movementType === 'adjustment') {
        // For adjustment, quantity can be positive or negative
        newStock = validatedData.quantity
      }

      // Check max capacity for 'in' movements (only if capacity is set and > 0)
      if (validatedData.movementType === 'in' && product.maxStockCapacity && product.maxStockCapacity > 0) {
        if (newStock > product.maxStockCapacity) {
          return NextResponse.json({
            success: false,
            error: 'Kapasite Aşımı',
            details: `Bu işlem maksimum kapasiteyi aşacak. Mevcut: ${currentStock}, Eklenecek: ${validatedData.quantity}, Maksimum: ${product.maxStockCapacity}`,
            validation: {
              currentStock,
              requestedQuantity: validatedData.quantity,
              newStock,
              maxCapacity: product.maxStockCapacity,
              availableCapacity: product.maxStockCapacity - currentStock
            }
          }, { status: 400 }) // 400 Bad Request instead of 500
        }
      }

      // Create stock movement record
      const stockMovement = await tx.stockMovement.create({
        data: {
          productId: validatedData.productId,
          variantId: validatedData.variantId,
          movementType: validatedData.movementType,
          quantity: validatedData.movementType === 'out' ? -validatedData.quantity : validatedData.quantity,
          previousStock: currentStock,
          newStock: newStock,
          reason: validatedData.reason,
          reference: validatedData.reference,
          unitCost: validatedData.unitCost,
          supplier: validatedData.supplier,
          notes: validatedData.notes,
          createdBy: validatedData.createdBy
        },
        include: {
          product: {
            select: {
              id: true,
              name: true,
              slug: true
            }
          }
        }
      })

      // Update product stock and recalculate status
      const newStatus = UnifiedStockService.calculateStockStatus(
        newStock,
        product.minStockThreshold || 0,
        0 // TODO: Get actual reserved stock
      )

      await tx.product.update({
        where: { id: validatedData.productId },
        data: {
          stockQuantity: newStock,
          stockStatus: newStatus
        }
      })

      return stockMovement
    })

    return NextResponse.json({
      success: true,
      message: 'Stock movement created successfully',
      data: result
    })

  } catch (error: any) {
    console.error('❌ Stock movement POST error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Validation error',
        details: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      error: 'Failed to create stock movement',
      details: error.message
    }, { status: 500 })
  }
}
