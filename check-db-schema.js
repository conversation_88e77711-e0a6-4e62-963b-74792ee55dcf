const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkDatabaseSchema() {
  console.log('🔍 Checking Database Schema...\n')

  try {
    // Check if stock fields exist by trying to select them
    console.log('📊 Step 1: Testing Stock Field Selection')
    console.log('=' .repeat(50))
    
    try {
      const testProduct = await prisma.product.findFirst({
        select: {
          id: true,
          name: true,
          stockQuantity: true,
          minStockThreshold: true,
          maxStockCapacity: true,
          stockStatus: true,
          trackStock: true,
          basePrice: true
        }
      })

      if (testProduct) {
        console.log('✅ Stock fields exist in database!')
        console.log('Sample product data:')
        console.log(`   ID: ${testProduct.id}`)
        console.log(`   Name: ${testProduct.name}`)
        console.log(`   Stock Quantity: ${testProduct.stockQuantity}`)
        console.log(`   Min Threshold: ${testProduct.minStockThreshold}`)
        console.log(`   Max Capacity: ${testProduct.maxStockCapacity}`)
        console.log(`   Stock Status: ${testProduct.stockStatus}`)
        console.log(`   Track Stock: ${testProduct.trackStock}`)
        console.log(`   Base Price: ${testProduct.basePrice}`)
        
        return testProduct.id
      } else {
        console.log('❌ No products found in database')
        return null
      }

    } catch (error) {
      console.log('❌ Stock fields do NOT exist in database!')
      console.log(`Error: ${error.message}`)
      
      // Try to get basic product info without stock fields
      console.log('\n📋 Step 2: Checking Basic Product Fields')
      console.log('=' .repeat(50))
      
      try {
        const basicProduct = await prisma.product.findFirst({
          select: {
            id: true,
            name: true,
            basePrice: true,
            trackStock: true
          }
        })

        if (basicProduct) {
          console.log('✅ Basic product fields exist:')
          console.log(`   ID: ${basicProduct.id}`)
          console.log(`   Name: ${basicProduct.name}`)
          console.log(`   Base Price: ${basicProduct.basePrice}`)
          console.log(`   Track Stock: ${basicProduct.trackStock}`)
          
          console.log('\n⚠️  DIAGNOSIS: Stock management fields are missing from database!')
          console.log('   The migration add_stock_management_fields.sql needs to be applied.')
          
          return basicProduct.id
        }
      } catch (basicError) {
        console.log(`❌ Even basic fields failed: ${basicError.message}`)
        return null
      }
    }

  } catch (error) {
    console.error('❌ Schema check failed:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the check
checkDatabaseSchema()
  .then((productId) => {
    if (productId) {
      console.log(`\n🎯 Next Steps:`)
      console.log(`1. If stock fields are missing, run the migration manually`)
      console.log(`2. Test the individual product API: http://localhost:3000/api/products/${productId}`)
      console.log(`3. Check the admin panel edit form`)
    }
  })
  .catch((error) => {
    console.error('Script failed:', error)
    process.exit(1)
  })
