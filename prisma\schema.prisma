// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for authentication
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  name      String?
  role      UserRole @default(ADMIN)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}

enum UserRole {
  ADMIN
  SUPER_ADMIN
}

// Kategori ile ilgili enum'lar
// StockStatus enum'ı kaldırıldı - stok yönetimi Product seviyesinde yapılacak

enum ApprovalStatus {
  TASLAK
  BEKLEMEDE
  ONAYLANDI
  REDDEDILDI
}

enum SyncStatus {
  SENKRONIZE
  BEKLEMEDE
  BASARISIZ
  DEVRE_DISI
}

enum IconType {
  SVG
  PNG
  FONT_ICON
  EMOJI
}

enum MobileTemplate {
  VARSAYILAN
  GRID
  LISTE
  KART
  BANNER
}

enum TransitionType {
  FADE
  SLIDE
  ZOOM
  FLIP
  CUBE
  COVERFLOW
}

enum DeviceType {
  DESKTOP
  MOBILE
  TABLET
}

// Category model
model Category {
  id             String     @id @default(cuid())
  name           String
  slug           String     @unique
  description    String
  icon           String
  parentId       String?
  parent         Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children       Category[] @relation("CategoryHierarchy")
  productCount   Int        @default(0)
  isActive       Boolean    @default(true)
  sortOrder      Int        @default(0)
  seoTitle       String?
  seoDescription String?

  // 1. Performans optimizasyonu
  cacheKey        String?
  viewCount       Int     @default(0)
  popularityScore Float   @default(0.0)

  // 2. Indexing & Search
  searchKeywords String?
  isSearchable   Boolean @default(true)

  // 4. Görsel & UI/UX
  categoryImage String?
  colorCode     String?
  iconType      IconType? @default(SVG)

  // 5. Advanced SEO
  metaKeywords  String?
  ogTitle       String?
  ogDescription String?
  ogImage       String?

  // 6. Analytics & Tracking
  conversionRate Float?
  avgOrderValue  Float?

  // 7. Business Rules
  minOrderAmount Float?
  commissionRate Float?
  taxRate        Float?

  // 8. Inventory & Stock alanları kaldırıldı - stok yönetimi Product seviyesinde yapılacak

  // 9. Admin Features
  isPromoted     Boolean        @default(false)
  isFeatured     Boolean        @default(false)
  adminNotes     String?
  approvalStatus ApprovalStatus @default(ONAYLANDI)

  // 10. Audit & History
  version   Int     @default(1)
  changeLog Json?
  createdBy String?

  // 11. Mobile Optimization
  mobileIcon     String?
  mobileTemplate MobileTemplate? @default(VARSAYILAN)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  products Product[]

  // Relations
  banners CategoryBanner[]

  @@map("categories")
}

// CategoryBanner model - Çoklu banner sistemi
model CategoryBanner {
  id         String   @id @default(cuid())
  categoryId String
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  // Görsel ve İçerik
  imageUrl       String
  imageAlt       String?
  mobileImageUrl String? // Responsive için ayrı mobil görsel
  title          String?
  subtitle       String?
  description    String?

  // Call-to-Action
  ctaText String?
  ctaUrl  String?

  // Görüntüleme Ayarları
  displayOrder    Int       @default(0)
  isActive        Boolean   @default(true)
  startDate       DateTime?
  endDate         DateTime?
  displayDuration Int       @default(5) // saniye cinsinden

  // Stil ve Animasyon
  transitionType  TransitionType @default(FADE)
  backgroundColor String?
  textColor       String?

  // Analytics ve Tracking
  clickCount      Int   @default(0)
  impressionCount Int   @default(0)
  ctr             Float @default(0.0)
  priority        Int   @default(1) // A/B testing için

  // Hedefleme
  targetAudience String? // JSON format: {"age": "25-45", "gender": "all"}
  deviceType     DeviceType[] @default([DESKTOP, MOBILE, TABLET])
  geoLocation    String? // Ülke/şehir kodları
  seasonalTags   String? // "summer,winter,holiday"

  // Business
  conversionGoal   String? // "sales,signup,download"
  budgetAllocation Float?

  // Audit
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy String?

  @@index([categoryId, displayOrder])
  @@index([isActive, startDate, endDate])
  @@map("category_banners")
}

// Product model - Enhanced with best practices
model Product {
  id               String   @id @default(cuid())
  name             String
  slug             String   @unique
  description      String
  shortDescription String
  categoryId       String
  category         Category @relation(fields: [categoryId], references: [id])
  brand            String
  model            String?

  // Base pricing (can be overridden by variants)
  basePrice      Float
  baseCostPrice  Float?
  wholesalePrice Float?
  retailPrice    Float?
  taxRate        Float  @default(18)
  vatRate        Float  @default(18)
  currency       String @default("TRY")
  profitMargin   Float?

  // Product-level settings
  hasVariants Boolean @default(false)
  trackStock  Boolean @default(true)

  // Enhanced Inventory Management
  stockQuantity     Int                @default(0) // Current total stock
  minStockThreshold Int                @default(0) // Low stock alert threshold
  maxStockCapacity  Int                @default(100) // Maximum storage capacity
  reservedStock     Int                @default(0) // Stock reserved in carts/orders
  reorderPoint      Int? // Automatic reorder trigger
  reorderQuantity   Int? // Quantity to reorder
  leadTime          Int? // days
  stockLocation     String? // Warehouse location
  batchNumber       String?
  expiryDate        DateTime?
  serialNumbers     Json? // array of serial numbers
  stockStatus       ProductStockStatus @default(IN_STOCK) // Computed status

  // Status flags
  isActive   Boolean @default(true)
  isFeatured Boolean @default(false)
  isNew      Boolean @default(false)
  isOnSale   Boolean @default(false)
  isDigital  Boolean @default(false)
  isVirtual  Boolean @default(false)

  // SEO & Marketing
  seoTitle        String?
  seoDescription  String?
  metaKeywords    String? // JSON array as string
  canonicalUrl    String?
  robotsDirective String? @default("index,follow")
  structuredData  Json? // Schema.org data
  focusKeyword    String?
  seoScore        Float?

  // Marketing
  promotionTags  Json? // array of promotion tags
  seasonalTags   Json? // array of seasonal tags
  targetAudience String?
  marketingNotes String?

  // Physical properties (base values)
  weight             Float?
  dimensions         String? // JSON object as string
  shippingWeight     Float?
  shippingDimensions Json?
  shippingClass      String?
  freeShippingLimit  Float?
  hazardousMaterial  Boolean @default(false)
  fragile            Boolean @default(false)
  requiresSignature  Boolean @default(false)

  // Quality & Compliance
  qualityGrade        String?
  manufacturingDate   DateTime?
  warrantyPeriod      Int? // months
  warrantyType        String?
  certifications      Json? // array of certifications
  complianceStandards Json?
  safetyWarnings      Json?
  ageRestriction      Int?

  // Analytics & Performance
  viewCount       Int    @default(0)
  clickCount      Int    @default(0)
  addToCartCount  Int    @default(0)
  purchaseCount   Int    @default(0)
  conversionRate  Float?
  bounceRate      Float?
  avgTimeOnPage   Float?
  searchRanking   Float?
  popularityScore Float  @default(0)
  trendingScore   Float  @default(0)

  // Content & Media
  videoUrl             String?
  video360Url          String?
  arModelUrl           String?
  downloadableFiles    Json?
  instructionManual    String?
  assemblyInstructions String?
  careInstructions     String?

  // Internationalization
  translations         Json? // multi-language content
  availableLanguages   Json? // array of language codes
  defaultLanguage      String @default("tr")
  localizedPricing     Json? // pricing per region/currency
  regionalAvailability Json? // availability per region

  // Business Intelligence
  profitability     String? // high, medium, low
  lifecycle         String? // introduction, growth, maturity, decline
  seasonality       String? // seasonal patterns
  demandForecast    Float?
  inventoryTurnover Float?
  abcClassification String? // A, B, C classification

  // Product Relationships
  parentProductId     String?
  relatedProducts     Json? // array of product IDs
  crossSellProducts   Json? // array of product IDs
  upSellProducts      Json? // array of product IDs
  bundleProducts      Json? // array of product IDs
  alternativeProducts Json? // array of product IDs

  // Timestamps
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  publishedAt     DateTime?
  lastViewedAt    DateTime?
  lastPurchasedAt DateTime?

  // Relations
  variants       ProductVariant[]
  images         ProductImage[]
  videos         ProductVideo[]
  specifications ProductSpecification[]
  certificates   ProductCertificate[]
  attributes     ProductAttribute[]
  reviews        ProductReview[]
  tags           ProductTag[]
  discounts      ProductDiscount[]
  priceHistory   ProductPriceHistory[]
  stockMovements StockMovement[]

  // Cart and Order Relations
  cartItems         CartItem[]         @relation("CartItems")
  orderItems        OrderItem[]        @relation("OrderItems")
  stockReservations StockReservation[] @relation("StockReservations")

  @@index([slug])
  @@index([categoryId])
  @@index([brand])
  @@index([isActive])
  @@index([isFeatured])
  @@index([popularityScore])
  @@index([createdAt])
  @@index([publishedAt])
  @@map("products")
}

// Product Variant model - Individual variants with specific attributes
model ProductVariant {
  id        String  @id @default(cuid())
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  // Variant identification
  sku     String  @unique
  barcode String?

  // Variant-specific pricing
  price         Float
  originalPrice Float?
  costPrice     Float?

  // Variant-specific stock
  stock       Int                @default(0)
  minStock    Int                @default(0)
  stockStatus ProductStockStatus @default(IN_STOCK)

  // Variant attributes (JSON format for flexibility)
  attributes Json // e.g., {"color": "red", "size": "XL", "material": "cotton"}

  // Variant-specific properties
  weight     Float?
  dimensions String? // JSON object as string

  // Status
  isActive  Boolean @default(true)
  isDefault Boolean @default(false) // Mark default variant for display

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  images ProductVariantImage[]

  @@index([productId])
  @@index([sku])
  @@map("product_variants")
}

// Product Attribute Definition - Defines available attributes for products
model ProductAttribute {
  id        String  @id @default(cuid())
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  name       String // e.g., "Color", "Size", "Material"
  type       ProductAttributeType // COLOR, SIZE, TEXT, NUMBER
  isRequired Boolean              @default(false)
  sortOrder  Int                  @default(0)

  // Attribute options (for predefined values)
  options ProductAttributeOption[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([productId])
  @@map("product_attributes")
}

// Product Attribute Options - Predefined values for attributes
model ProductAttributeOption {
  id          String           @id @default(cuid())
  attributeId String
  attribute   ProductAttribute @relation(fields: [attributeId], references: [id], onDelete: Cascade)

  value       String // e.g., "Red", "XL", "Cotton"
  displayName String // e.g., "Kırmızı", "Extra Large"
  colorCode   String? // For color attributes
  sortOrder   Int     @default(0)
  isActive    Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([attributeId])
  @@map("product_attribute_options")
}

// Product Variant Images - Variant-specific images
model ProductVariantImage {
  id        String         @id @default(cuid())
  variantId String
  variant   ProductVariant @relation(fields: [variantId], references: [id], onDelete: Cascade)
  url       String
  alt       String
  title     String?
  sortOrder Int            @default(0)
  isMain    Boolean        @default(false)
  size      Int            @default(0)
  width     Int            @default(0)
  height    Int            @default(0)
  format    String         @default("jpg")

  @@index([variantId])
  @@map("product_variant_images")
}

// Enum for attribute types
enum ProductAttributeType {
  COLOR
  SIZE
  TEXT
  NUMBER
  BOOLEAN
}

// Product Review model
model ProductReview {
  id        String  @id @default(cuid())
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  // Review details
  rating  Int // 1-5 stars
  title   String?
  comment String?

  // Reviewer info (can be anonymous)
  reviewerName       String?
  reviewerEmail      String?
  isVerifiedPurchase Boolean @default(false)

  // Status
  isApproved Boolean @default(false)
  isHelpful  Int     @default(0) // helpful votes

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([productId])
  @@index([rating])
  @@index([isApproved])
  @@map("product_reviews")
}

// Product Tag model
model ProductTag {
  id        String  @id @default(cuid())
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  name  String
  slug  String
  type  String? // category, feature, material, etc.
  color String? // for UI display

  createdAt DateTime @default(now())

  @@unique([productId, slug])
  @@index([productId])
  @@index([slug])
  @@map("product_tags")
}

// Product Discount model - Flexible discount system
model ProductDiscount {
  id         String   @id @default(cuid())
  productId  String? // null for global discounts
  product    Product? @relation(fields: [productId], references: [id], onDelete: Cascade)
  variantId  String? // specific variant discount
  categoryId String? // category-wide discount

  // Discount identification
  name        String // e.g., "Black Friday Sale", "Summer Discount"
  code        String? // discount code if applicable
  description String?

  // Discount type and value
  type           DiscountType // PERCENTAGE, FIXED_AMOUNT, BUY_X_GET_Y, etc.
  value          Float // percentage (0-100) or fixed amount
  maxDiscount    Float? // maximum discount amount for percentage discounts
  minOrderAmount Float? // minimum order amount to apply discount

  // Buy X Get Y specific fields
  buyQuantity        Int? // buy X items
  getQuantity        Int? // get Y items free/discounted
  getDiscountPercent Float? // discount on the "get" items

  // Validity period
  startDate DateTime
  endDate   DateTime

  // Usage limits
  usageLimit Int? // total usage limit
  usageCount Int  @default(0)
  userLimit  Int? // per user limit

  // Conditions
  conditions Json? // complex conditions as JSON

  // Targeting
  customerGroups Json? // array of customer group IDs
  regions        Json? // array of region codes

  // Status and priority
  isActive  Boolean @default(true)
  priority  Int     @default(0) // higher priority discounts apply first
  stackable Boolean @default(false) // can be combined with other discounts

  // Analytics
  clickCount      Int   @default(0)
  conversionCount Int   @default(0)
  totalSavings    Float @default(0)

  // Audit
  createdBy String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  usages DiscountUsage[]

  @@index([productId])
  @@index([variantId])
  @@index([categoryId])
  @@index([startDate, endDate])
  @@index([isActive])
  @@index([priority])
  @@index([code])
  @@map("product_discounts")
}

// Discount Usage Tracking
model DiscountUsage {
  id         String          @id @default(cuid())
  discountId String
  discount   ProductDiscount @relation(fields: [discountId], references: [id], onDelete: Cascade)

  // Usage details
  userId    String? // customer who used the discount
  orderId   String? // order where discount was applied
  productId String? // specific product if applicable
  variantId String? // specific variant if applicable

  // Usage amounts
  originalAmount Float // original price
  discountAmount Float // discount applied
  finalAmount    Float // final price after discount

  // Usage context
  ipAddress String?
  userAgent String?
  sessionId String?

  usedAt DateTime @default(now())

  @@index([discountId])
  @@index([userId])
  @@index([orderId])
  @@index([usedAt])
  @@map("discount_usage")
}

// Product Price History model - Enhanced
model ProductPriceHistory {
  id        String  @id @default(cuid())
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  variantId String? // null for base product price

  oldPrice   Float
  newPrice   Float
  changeType PriceChangeType // INCREASE, DECREASE, PROMOTION, COST_CHANGE, etc.
  reason     String? // reason for price change
  discountId String? // if change is due to discount

  // Price components
  basePrice Float? // base price at time of change
  costPrice Float? // cost price at time of change
  margin    Float? // profit margin

  effectiveDate DateTime @default(now())
  createdBy     String? // user who made the change
  createdAt     DateTime @default(now())

  @@index([productId])
  @@index([variantId])
  @@index([effectiveDate])
  @@index([changeType])
  @@map("product_price_history")
}

// Enums for discount system
enum DiscountType {
  PERCENTAGE // 20% off
  FIXED_AMOUNT // $10 off
  BUY_X_GET_Y // Buy 2 Get 1 Free
  FREE_SHIPPING // Free shipping
  BULK_DISCOUNT // Quantity-based discount
  TIERED_DISCOUNT // Spend $100 get 10%, $200 get 20%
}

enum PriceChangeType {
  INCREASE
  DECREASE
  PROMOTION
  COST_CHANGE
  MARKET_ADJUSTMENT
  SEASONAL_CHANGE
  COMPETITOR_MATCH
  CLEARANCE
}

// Stock Movement model for inventory tracking
model StockMovement {
  id        String  @id @default(cuid())
  productId String
  variantId String?

  // Movement details
  movementType    String // 'in', 'out', 'adjustment'
  quantity        Int    // positive for in, negative for out
  previousStock   Int
  newStock        Int

  // Business context
  reason     String? // 'purchase', 'sale', 'damage', 'adjustment', 'return', 'transfer'
  reference  String? // order ID, invoice number, adjustment ID, etc.
  unitCost   Float?  // cost per unit for purchases
  supplier   String? // supplier name for purchases
  notes      String?

  // Audit trail
  createdBy String? // user ID who made the change
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  // Indexes for performance
  @@index([productId])
  @@index([variantId])
  @@index([movementType])
  @@index([reason])
  @@index([createdAt])
  @@index([createdBy])
  @@map("stock_movements")
}

enum ProductStockStatus {
  IN_STOCK
  OUT_OF_STOCK
  LOW_STOCK
}

// Cart Management Models
model Cart {
  id        String     @id @default(cuid())
  sessionId String     @unique
  userId    String?
  items     CartItem[]

  // Cart metadata
  totalItems     Int   @default(0)
  totalAmount    Float @default(0)
  discountAmount Float @default(0)

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  expiresAt DateTime // Auto-expire after 24 hours

  @@index([sessionId])
  @@index([userId])
  @@index([expiresAt])
  @@map("carts")
}

model CartItem {
  id        String  @id @default(cuid())
  cartId    String
  cart      Cart    @relation(fields: [cartId], references: [id], onDelete: Cascade)
  productId String
  product   Product @relation("CartItems", fields: [productId], references: [id], onDelete: Cascade)

  // Item details
  quantity     Int
  unitPrice    Float // Price when added to cart
  currentPrice Float // Current price (updated with discounts)
  totalPrice   Float // quantity * currentPrice

  // Stock reservation
  reservedAt    DateTime @default(now())
  reservationId String? // Link to stock reservation

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([cartId, productId])
  @@index([productId])
  @@index([reservedAt])
  @@map("cart_items")
}

// Stock Reservation Model
model StockReservation {
  id        String  @id @default(cuid())
  productId String
  product   Product @relation("StockReservations", fields: [productId], references: [id], onDelete: Cascade)

  // Reservation details
  quantity   Int
  sessionId  String
  cartItemId String?
  orderId    String?

  // Reservation lifecycle
  status ReservationStatus @default(ACTIVE)
  reason String? // cart, order, manual

  // Timestamps
  createdAt  DateTime  @default(now())
  expiresAt  DateTime
  releasedAt DateTime?

  @@index([productId])
  @@index([sessionId])
  @@index([status])
  @@index([expiresAt])
  @@map("stock_reservations")
}

enum ReservationStatus {
  ACTIVE
  EXPIRED
  RELEASED
  CONVERTED // Converted to order
}

// Order Management Models
model Order {
  id          String  @id @default(cuid())
  orderNumber String  @unique
  sessionId   String?
  userId      String?

  // Order details
  items  OrderItem[]
  status OrderStatus @default(PENDING)

  // Pricing
  subtotal       Float
  discountAmount Float @default(0)
  taxAmount      Float @default(0)
  shippingAmount Float @default(0)
  totalAmount    Float

  // Customer information
  customerEmail   String?
  customerPhone   String?
  shippingAddress Json?
  billingAddress  Json?

  // Payment
  paymentStatus PaymentStatus @default(PENDING)
  paymentMethod String?
  paymentId     String?

  // Fulfillment
  shippingMethod String?
  trackingNumber String?
  shippedAt      DateTime?
  deliveredAt    DateTime?

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([orderNumber])
  @@index([status])
  @@index([paymentStatus])
  @@index([createdAt])
  @@map("orders")
}

model OrderItem {
  id      String @id @default(cuid())
  orderId String
  order   Order  @relation(fields: [orderId], references: [id], onDelete: Cascade)

  productId String
  product   Product @relation("OrderItems", fields: [productId], references: [id])

  // Item details at time of order
  quantity    Int
  unitPrice   Float
  totalPrice  Float
  productName String // Snapshot of product name
  productSku  String? // Snapshot of SKU

  // Stock management
  stockDeducted   Boolean   @default(false)
  stockDeductedAt DateTime?

  @@index([orderId])
  @@index([productId])
  @@map("order_items")
}

enum OrderStatus {
  PENDING
  CONFIRMED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
  REFUNDED
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
  PARTIALLY_REFUNDED
}

// Product Image model
model ProductImage {
  id        String  @id @default(cuid())
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  url       String
  alt       String
  title     String?
  sortOrder Int     @default(0)
  isMain    Boolean @default(false)
  size      Int     @default(0)
  width     Int     @default(0)
  height    Int     @default(0)
  format    String  @default("jpg")

  @@map("product_images")
}

// Product Video model
model ProductVideo {
  id          String  @id @default(cuid())
  productId   String
  product     Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  url         String
  title       String
  description String?
  thumbnail   String?
  duration    Int?
  sortOrder   Int     @default(0)

  @@map("product_videos")
}

// Product Specification model
model ProductSpecification {
  id        String  @id @default(cuid())
  productId String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  name      String
  value     String
  unit      String?
  sortOrder Int     @default(0)

  @@map("product_specifications")
}

// Product Certificate model
model ProductCertificate {
  id          String    @id @default(cuid())
  productId   String
  product     Product   @relation(fields: [productId], references: [id], onDelete: Cascade)
  name        String
  issuer      String
  number      String?
  validUntil  DateTime?
  documentUrl String?

  @@map("product_certificates")
}
