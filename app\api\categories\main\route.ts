import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import type { Category } from "@/types"

// GET /api/categories/main - Ana kategorileri getir
export async function GET() {
  try {
    // Get main categories (parentId is null) that are active
    const mainCategories = await prisma.category.findMany({
      where: {
        parentId: null,
        isActive: true
      },
      orderBy: {
        sortOrder: 'asc'
      },
      include: {
        children: {
          where: { isActive: true },
          orderBy: { sortOrder: 'asc' }
        },
        products: {
          select: { id: true }
        }
      }
    })

    // Transform to match existing interface
    const transformedCategories: Category[] = mainCategories.map(category => ({
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      icon: category.icon,
      parentId: category.parentId,
      children: category.children,
      productCount: category.products.length,
      isActive: category.isActive,
      sortOrder: category.sortOrder,
      seoTitle: category.seoTitle,
      seoDescription: category.seoDescription,
      bannerImage: category.bannerImage,
      mobileIcon: category.mobileIcon,
      ogImage: category.ogImage,
      createdAt: category.createdAt,
      updatedAt: category.updatedAt,
    }))

    return NextResponse.json({
      success: true,
      data: transformedCategories,
      message: "Ana kategoriler başarıyla getirildi",
    })
  } catch (error) {
    console.error("Categories main GET error:", error)
    return NextResponse.json({ success: false, message: "Ana kategoriler yüklenirken hata oluştu" }, { status: 500 })
  }
}
