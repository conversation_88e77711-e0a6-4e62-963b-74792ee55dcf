const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function checkDatabase() {
  try {
    console.log('🔍 Database kontrolü başlıyor...')
    
    // Kategorileri say
    const categoryCount = await prisma.categories.count()
    console.log(`📊 Toplam kategori sayısı: ${categoryCount}`)
    
    if (categoryCount > 0) {
      // İlk 5 kategoriyi göster
      const categories = await prisma.categories.findMany({
        take: 5,
        select: {
          id: true,
          name: true,
          isActive: true,
          parentId: true,
          createdAt: true
        }
      })
      
      console.log('📋 İlk 5 kategori:')
      categories.forEach((cat, index) => {
        console.log(`  ${index + 1}. ${cat.name} (ID: ${cat.id}, Aktif: ${cat.isActive}, Parent: ${cat.parentId || 'Ana kategori'})`)
      })
    } else {
      console.log('❌ Database\'de hiç kategori yok!')
      console.log('💡 Seed script çalıştırmanız gerekebilir: npm run db:seed')
    }
    
    // Ürünleri de kontrol et
    const productCount = await prisma.products.count()
    console.log(`📦 Toplam ürün sayısı: ${productCount}`)
    
  } catch (error) {
    console.error('❌ Database kontrolü sırasında hata:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkDatabase()
