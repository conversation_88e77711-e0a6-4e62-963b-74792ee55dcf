import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Home, Search, Folder } from "lucide-react"

export default function CategoryNotFound() {
  return (
    <div className="container mx-auto px-4 py-16 text-center">
      <div className="max-w-md mx-auto">
        <div className="mb-8">
          <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <Folder className="w-12 h-12 text-gray-400" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Kategori Bulunamadı</h1>
          <p className="text-gray-600">
            Aradığınız kategori bulunamadı veya artık mevcut değil. Lütfen diğer kategorilerimize göz atın.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button asChild>
            <Link href="/urunler">
              <Search className="mr-2 h-4 w-4" />
              Tüm Ürünler
            </Link>
          </Button>
          <Button variant="outline" asChild>
            <Link href="/">
              <Home className="mr-2 h-4 w-4" />
              Ana Sayfa
            </Link>
          </Button>
        </div>
      </div>
    </div>
  )
}
