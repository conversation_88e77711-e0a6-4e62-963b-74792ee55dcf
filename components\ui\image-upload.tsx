"use client"

import React, { useState, useCallback, useRef } from 'react'
import { Upload, X, Image as ImageIcon, Loader2, Star } from 'lucide-react'
import { Button } from './button'
import { cn } from '@/lib/utils'
import { validateImageFiles, uploadImages, deleteImage } from '@/lib/image-utils'
import type { ProductImage } from '@/types'

interface ImageUploadProps {
  images: ProductImage[]
  onImagesChange: (images: ProductImage[]) => void
  maxImages?: number
  maxFileSize?: number
  className?: string
  disabled?: boolean
}

export function ImageUpload({
  images,
  onImagesChange,
  maxImages = 10,
  maxFileSize = 5 * 1024 * 1024, // 5MB
  className,
  disabled = false
}: ImageUploadProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({})
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    if (!disabled) {
      setIsDragging(true)
    }
  }, [disabled])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }, [])

  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)

    if (disabled) return

    const files = Array.from(e.dataTransfer.files).filter(file => 
      file.type.startsWith('image/')
    )

    if (files.length > 0) {
      await handleFileUpload(files)
    }
  }, [disabled])

  const handleFileSelect = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    if (files.length > 0) {
      await handleFileUpload(files)
    }
    // Reset input value to allow selecting the same file again
    e.target.value = ''
  }, [])

  const handleFileUpload = async (files: File[]) => {
    if (disabled || isUploading) return

    // Check if adding these files would exceed the limit
    if (images.length + files.length > maxImages) {
      alert(`Maksimum ${maxImages} görsel yükleyebilirsiniz`)
      return
    }

    // Validate files
    const validation = validateImageFiles(files)
    if (!validation.valid) {
      alert(validation.errors.join('\n'))
      return
    }

    setIsUploading(true)

    try {
      // Upload files
      const uploadResults = await uploadImages(files)
      
      // Convert to ProductImage format and add to existing images
      const newImages: ProductImage[] = uploadResults.map((result, index) => ({
        id: result.id,
        url: result.url,
        alt: result.alt,
        title: result.title,
        sortOrder: images.length + index,
        isMain: images.length === 0 && index === 0, // First image is main if no existing images
        size: result.size,
        width: result.width,
        height: result.height,
        format: result.format,
      }))

      onImagesChange([...images, ...newImages])
    } catch (error) {
      console.error('Upload error:', error)
      alert(error instanceof Error ? error.message : 'Görsel yükleme hatası')
    } finally {
      setIsUploading(false)
    }
  }

  const handleRemoveImage = async (imageId: string) => {
    if (disabled) return

    const imageToRemove = images.find(img => img.id === imageId)
    if (!imageToRemove) return

    try {
      // Delete from server if it's an uploaded image
      if (imageToRemove.url.startsWith('/uploads/')) {
        const filename = imageToRemove.url.split('/').pop()
        if (filename) {
          await deleteImage(filename)
        }
      }

      // Remove from images array
      const updatedImages = images.filter(img => img.id !== imageId)
      
      // If removed image was main, make first remaining image main
      if (imageToRemove.isMain && updatedImages.length > 0) {
        updatedImages[0].isMain = true
      }

      // Update sort orders
      const reorderedImages = updatedImages.map((img, index) => ({
        ...img,
        sortOrder: index
      }))

      onImagesChange(reorderedImages)
    } catch (error) {
      console.error('Delete error:', error)
      alert('Görsel silinirken hata oluştu')
    }
  }

  const handleSetMainImage = (imageId: string) => {
    if (disabled) return

    const updatedImages = images.map(img => ({
      ...img,
      isMain: img.id === imageId
    }))

    onImagesChange(updatedImages)
  }

  const openFileDialog = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Upload Area */}
      <div
        className={cn(
          "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
          isDragging ? "border-blue-500 bg-blue-50" : "border-gray-300",
          disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer hover:border-gray-400",
          isUploading && "pointer-events-none"
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={openFileDialog}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
          disabled={disabled}
        />

        {isUploading ? (
          <div className="flex flex-col items-center">
            <Loader2 className="h-8 w-8 animate-spin text-blue-500 mb-2" />
            <p className="text-sm text-gray-600">Görseller yükleniyor...</p>
          </div>
        ) : (
          <div className="flex flex-col items-center">
            <Upload className="h-8 w-8 text-gray-400 mb-2" />
            <p className="text-sm text-gray-600 mb-1">
              Görselleri buraya sürükleyin veya tıklayın
            </p>
            <p className="text-xs text-gray-500">
              PNG, JPG, WebP - Maksimum {Math.round(maxFileSize / 1024 / 1024)}MB
            </p>
            <p className="text-xs text-gray-500">
              {images.length}/{maxImages} görsel
            </p>
          </div>
        )}
      </div>

      {/* Image Grid */}
      {images.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          {images.map((image) => (
            <div key={image.id} className="relative group aspect-square">
              <img
                src={image.url}
                alt={image.alt}
                className="w-full h-full object-cover rounded-lg border"
              />
              
              {/* Main Image Badge */}
              {image.isMain && (
                <div className="absolute top-2 left-2 bg-yellow-500 text-white px-2 py-1 rounded text-xs font-medium flex items-center gap-1">
                  <Star className="h-3 w-3" />
                  Ana
                </div>
              )}

              {/* Action Buttons */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100">
                <div className="flex gap-2">
                  {!image.isMain && (
                    <Button
                      type="button"
                      size="sm"
                      variant="secondary"
                      onClick={() => handleSetMainImage(image.id)}
                      disabled={disabled}
                    >
                      Ana Yap
                    </Button>
                  )}
                  <Button
                    type="button"
                    size="sm"
                    variant="destructive"
                    onClick={() => handleRemoveImage(image.id)}
                    disabled={disabled}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Image Count Info */}
      {images.length > 0 && (
        <div className="text-sm text-gray-500 text-center">
          {images.length} görsel yüklendi
          {images.length >= maxImages && (
            <span className="text-orange-500 ml-2">
              (Maksimum limit: {maxImages})
            </span>
          )}
        </div>
      )}
    </div>
  )
}
