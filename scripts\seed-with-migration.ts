#!/usr/bin/env tsx

/**
 * Complete Database Setup Script
 * 
 * This script performs a complete database setup:
 * 1. Applies stock management migration
 * 2. Seeds categories (if needed)
 * 3. Seeds comprehensive product catalog
 * 4. Verifies data integrity
 * 
 * Usage:
 * npx tsx scripts/seed-with-migration.ts
 */

import { PrismaClient } from '@prisma/client'
import { applyStockMigration } from './apply-stock-migration'
import { comprehensiveSeed } from './comprehensive-seed'

const prisma = new PrismaClient()

// Categories data (in case they don't exist)
const categories = [
  {
    name: "<PERSON>ş Koruma",
    slug: "bas-koruma",
    description: "Güvenlik baretleri ve kaskları",
    sortOrder: 1
  },
  {
    name: "Göz Koruma", 
    slug: "goz-koruma",
    description: "Güvenlik gözlükleri ve yüz koruyucuları",
    sortOrder: 2
  },
  {
    name: "<PERSON><PERSON> Korum<PERSON>",
    slug: "kulak-koruma", 
    description: "Kulaklıklar ve kulak tıkaçları",
    sortOrder: 3
  },
  {
    name: "Solunum Koruma",
    slug: "solunum-koruma",
    description: "Maskeler ve solunum cihazları", 
    sortOrder: 4
  },
  {
    name: "El Koruma",
    slug: "el-koruma",
    description: "İş eldivenleri ve el koruyucuları",
    sortOrder: 5
  },
  {
    name: "Ayak Koruma",
    slug: "ayak-koruma",
    description: "Güvenlik ayakkabıları ve botları",
    sortOrder: 6
  },
  {
    name: "Vücut Koruma", 
    slug: "vucut-koruma",
    description: "İş kıyafetleri ve koruyucu tulumlar",
    sortOrder: 7
  },
  {
    name: "Yüksekte Çalışma",
    slug: "yuksekte-calisma",
    description: "Emniyet kemerleri ve yüksekte çalışma ekipmanları",
    sortOrder: 8
  }
]

async function seedCategories() {
  console.log('🏷️  Checking categories...')
  
  const existingCategories = await prisma.category.findMany()
  
  if (existingCategories.length > 0) {
    console.log(`✅ Found ${existingCategories.length} existing categories`)
    return existingCategories.length
  }
  
  console.log('🌱 Creating categories...')
  let createdCount = 0
  
  for (const categoryData of categories) {
    try {
      await prisma.category.create({
        data: {
          name: categoryData.name,
          slug: categoryData.slug,
          description: categoryData.description,
          icon: '',
          parentId: null,
          productCount: 0,
          isActive: true,
          sortOrder: categoryData.sortOrder,
          seoTitle: `${categoryData.name} - İş Güvenliği Ekipmanları`,
          seoDescription: `${categoryData.description}. Kaliteli ve güvenilir ${categoryData.name.toLowerCase()} ürünleri.`,
          
          // Additional fields with defaults
          cacheKey: `cat_${categoryData.slug}_tr`,
          viewCount: 0,
          popularityScore: 0,
          searchKeywords: categoryData.name.toLowerCase(),
          isSearchable: true,
          categoryImage: null,
          colorCode: '#3B82F6',
          iconType: 'SVG',
          metaKeywords: categoryData.name.toLowerCase(),
          ogTitle: `${categoryData.name} - İş Güvenliği`,
          ogDescription: categoryData.description,
          ogImage: null,
          conversionRate: null,
          avgOrderValue: null,
          minOrderAmount: null,
          commissionRate: null,
          taxRate: null,
          isPromoted: false,
          isFeatured: false,
          adminNotes: null,
          approvalStatus: 'ONAYLANDI',
          version: 1,
          changeLog: null,
          createdBy: null,
          mobileIcon: null,
          mobileTemplate: 'VARSAYILAN'
        }
      })
      
      createdCount++
      console.log(`  ✅ Created: ${categoryData.name}`)
      
    } catch (error) {
      console.error(`  ❌ Failed to create category ${categoryData.name}:`, error)
    }
  }
  
  console.log(`✅ Created ${createdCount} categories`)
  return createdCount
}

async function verifyDataIntegrity() {
  console.log('🔍 Verifying data integrity...')
  
  // Check categories
  const categoryCount = await prisma.category.count()
  console.log(`📂 Categories: ${categoryCount}`)
  
  // Check products
  const productCount = await prisma.product.count()
  console.log(`📦 Products: ${productCount}`)
  
  // Check stock data consistency
  const productsWithStock = await prisma.product.findMany({
    select: {
      id: true,
      name: true,
      stockQuantity: true,
      minStockThreshold: true,
      stockStatus: true
    },
    take: 5
  })
  
  console.log('\n📊 Sample stock data:')
  productsWithStock.forEach(product => {
    console.log(`  - ${product.name}: ${product.stockQuantity} units (${product.stockStatus})`)
  })
  
  // Check for any products with inconsistent stock data
  const inconsistentProducts = await prisma.product.count({
    where: {
      OR: [
        { stockQuantity: null },
        { minStockThreshold: null },
        { stockStatus: null }
      ]
    }
  })
  
  if (inconsistentProducts > 0) {
    console.log(`⚠️  Found ${inconsistentProducts} products with missing stock data`)
    return false
  }
  
  console.log('✅ All products have consistent stock data')
  return true
}

async function main() {
  console.log('🚀 Starting complete database setup...')
  console.log('=' .repeat(50))
  
  try {
    // Step 1: Apply stock management migration
    console.log('\n📋 Step 1: Database Migration')
    await applyStockMigration()
    
    // Step 2: Seed categories
    console.log('\n📋 Step 2: Category Setup')
    const categoryCount = await seedCategories()
    
    // Step 3: Seed products
    console.log('\n📋 Step 3: Product Catalog Creation')
    await comprehensiveSeed()
    
    // Step 4: Verify data integrity
    console.log('\n📋 Step 4: Data Verification')
    const isDataConsistent = await verifyDataIntegrity()
    
    // Final summary
    console.log('\n' + '=' .repeat(50))
    console.log('🎉 Database setup completed successfully!')
    console.log('')
    console.log('📊 Setup Summary:')
    console.log(`  ✅ Stock management migration: Applied`)
    console.log(`  ✅ Categories: ${categoryCount} available`)
    console.log(`  ✅ Products: Comprehensive catalog created`)
    console.log(`  ✅ Data integrity: ${isDataConsistent ? 'Verified' : 'Issues found'}`)
    console.log('')
    console.log('🔗 Next Steps:')
    console.log('  1. Start development server: npm run dev')
    console.log('  2. Visit admin panel: http://localhost:3000/admin')
    console.log('  3. Test product management and stock consistency')
    console.log('  4. Verify cart functionality with seeded products')
    console.log('')
    console.log('💡 The stock data inconsistency issue should now be resolved!')
    
  } catch (error) {
    console.error('\n❌ Database setup failed:', error)
    console.error('\n🔧 Troubleshooting:')
    console.error('  1. Check database connection')
    console.error('  2. Ensure PostgreSQL is running')
    console.error('  3. Verify DATABASE_URL in .env file')
    console.error('  4. Try running: npx prisma db push')
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run if called directly
if (require.main === module) {
  main()
    .catch((error) => {
      console.error(error)
      process.exit(1)
    })
}
