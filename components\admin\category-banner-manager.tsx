"use client"

import { useState, useEffect } from "react"
import { Plus, Edit, Trash2, Eye, EyeOff, GripVertical, Calendar, Clock, Target, Copy, CheckSquare, Square, Play } from "lucide-react"
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core'
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable'
import {
  useSortable,
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  getCategoryBanners, 
  deleteCategoryBanner, 
  updateCategoryBanner,
  type CategoryBannerFilter 
} from "@/lib/api/category-banners"
import { TransitionTypeLabels, DeviceTypeLabels } from "@/lib/enums"
import type { CategoryBanner, Category } from "@/types"
import { cn } from "@/lib/utils"
import { BannerPreviewModal } from "@/components/admin/banner-preview-modal"

interface CategoryBannerManagerProps {
  category: Category
  onCreateBanner?: () => void
  onEditBanner?: (banner: CategoryBanner) => void
  onBannerChange?: () => void // Add callback for banner changes
}

export function CategoryBannerManager({
  category,
  onCreateBanner,
  onEditBanner,
  onBannerChange
}: CategoryBannerManagerProps) {
  const [banners, setBanners] = useState<CategoryBanner[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [selectedBanners, setSelectedBanners] = useState<string[]>([])
  const [bulkLoading, setBulkLoading] = useState(false)
  const [showPreview, setShowPreview] = useState(false)

  // Drag & Drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  )

  // Load banners
  const loadBanners = async () => {
    try {
      setLoading(true)
      setError(null)

      const filter: CategoryBannerFilter = {
        categoryId: category.id,
        includeInactive: true
      }

      const response = await getCategoryBanners(filter)
      setBanners(response.data || [])
      onBannerChange?.() // Notify parent of banner changes
    } catch (err) {
      console.error('Error loading banners:', err)
      setError('Banner\'lar yüklenirken hata oluştu')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadBanners()
  }, [category.id])

  // Expose refresh function for parent components
  const refreshBanners = () => {
    loadBanners()
  }

  // Toggle banner active status
  const toggleBannerStatus = async (banner: CategoryBanner) => {
    try {
      await updateCategoryBanner(banner.id, {
        isActive: !banner.isActive
      })
      
      // Update local state
      setBanners(prev => prev.map(b => 
        b.id === banner.id ? { ...b, isActive: !b.isActive } : b
      ))
    } catch (err) {
      console.error('Error updating banner status:', err)
      setError('Banner durumu güncellenirken hata oluştu')
    }
  }

  // Delete banner
  const handleDeleteBanner = async (banner: CategoryBanner) => {
    if (!confirm(`"${banner.title || 'Banner'}" silinsin mi?`)) return

    try {
      await deleteCategoryBanner(banner.id)
      setBanners(prev => prev.filter(b => b.id !== banner.id))
    } catch (err) {
      console.error('Error deleting banner:', err)
      setError('Banner silinirken hata oluştu')
    }
  }

  // Drag & Drop handler
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event
    setIsDragging(false)

    if (!over || active.id === over.id) {
      return
    }

    const oldIndex = banners.findIndex(banner => banner.id === active.id)
    const newIndex = banners.findIndex(banner => banner.id === over.id)

    if (oldIndex === -1 || newIndex === -1) {
      return
    }

    // Optimistic update
    const newBanners = arrayMove(banners, oldIndex, newIndex)
    setBanners(newBanners)

    try {
      // Update display orders
      const updatePromises = newBanners.map((banner, index) =>
        updateCategoryBanner(banner.id, { displayOrder: index })
      )

      await Promise.all(updatePromises)
    } catch (err) {
      console.error('Error updating banner order:', err)
      setError('Banner sıralaması güncellenirken hata oluştu')
      // Revert on error
      loadBanners()
    }
  }

  const handleDragStart = () => {
    setIsDragging(true)
  }

  // Bulk operations
  const handleSelectBanner = (bannerId: string, selected: boolean) => {
    setSelectedBanners(prev =>
      selected
        ? [...prev, bannerId]
        : prev.filter(id => id !== bannerId)
    )
  }

  const handleSelectAll = (selected: boolean) => {
    setSelectedBanners(selected ? banners.map(b => b.id) : [])
  }

  const handleBulkActivate = async (activate: boolean) => {
    if (selectedBanners.length === 0) return

    try {
      setBulkLoading(true)

      const updatePromises = selectedBanners.map(bannerId =>
        updateCategoryBanner(bannerId, { isActive: activate })
      )

      await Promise.all(updatePromises)

      // Update local state
      setBanners(prev => prev.map(banner =>
        selectedBanners.includes(banner.id)
          ? { ...banner, isActive: activate }
          : banner
      ))

      setSelectedBanners([])
    } catch (err) {
      console.error('Error in bulk activate:', err)
      setError(`Banner'lar ${activate ? 'aktif' : 'pasif'} yapılırken hata oluştu`)
    } finally {
      setBulkLoading(false)
    }
  }

  const handleBulkDelete = async () => {
    if (selectedBanners.length === 0) return

    if (!confirm(`${selectedBanners.length} banner silinsin mi? Bu işlem geri alınamaz.`)) return

    try {
      setBulkLoading(true)

      const deletePromises = selectedBanners.map(bannerId =>
        deleteCategoryBanner(bannerId)
      )

      await Promise.all(deletePromises)

      // Update local state
      setBanners(prev => prev.filter(banner => !selectedBanners.includes(banner.id)))
      setSelectedBanners([])
    } catch (err) {
      console.error('Error in bulk delete:', err)
      setError('Banner\'lar silinirken hata oluştu')
    } finally {
      setBulkLoading(false)
    }
  }

  const handleDuplicateBanner = async (banner: CategoryBanner) => {
    try {
      const duplicateData = {
        categoryId: banner.categoryId,
        imageUrl: banner.imageUrl,
        imageAlt: banner.imageAlt ? `${banner.imageAlt} (Kopya)` : undefined,
        mobileImageUrl: banner.mobileImageUrl,
        title: banner.title ? `${banner.title} (Kopya)` : undefined,
        subtitle: banner.subtitle,
        description: banner.description,
        ctaText: banner.ctaText,
        ctaUrl: banner.ctaUrl,
        displayOrder: banners.length, // En sona ekle
        isActive: false, // Kopyalar pasif başlar
        startDate: banner.startDate,
        endDate: banner.endDate,
        displayDuration: banner.displayDuration,
        transitionType: banner.transitionType,
        backgroundColor: banner.backgroundColor,
        textColor: banner.textColor,
        priority: banner.priority,
        deviceType: banner.deviceType,
        geoLocation: banner.geoLocation,
        seasonalTags: banner.seasonalTags,
        conversionGoal: banner.conversionGoal,
        budgetAllocation: banner.budgetAllocation
      }

      await createCategoryBanner(duplicateData)
      loadBanners() // Refresh list
    } catch (err) {
      console.error('Error duplicating banner:', err)
      setError('Banner kopyalanırken hata oluştu')
    }
  }

  // Format date
  const formatDate = (date: Date | string | null) => {
    if (!date) return 'Belirsiz'
    const d = new Date(date)
    return d.toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Check if banner is currently active (considering date range)
  const isBannerCurrentlyActive = (banner: CategoryBanner) => {
    if (!banner.isActive) return false
    
    const now = new Date()
    const start = banner.startDate ? new Date(banner.startDate) : null
    const end = banner.endDate ? new Date(banner.endDate) : null
    
    if (start && now < start) return false
    if (end && now > end) return false
    
    return true
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Banner Yönetimi</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="text-gray-500">Banner'lar yükleniyor...</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Banner Yönetimi</CardTitle>
          <div className="flex items-center gap-2">
            {selectedBanners.length > 0 && (
              <>
                <span className="text-sm text-gray-600">
                  {selectedBanners.length} seçili
                </span>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkActivate(true)}
                  disabled={bulkLoading}
                  className="flex items-center gap-1"
                >
                  <Eye className="w-3 h-3" />
                  Aktif Yap
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => handleBulkActivate(false)}
                  disabled={bulkLoading}
                  className="flex items-center gap-1"
                >
                  <EyeOff className="w-3 h-3" />
                  Pasif Yap
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleBulkDelete}
                  disabled={bulkLoading}
                  className="flex items-center gap-1 text-red-600 hover:text-red-700"
                >
                  <Trash2 className="w-3 h-3" />
                  Sil
                </Button>
              </>
            )}
            {banners.length > 0 && (
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowPreview(true)}
                className="flex items-center gap-2"
              >
                <Play className="w-4 h-4" />
                Önizleme
              </Button>
            )}
            <Button type="button" onClick={onCreateBanner} className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Yeni Banner
            </Button>
          </div>
        </div>
        {error && (
          <div className="text-red-600 text-sm bg-red-50 p-3 rounded-lg">
            {error}
          </div>
        )}
        {banners.length > 0 && (
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="checkbox"
                checked={selectedBanners.length === banners.length}
                onChange={(e) => handleSelectAll(e.target.checked)}
                className="rounded border-gray-300"
              />
              Tümünü Seç
            </label>
            <span>Toplam: {banners.length} banner</span>
            <span>Aktif: {banners.filter(b => isBannerCurrentlyActive(b)).length}</span>
          </div>
        )}
      </CardHeader>
      <CardContent>
        {banners.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-500 mb-4">Henüz banner eklenmemiş</div>
            <Button type="button" onClick={onCreateBanner} variant="outline">
              İlk Banner'ı Ekle
            </Button>
          </div>
        ) : (
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
          >
            <SortableContext
              items={banners.map(b => b.id)}
              strategy={verticalListSortingStrategy}
            >
              <div className="space-y-4">
                {banners
                  .sort((a, b) => a.displayOrder - b.displayOrder)
                  .map((banner) => (
                    <SortableBannerCard
                      key={banner.id}
                      banner={banner}
                      isCurrentlyActive={isBannerCurrentlyActive(banner)}
                      onToggleStatus={() => toggleBannerStatus(banner)}
                      onEdit={() => onEditBanner?.(banner)}
                      onDelete={() => handleDeleteBanner(banner)}
                      onDuplicate={() => handleDuplicateBanner(banner)}
                      isDragging={isDragging}
                      isSelected={selectedBanners.includes(banner.id)}
                      onSelect={(selected) => handleSelectBanner(banner.id, selected)}
                    />
                  ))}
              </div>
            </SortableContext>
          </DndContext>
        )}
      </CardContent>
    </Card>

    {/* Banner Preview Modal */}
    <BannerPreviewModal
      banners={banners}
      open={showPreview}
      onOpenChange={setShowPreview}
    />
  </>
  )
}

// Sortable banner card component
interface SortableBannerCardProps {
  banner: CategoryBanner
  isCurrentlyActive: boolean
  onToggleStatus: () => void
  onEdit: () => void
  onDelete: () => void
  onDuplicate: () => void
  isDragging: boolean
  isSelected: boolean
  onSelect: (selected: boolean) => void
}

function SortableBannerCard(props: SortableBannerCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging: isSortableDragging,
  } = useSortable({ id: props.banner.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isSortableDragging ? 0.5 : 1,
  }

  return (
    <div ref={setNodeRef} style={style}>
      <BannerCard
        {...props}
        dragHandleProps={{ ...attributes, ...listeners }}
        isDragging={isSortableDragging || props.isDragging}
      />
    </div>
  )
}

// Individual banner card component
interface BannerCardProps {
  banner: CategoryBanner
  isCurrentlyActive: boolean
  onToggleStatus: () => void
  onEdit: () => void
  onDelete: () => void
  onDuplicate?: () => void
  dragHandleProps?: any
  isDragging?: boolean
  isSelected?: boolean
  onSelect?: (selected: boolean) => void
}

function BannerCard({
  banner,
  isCurrentlyActive,
  onToggleStatus,
  onEdit,
  onDelete,
  onDuplicate,
  dragHandleProps,
  isDragging = false,
  isSelected = false,
  onSelect
}: BannerCardProps) {
  const formatDate = (date: Date | string | null) => {
    if (!date) return null
    const d = new Date(date)
    return d.toLocaleDateString('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    })
  }

  return (
    <div className={cn(
      "border rounded-lg p-4 transition-colors",
      isCurrentlyActive ? "border-green-200 bg-green-50" : "border-gray-200",
      isDragging && "shadow-lg scale-105",
      isSelected && "ring-2 ring-blue-500 border-blue-300"
    )}>
      <div className="flex items-start gap-4">
        {/* Selection Checkbox */}
        {onSelect && (
          <div className="flex-shrink-0 mt-2">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={(e) => onSelect(e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
          </div>
        )}

        {/* Drag Handle */}
        <div
          className="flex-shrink-0 mt-2 cursor-move text-gray-400 hover:text-gray-600 touch-none"
          {...dragHandleProps}
        >
          <GripVertical className="w-4 h-4" />
        </div>

        {/* Banner Preview */}
        <div className="flex-shrink-0">
          <img
            src={banner.imageUrl}
            alt={banner.imageAlt || banner.title || 'Banner'}
            className="w-20 h-12 object-cover rounded border"
          />
        </div>

        {/* Banner Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div>
              <h4 className="font-medium text-gray-900 truncate">
                {banner.title || 'Başlıksız Banner'}
              </h4>
              {banner.subtitle && (
                <p className="text-sm text-gray-600 truncate">{banner.subtitle}</p>
              )}
              {banner.description && (
                <p className="text-xs text-gray-500 mt-1 line-clamp-2">{banner.description}</p>
              )}
            </div>

            {/* Status Badge */}
            <div className="flex items-center gap-2">
              {isCurrentlyActive ? (
                <Badge variant="default" className="bg-green-100 text-green-800">
                  <Eye className="w-3 h-3 mr-1" />
                  Aktif
                </Badge>
              ) : (
                <Badge variant="secondary">
                  <EyeOff className="w-3 h-3 mr-1" />
                  Pasif
                </Badge>
              )}
            </div>
          </div>

          {/* Banner Details */}
          <div className="mt-3 flex flex-wrap gap-4 text-xs text-gray-500">
            <div className="flex items-center gap-1">
              <Target className="w-3 h-3" />
              Sıra: {banner.displayOrder}
            </div>
            
            <div className="flex items-center gap-1">
              <Clock className="w-3 h-3" />
              {banner.displayDuration}s
            </div>

            {banner.transitionType && (
              <div>
                Geçiş: {TransitionTypeLabels[banner.transitionType]}
              </div>
            )}

            {banner.deviceType && banner.deviceType.length > 0 && (
              <div>
                Cihaz: {banner.deviceType.map(d => DeviceTypeLabels[d]).join(', ')}
              </div>
            )}

            {(banner.startDate || banner.endDate) && (
              <div className="flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                {formatDate(banner.startDate)} - {formatDate(banner.endDate)}
              </div>
            )}
          </div>

          {/* CTA Info */}
          {banner.ctaText && banner.ctaUrl && (
            <div className="mt-2 text-xs">
              <span className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 rounded">
                CTA: {banner.ctaText}
              </span>
            </div>
          )}

          {/* Analytics */}
          {(banner.clickCount > 0 || banner.impressionCount > 0) && (
            <div className="mt-2 flex gap-4 text-xs text-gray-500">
              <span>Gösterim: {banner.impressionCount}</span>
              <span>Tıklama: {banner.clickCount}</span>
              {banner.ctr > 0 && <span>CTR: %{banner.ctr.toFixed(2)}</span>}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex-shrink-0 flex items-center gap-2">
          <Switch
            checked={banner.isActive}
            onCheckedChange={onToggleStatus}
            size="sm"
          />

          {onDuplicate && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={onDuplicate}
              className="flex items-center gap-1"
              title="Banner'ı Kopyala"
            >
              <Copy className="w-3 h-3" />
              Kopyala
            </Button>
          )}

          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={onEdit}
            className="flex items-center gap-1"
          >
            <Edit className="w-3 h-3" />
            Düzenle
          </Button>

          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={onDelete}
            className="flex items-center gap-1 text-red-600 hover:text-red-700"
          >
            <Trash2 className="w-3 h-3" />
            Sil
          </Button>
        </div>
      </div>
    </div>
  )
}
