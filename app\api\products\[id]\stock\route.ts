import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/products/[id]/stock - Get product stock information
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const productId = params.id

    // Get product with stock information
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: {
        id: true,
        name: true,
        slug: true,
        stockQuantity: true,
        minStockThreshold: true,
        maxStockCapacity: true,
        stockStatus: true,
        trackStock: true,
        stockLocation: true,
        reorderPoint: true,
        reorderQuantity: true
      }
    })

    if (!product) {
      return NextResponse.json({
        success: false,
        error: 'Product not found'
      }, { status: 404 })
    }

    // Get recent stock movements
    const recentMovements = await prisma.stockMovement.findMany({
      where: { productId },
      orderBy: { createdAt: 'desc' },
      take: 10,
      select: {
        id: true,
        movementType: true,
        quantity: true,
        previousStock: true,
        newStock: true,
        reason: true,
        reference: true,
        notes: true,
        createdAt: true,
        createdBy: true
      }
    })

    // Calculate stock statistics
    const stockStats = await prisma.stockMovement.aggregate({
      where: { 
        productId,
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
        }
      },
      _sum: {
        quantity: true
      },
      _count: {
        id: true
      }
    })

    // Get stock movements by type for the last 30 days
    const movementsByType = await prisma.stockMovement.groupBy({
      by: ['movementType'],
      where: {
        productId,
        createdAt: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        }
      },
      _sum: {
        quantity: true
      },
      _count: {
        id: true
      }
    })

    // Calculate stock status indicators
    const currentStock = product.stockQuantity || 0
    const minThreshold = product.minStockThreshold || 0
    const maxCapacity = product.maxStockCapacity || 0

    const stockIndicators = {
      isLowStock: currentStock <= minThreshold && minThreshold > 0,
      isOutOfStock: currentStock <= 0,
      isOverCapacity: maxCapacity > 0 && currentStock > maxCapacity,
      stockPercentage: maxCapacity > 0 ? Math.round((currentStock / maxCapacity) * 100) : null,
      daysUntilReorder: null as number | null
    }

    // Calculate estimated days until reorder (simple calculation based on recent usage)
    const outMovements = movementsByType.find(m => m.movementType === 'out')
    if (outMovements && outMovements._sum.quantity && outMovements._sum.quantity < 0) {
      const dailyUsage = Math.abs(outMovements._sum.quantity) / 30
      if (dailyUsage > 0 && currentStock > minThreshold) {
        stockIndicators.daysUntilReorder = Math.floor((currentStock - minThreshold) / dailyUsage)
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        product: {
          ...product,
          stockIndicators
        },
        recentMovements,
        statistics: {
          totalMovements: stockStats._count.id || 0,
          netChange: stockStats._sum.quantity || 0,
          movementsByType: movementsByType.map(m => ({
            type: m.movementType,
            totalQuantity: m._sum.quantity || 0,
            count: m._count.id
          }))
        }
      }
    })

  } catch (error: any) {
    console.error('❌ Product stock GET error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch product stock information',
      details: error.message
    }, { status: 500 })
  }
}

// PATCH /api/products/[id]/stock - Update stock settings (not quantity)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const productId = params.id
    const body = await request.json()

    // Only allow updating stock settings, not quantity
    const allowedFields = {
      minStockThreshold: body.minStockThreshold,
      maxStockCapacity: body.maxStockCapacity,
      trackStock: body.trackStock,
      stockLocation: body.stockLocation,
      reorderPoint: body.reorderPoint,
      reorderQuantity: body.reorderQuantity
    }

    // Remove undefined values
    const updateData = Object.fromEntries(
      Object.entries(allowedFields).filter(([_, value]) => value !== undefined)
    )

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No valid fields to update'
      }, { status: 400 })
    }

    const updatedProduct = await prisma.product.update({
      where: { id: productId },
      data: updateData,
      select: {
        id: true,
        name: true,
        stockQuantity: true,
        minStockThreshold: true,
        maxStockCapacity: true,
        stockStatus: true,
        trackStock: true,
        stockLocation: true,
        reorderPoint: true,
        reorderQuantity: true
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Stock settings updated successfully',
      data: updatedProduct
    })

  } catch (error: any) {
    console.error('❌ Product stock PATCH error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to update stock settings',
      details: error.message
    }, { status: 500 })
  }
}
