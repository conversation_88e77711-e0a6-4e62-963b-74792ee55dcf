"use client"

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Package, DollarSign, TrendingUp, AlertTriangle } from "lucide-react"
import { formatNumber, formatCurrency } from "@/lib/utils"
import type { Product } from "@/types"

interface ProductStatsProps {
  products: Product[]
}

export function ProductStats({ products }: ProductStatsProps) {
  const totalProducts = products.length
  const activeProducts = products.filter((p) => p.isActive).length
  const outOfStockProducts = products.filter((p) => p.stock <= 0).length
  const lowStockProducts = products.filter((p) => p.stock > 0 && p.stock <= p.minStock).length

  const totalRevenuePotential = products.reduce((sum, p) => sum + p.price * p.stock, 0)
  const averagePrice = totalProducts > 0 ? products.reduce((sum, p) => sum + p.price, 0) / totalProducts : 0

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Toplam Ürün</CardTitle>
          <Package className="h-4 w-4 text-gray-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(totalProducts)}</div>
          <p className="text-xs text-gray-500">{formatNumber(activeProducts)} aktif ürün</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Ortalama Fiyat</CardTitle>
          <DollarSign className="h-4 w-4 text-gray-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(averagePrice)}</div>
          <p className="text-xs text-gray-500">Tüm ürünlerin ortalama satış fiyatı</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Stokta Olmayan Ürünler</CardTitle>
          <AlertTriangle className="h-4 w-4 text-red-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">{formatNumber(outOfStockProducts)}</div>
          <p className="text-xs text-gray-500">{formatNumber(lowStockProducts)} ürün az stokta</p>
        </CardContent>
      </Card>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Potansiyel Gelir</CardTitle>
          <TrendingUp className="h-4 w-4 text-gray-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(totalRevenuePotential)}</div>
          <p className="text-xs text-gray-500">Mevcut stoktan elde edilecek tahmini gelir</p>
        </CardContent>
      </Card>
    </div>
  )
}
