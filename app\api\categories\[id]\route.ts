import { type NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { slugify } from "@/lib/utils"
import type { Category } from "@/types"

// GET /api/categories/[id] - <PERSON>gori detayını getir
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const category = await prisma.category.findUnique({
      where: { id: params.id },
      include: {
        children: {
          orderBy: { sortOrder: 'asc' }
        },
        products: {
          select: { id: true }
        }
      }
    })

    if (!category) {
      return NextResponse.json({ success: false, message: "Kategori bulunamadı" }, { status: 404 })
    }

    console.log('📝 GET /api/categories/[id] - Raw category data:', {
      id: category.id,
      name: category.name,
      categoryImage: category.categoryImage,
      hasCategoryImage: !!category.categoryImage
    })

    // Transform to match existing interface
    const transformedCategory: Category = {
      id: category.id,
      name: category.name,
      slug: category.slug,
      description: category.description,
      icon: category.icon,
      parentId: category.parentId,
      children: category.children,
      productCount: category.products.length,
      isActive: category.isActive,
      sortOrder: category.sortOrder,
      seoTitle: category.seoTitle,
      seoDescription: category.seoDescription,

      // Performans optimizasyonu
      cacheKey: category.cacheKey,
      viewCount: category.viewCount,
      popularityScore: category.popularityScore,

      // Indexing & Search
      searchKeywords: category.searchKeywords,
      isSearchable: category.isSearchable,

      // Görsel & UI/UX
      categoryImage: category.categoryImage,
      colorCode: category.colorCode,
      iconType: category.iconType,

      // Advanced SEO
      metaKeywords: category.metaKeywords,
      ogTitle: category.ogTitle,
      ogDescription: category.ogDescription,
      ogImage: category.ogImage,

      // Analytics & Tracking
      conversionRate: category.conversionRate,
      avgOrderValue: category.avgOrderValue,

      // Business Rules
      minOrderAmount: category.minOrderAmount,
      commissionRate: category.commissionRate,
      taxRate: category.taxRate,

      // Admin Features
      isPromoted: category.isPromoted,
      isFeatured: category.isFeatured,
      adminNotes: category.adminNotes,
      approvalStatus: category.approvalStatus,

      // Audit & History
      version: category.version,
      changeLog: category.changeLog,
      createdBy: category.createdBy,

      // Mobile Optimization
      mobileIcon: category.mobileIcon,
      mobileTemplate: category.mobileTemplate,

      createdAt: category.createdAt,
      updatedAt: category.updatedAt,
    }

    return NextResponse.json({
      success: true,
      data: transformedCategory,
    })
  } catch (error) {
    console.error("Category GET error:", error)
    return NextResponse.json({ success: false, message: "Kategori yüklenirken hata oluştu" }, { status: 500 })
  }
}

// PUT /api/categories/[id] - Kategori güncelle
export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    console.log('🔄 PUT /api/categories/[id] - Starting update for ID:', params.id)
    const data = await request.json()
    console.log('📝 Received data:', JSON.stringify(data, null, 2))

    // Check if category exists
    const existingCategory = await prisma.category.findUnique({
      where: { id: params.id }
    })

    if (!existingCategory) {
      return NextResponse.json({ success: false, message: "Kategori bulunamadı" }, { status: 404 })
    }

    // Validation
    if (!data.name || !data.description) {
      return NextResponse.json({ success: false, message: "Kategori adı ve açıklama zorunludur" }, { status: 400 })
    }

    const slug = slugify(data.name)

    // Check slug uniqueness (excluding current category)
    const duplicateCategory = await prisma.category.findFirst({
      where: {
        slug: slug,
        id: { not: params.id }
      }
    })

    if (duplicateCategory) {
      return NextResponse.json({ success: false, message: "Bu isimde bir kategori zaten mevcut" }, { status: 400 })
    }

    // Update category
    console.log('🔄 Attempting to update category with data:', {
      id: params.id,
      dataKeys: Object.keys(data),
      hasRequiredFields: !!(data.name && data.description)
    })

    // Safely parse numeric values
    const parseNumber = (value: any, defaultValue: number = 0): number => {
      if (value === null || value === undefined || value === '') return defaultValue
      const parsed = Number(value)
      return isNaN(parsed) ? defaultValue : parsed
    }

    // Safely handle enum values
    const safeEnumValue = (value: any, validValues: string[], defaultValue?: string) => {
      if (!value) return defaultValue || null
      return validValues.includes(value) ? value : (defaultValue || null)
    }

    // Prepare update data with safe parsing
    const updateData = {
      name: data.name,
      slug,
      description: data.description,
      icon: data.icon || '',
      parentId: data.parentId || null,
      isActive: data.isActive !== false,
      sortOrder: parseNumber(data.sortOrder, 0),
      seoTitle: data.seoTitle || null,
      seoDescription: data.seoDescription || null,

      // Performans optimizasyonu
      cacheKey: data.cacheKey || null,
      viewCount: parseNumber(data.viewCount, 0),
      popularityScore: parseNumber(data.popularityScore, 0),

      // Indexing & Search
      searchKeywords: data.searchKeywords || null,
      isSearchable: data.isSearchable !== false,

      // Görsel & UI/UX
      categoryImage: data.categoryImage || null,
      colorCode: data.colorCode || null,
      iconType: safeEnumValue(data.iconType, ['SVG', 'PNG', 'FONT_ICON', 'EMOJI']),

      // Advanced SEO
      metaKeywords: data.metaKeywords || null,
      ogTitle: data.ogTitle || null,
      ogDescription: data.ogDescription || null,
      ogImage: data.ogImage || null,

      // Analytics & Tracking
      conversionRate: data.conversionRate ? parseNumber(data.conversionRate) : null,
      avgOrderValue: data.avgOrderValue ? parseNumber(data.avgOrderValue) : null,

      // Business Rules
      minOrderAmount: data.minOrderAmount ? parseNumber(data.minOrderAmount) : null,
      commissionRate: data.commissionRate ? parseNumber(data.commissionRate) : null,
      taxRate: data.taxRate ? parseNumber(data.taxRate) : null,

      // Admin Features
      isPromoted: data.isPromoted === true,
      isFeatured: data.isFeatured === true,
      adminNotes: data.adminNotes || null,
      approvalStatus: safeEnumValue(data.approvalStatus, ['TASLAK', 'BEKLEMEDE', 'ONAYLANDI', 'REDDEDILDI'], 'ONAYLANDI'),

      // Audit & History
      version: parseNumber(data.version, 1),
      changeLog: data.changeLog || null,
      createdBy: data.createdBy || null,

      // Mobile Optimization
      mobileIcon: data.mobileIcon || null,
      mobileTemplate: safeEnumValue(data.mobileTemplate, ['VARSAYILAN', 'GRID', 'LISTE', 'KART', 'BANNER']),
    }

    console.log('🔄 Prepared update data:', JSON.stringify(updateData, null, 2))

    const updatedCategory = await prisma.category.update({
      where: { id: params.id },
      data: updateData,
      include: {
        children: {
          orderBy: { sortOrder: 'asc' }
        },
        products: {
          select: { id: true }
        }
      }
    })

    // Transform to match existing interface
    const transformedCategory: Category = {
      id: updatedCategory.id,
      name: updatedCategory.name,
      slug: updatedCategory.slug,
      description: updatedCategory.description,
      icon: updatedCategory.icon,
      parentId: updatedCategory.parentId,
      children: updatedCategory.children,
      productCount: updatedCategory.products.length,
      isActive: updatedCategory.isActive,
      sortOrder: updatedCategory.sortOrder,
      seoTitle: updatedCategory.seoTitle,
      seoDescription: updatedCategory.seoDescription,

      // Performans optimizasyonu
      cacheKey: updatedCategory.cacheKey,
      viewCount: updatedCategory.viewCount,
      popularityScore: updatedCategory.popularityScore,

      // Indexing & Search
      searchKeywords: updatedCategory.searchKeywords,
      isSearchable: updatedCategory.isSearchable,

      // Görsel & UI/UX - IMAGE FIELDS ADDED
      categoryImage: updatedCategory.categoryImage,
      colorCode: updatedCategory.colorCode,
      iconType: updatedCategory.iconType,

      // Görsel & UI/UX
      bannerImage: updatedCategory.bannerImage,
      colorCode: updatedCategory.colorCode,
      iconType: updatedCategory.iconType,

      // Advanced SEO
      metaKeywords: updatedCategory.metaKeywords,
      ogTitle: updatedCategory.ogTitle,
      ogDescription: updatedCategory.ogDescription,
      ogImage: updatedCategory.ogImage,

      // Analytics & Tracking
      conversionRate: updatedCategory.conversionRate,
      avgOrderValue: updatedCategory.avgOrderValue,

      // Business Rules
      minOrderAmount: updatedCategory.minOrderAmount,
      commissionRate: updatedCategory.commissionRate,
      taxRate: updatedCategory.taxRate,

      // Admin Features
      isPromoted: updatedCategory.isPromoted,
      isFeatured: updatedCategory.isFeatured,
      adminNotes: updatedCategory.adminNotes,
      approvalStatus: updatedCategory.approvalStatus,

      // Audit & History
      version: updatedCategory.version,
      changeLog: updatedCategory.changeLog,
      createdBy: updatedCategory.createdBy,

      // Mobile Optimization
      mobileIcon: updatedCategory.mobileIcon,
      mobileTemplate: updatedCategory.mobileTemplate,

      createdAt: updatedCategory.createdAt,
      updatedAt: updatedCategory.updatedAt,
    }

    return NextResponse.json({
      success: true,
      data: transformedCategory,
      message: "Kategori başarıyla güncellendi",
    })
  } catch (error) {
    console.error("❌ Category PUT error:", error)
    console.error("❌ Error details:", {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace',
      categoryId: params.id
    })
    return NextResponse.json({
      success: false,
      message: "Kategori güncellenirken hata oluştu",
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// DELETE /api/categories/[id] - Kategori sil
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Check if category exists
    const category = await prisma.category.findUnique({
      where: { id: params.id },
      include: {
        children: true,
        products: {
          select: { id: true }
        }
      }
    })

    if (!category) {
      return NextResponse.json({ success: false, message: "Kategori bulunamadı" }, { status: 404 })
    }

    // Check for child categories
    if (category.children.length > 0) {
      return NextResponse.json({ success: false, message: "Alt kategorileri olan kategori silinemez" }, { status: 400 })
    }

    // Check for products
    if (category.products.length > 0) {
      return NextResponse.json({ success: false, message: "Ürünleri olan kategori silinemez" }, { status: 400 })
    }

    // Delete category
    await prisma.category.delete({
      where: { id: params.id }
    })

    return NextResponse.json({
      success: true,
      data: null,
      message: "Kategori başarıyla silindi",
    })
  } catch (error) {
    console.error("Category DELETE error:", error)
    return NextResponse.json({ success: false, message: "Kategori silinirken hata oluştu" }, { status: 500 })
  }
}
