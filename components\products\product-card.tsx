"use client"

import type React from "react"
import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Star, Heart, ShoppingCart, Check, Eye } from "lucide-react"
import { AddToCartButton } from "./add-to-cart-button"
import { formatCurrency } from "@/lib/utils"
import { useCartStore } from "@/lib/stores/cart-store"
import { StockDisplay, StockBadge } from "@/components/common/StockDisplay"
import { useStock } from "@/hooks/useStock"
import type { Product } from "@/types"

interface ProductCardProps {
  product: Product
  showAddToCart?: boolean
  className?: string
  variant?: "default" | "compact" | "featured"
}

export default function ProductCard({
  product,
  showAddToCart = true,
  className = "",
  variant = "default",
}: ProductCardProps) {
  const [isWishlisted, setIsWishlisted] = useState(false)
  const [imageError, setImageError] = useState(false)
  const [isHovered, setIsHovered] = useState(false)
  const { items } = useCartStore()

  // Use unified stock hook for real-time stock information
  const { stockInfo } = useStock({ productId: product.id })

  const cartItem = items.find((item) => item.id === product.id)
  const isInCart = !!cartItem
  const cartQuantity = cartItem?.quantity || 0

  const mainImage = product.images?.find((img) => img.isMain) || product.images?.[0]
  const isInStock = stockInfo ? stockInfo.availableStock > 0 : (product.stockStatus === "in_stock" && (product.stockQuantity || 0) > 0)
  const discountPercentage = product.originalPrice
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    : 0

  const handleWishlistToggle = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsWishlisted(!isWishlisted)
    // TODO: Implement wishlist functionality
  }

  const handleImageError = () => {
    setImageError(true)
  }

  // Stock information is now handled by unified stock system

  const cardClasses = `
    group hover:shadow-lg transition-all duration-300 hover:-translate-y-1 
    ${isInCart ? "ring-2 ring-blue-500 ring-opacity-50 bg-blue-50/30" : "bg-white"}
    ${className}
  `

  return (
    <Card className={cardClasses} onMouseEnter={() => setIsHovered(true)} onMouseLeave={() => setIsHovered(false)}>
      {/* Cart Status Badge */}
      {isInCart && <Badge className="absolute top-2 left-2 z-10 bg-blue-600 text-white">Sepette</Badge>}

      {/* Product Badges */}
      <div className="absolute top-2 right-2 z-10 flex flex-col gap-1">
        {product.isNew && <Badge className="bg-green-600 text-white">Yeni</Badge>}
        {product.isOnSale && discountPercentage > 0 && (
          <Badge className="bg-red-600 text-white">%{discountPercentage}</Badge>
        )}
      </div>

      {/* Hover Overlay for Cart Items */}
      {isInCart && isHovered && (
        <div className="absolute inset-0 bg-blue-600/10 z-5 flex items-center justify-center">
          <div className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
            Sepette ({cartQuantity})
          </div>
        </div>
      )}

      <CardContent className="p-0">
        <div className="relative">
          <Link href={`/urun/${product.slug}`}>
            <div className="relative overflow-hidden rounded-t-lg">
              <Image
                src={mainImage?.url || "/placeholder.svg?height=300&width=300"}
                alt={mainImage?.alt || product.name}
                width={300}
                height={300}
                className="w-full h-48 sm:h-56 object-cover transition-transform duration-300 group-hover:scale-105"
                loading="lazy"
                onError={handleImageError}
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
              />

              {/* Overlay on hover */}
              <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

              {/* Cart Status Overlay */}
              {isInCart && (
                <div className="absolute inset-0 bg-blue-600/10 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center gap-2">
                    <Check className="h-4 w-4" />
                    Sepette ({cartQuantity})
                  </div>
                </div>
              )}
            </div>
          </Link>

          {/* Badges */}
          <div className="absolute top-2 left-2 flex flex-col gap-1">
            {product.isNew && (
              <Badge className="bg-green-500 hover:bg-green-600 text-xs font-medium text-white">Yeni</Badge>
            )}
            {product.isOnSale && discountPercentage > 0 && (
              <Badge className="bg-red-500 hover:bg-red-600 text-xs font-medium text-white">
                %{discountPercentage} İndirim
              </Badge>
            )}
            {!isInStock && (
              <Badge variant="secondary" className="text-xs bg-gray-500 text-white">
                Stokta Yok
              </Badge>
            )}
            {isInCart && (
              <Badge className="text-xs bg-blue-600 text-white flex items-center gap-1">
                <ShoppingCart className="h-3 w-3" />
                Sepette
              </Badge>
            )}
          </div>

          {/* Wishlist Button */}
          <Button
            size="icon"
            variant="ghost"
            onClick={handleWishlistToggle}
            className={`absolute top-2 right-2 h-8 w-8 bg-white/90 hover:bg-white transition-colors ${
              isWishlisted ? "text-red-500" : "text-gray-600"
            }`}
          >
            <Heart className={`h-4 w-4 ${isWishlisted ? "fill-current" : ""}`} />
          </Button>

          {/* Quick Action Buttons */}
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <div className="flex flex-col gap-1">
              <Button size="icon" variant="secondary" className="h-8 w-8 bg-white/90 hover:bg-white">
                <Heart className="w-4 h-4" />
              </Button>
              <Button size="icon" variant="secondary" className="h-8 w-8 bg-white/90 hover:bg-white" asChild>
                <Link href={`/urun/${product.slug}`}>
                  <Eye className="w-4 h-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>

        <div className="p-4">
          {/* Category */}
          <div className="text-xs text-gray-500 mb-1 uppercase tracking-wide">{product.category?.name}</div>

          {/* Product Name */}
          <Link href={`/urun/${product.slug}`}>
            <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors leading-tight">
              {product.name}
            </h3>
          </Link>

          {/* Brand */}
          {product.brand && <div className="text-xs text-gray-500 mb-2">{product.brand}</div>}

          {/* Rating */}
          <div className="flex items-center gap-1 mb-3">
            <div className="flex">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className={`h-3 w-3 ${i < 4 ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}`} />
              ))}
            </div>
            <span className="text-xs text-gray-500">(124)</span>
          </div>

          {/* Price */}
          <div className="flex items-center gap-2 mb-4">
            <span className="text-lg font-bold text-gray-900">{formatCurrency(product.price)}</span>
            {product.originalPrice && product.originalPrice > product.price && (
              <span className="text-sm text-gray-500 line-through">{formatCurrency(product.originalPrice)}</span>
            )}
          </div>

          {/* Stock Status - Unified Display */}
          <div className="mb-3">
            {stockInfo ? (
              <StockDisplay
                stockInfo={stockInfo}
                variant="minimal"
                className="justify-between"
              />
            ) : (
              <Badge variant="secondary" className="text-xs">
                Stok bilgisi yükleniyor...
              </Badge>
            )}
          </div>

          {/* Cart Info Panel for items in cart */}
          {isInCart && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-blue-800 font-medium">Sepette: {cartQuantity} adet</span>
                <ShoppingCart className="w-4 h-4 text-blue-600" />
              </div>
            </div>
          )}

          {/* Add to Cart Button */}
          {showAddToCart && <AddToCartButton product={product} className="w-full" showQuantityControls={isInCart} />}

          {/* Quick Actions (when not showing add to cart) */}
          {!showAddToCart && (
            <div className="flex gap-2">
              <Link href={`/urun/${product.slug}`} className="flex-1">
                <Button variant="outline" className="w-full bg-transparent" size="sm">
                  Detayları Gör
                </Button>
              </Link>
              {isInStock && (
                <AddToCartButton
                  product={product}
                  variant="default"
                  size="sm"
                  className="flex-1"
                  showQuantityControls={isInCart}
                />
              )}
            </div>
          )}

          {/* Stock Warnings */}
          {stockInfo && isInCart && cartQuantity >= stockInfo.availableStock && (
            <div className="mt-2 text-xs text-red-600">Maksimum stok sepetinizde!</div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
