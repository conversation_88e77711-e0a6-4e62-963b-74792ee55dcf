/**
 * Test Script for SEO Generation Service
 * Run this to test the LLM-based SEO generation
 */

const testProduct = {
  productName: "Onvec Smart Tag Akıllı Takip Cihazı 4 adet (Apple uyumlu)",
  productDescription: "Onvec Smart Tag Akıllı Takip Cihazı, anahtarlarınızdan değerli eşyalarınıza ve hatta evcil hayvanlarınıza kadar birçok farklı şeyi takip etmek için kullanabileceğiniz kullanışlı bir cihazdır. Eşyalarınızı kaybetme endişesi olmadan güvenle kullanabilirsiniz.",
  category: "Telefonlar & Aksesuarlar",
  brand: "Onvec",
  price: 899,
  storeName: "Teknoloji Mağazası"
}

async function testSEOGeneration() {
  try {
    console.log('🚀 Testing SEO Generation Service...\n')
    console.log('📦 Test Product:', testProduct.productName)
    console.log('🏷️ Category:', testProduct.category)
    console.log('💰 Price:', testProduct.price, 'TL\n')

    // First, test the API status
    console.log('🔍 Checking API status...')
    const statusResponse = await fetch('http://localhost:3000/api/seo/generate')
    const statusData = await statusResponse.json()

    console.log('📊 API Status:', {
      running: statusData.success,
      provider: statusData.status?.llmProvider,
      model: statusData.status?.model,
      llmConfigured: statusData.status?.llmConfigured,
      fallbackEnabled: statusData.status?.fallbackEnabled
    })

    if (!statusData.status?.llmConfigured) {
      console.log('⚠️  Warning: OpenRouter API not configured, will use fallback generation\n')
    } else {
      console.log('✅ OpenRouter AI configured and ready\n')
    }

    console.log('\n🚀 Starting SEO generation...')
    const startTime = Date.now()

    const response = await fetch('http://localhost:3000/api/seo/generate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testProduct)
    })

    const duration = Date.now() - startTime
    console.log(`⏱️  Request completed in ${duration}ms`)

    const result = await response.json()

    if (!response.ok) {
      console.error(`❌ HTTP ${response.status}:`, result.message || response.statusText)
      if (result.details) {
        console.error('📋 Details:', result.details)
      }
      return
    }

    if (result.success) {
      console.log('✅ SEO Generation Successful!\n')

      const seoData = result.data
      const meta = result.meta || {}

      console.log('📊 Generation Info:')
      console.log(`   Processing Time: ${meta.processingTime || duration}ms`)
      console.log(`   Generated Fields: ${meta.generatedFields || Object.keys(seoData).length}`)
      console.log(`   Fallback Used: ${meta.fallbackUsed ? 'Yes' : 'No'}`)

      console.log('\n📋 Generated SEO Data:')
      console.log('=' .repeat(60))

      console.log('\n🔍 BASIC SEO:')
      console.log(`   SEO Title (${seoData.seoTitle?.length || 0}/60): "${seoData.seoTitle}"`)
      console.log(`   SEO Description (${seoData.seoDescription?.length || 0}/160): "${seoData.seoDescription}"`)
      console.log(`   Focus Keyword: "${seoData.focusKeyword}"`)
      console.log(`   Meta Keywords (${seoData.metaKeywords?.length || 0}): [${seoData.metaKeywords?.join(', ')}]`)
      console.log(`   Canonical URL: "${seoData.canonicalUrl}"`)
      console.log(`   Robots: "${seoData.robotsDirective}"`)

      console.log('\n📱 SOCIAL MEDIA:')
      console.log(`   OG Title: "${seoData.ogTitle}"`)
      console.log(`   OG Description: "${seoData.ogDescription}"`)
      console.log(`   OG Type: "${seoData.ogType}"`)
      console.log(`   Twitter Card: "${seoData.twitterCard}"`)
      console.log(`   Twitter Title: "${seoData.twitterTitle}"`)
      console.log(`   Twitter Description: "${seoData.twitterDescription}"`)

      console.log('\n⚙️ TECHNICAL SEO:')
      console.log(`   Alternative Text: "${seoData.alternativeText}"`)
      console.log(`   Breadcrumbs: ${seoData.breadcrumbs?.join(' > ')}`)

      console.log('\n🎯 STRUCTURED DATA:')
      if (seoData.structuredData) {
        console.log(`   Schema Type: ${seoData.structuredData['@type']}`)
        console.log(`   Product Name: ${seoData.structuredData.name}`)
        console.log(`   Brand: ${seoData.structuredData.brand?.name}`)
        console.log(`   Price: ${seoData.structuredData.offers?.price} ${seoData.structuredData.offers?.priceCurrency}`)
        console.log(`   Availability: ${seoData.structuredData.offers?.availability}`)
      }

      console.log('\n📋 SCHEMA MARKUP (JSON-LD):')
      try {
        const parsedSchema = JSON.parse(seoData.schemaMarkup)
        console.log(JSON.stringify(parsedSchema, null, 2))
      } catch (e) {
        console.log('   Invalid JSON:', seoData.schemaMarkup)
      }

      console.log('\n🎉 Test completed successfully!')

      // Validation checks
      console.log('\n🔍 VALIDATION CHECKS:')
      const checks = []
      if (seoData.seoTitle && seoData.seoTitle.length <= 60) checks.push('✅ SEO Title length OK')
      else checks.push('❌ SEO Title too long or missing')

      if (seoData.seoDescription && seoData.seoDescription.length <= 160) checks.push('✅ SEO Description length OK')
      else checks.push('❌ SEO Description too long or missing')

      if (seoData.metaKeywords && seoData.metaKeywords.length <= 10) checks.push('✅ Meta Keywords count OK')
      else checks.push('❌ Too many meta keywords or missing')

      if (seoData.canonicalUrl && seoData.canonicalUrl.startsWith('/')) checks.push('✅ Canonical URL format OK')
      else checks.push('❌ Invalid canonical URL format')

      checks.forEach(check => console.log(`   ${check}`))

    } else {
      console.error('❌ SEO Generation Failed:', result.error)
      console.error('📋 Message:', result.message)
      if (result.details) {
        console.error('📋 Details:', result.details)
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message)

    if (error.message.includes('fetch') || error.message.includes('ECONNREFUSED')) {
      console.log('\n💡 Make sure the development server is running:')
      console.log('   npm run dev')
      console.log('   Then visit: http://localhost:3000')
    } else if (error.message.includes('JSON')) {
      console.log('\n💡 The API returned invalid JSON. Check the server logs.')
    }
  }
}

// Test different products
const testProducts = [
  {
    productName: "3M H-700 Güvenlik Bareti",
    productDescription: "3M H-700 serisi güvenlik bareti, yüksek darbe dayanımı ve konfor sağlar. İnşaat, endüstri ve genel iş güvenliği için idealdir. UV koruması ve ayarlanabilir kafa bandı ile uzun süreli kullanım için tasarlanmıştır.",
    category: "Baş Koruma",
    brand: "3M",
    price: 65
  },
  {
    productName: "MSA V-Gard Endüstriyel Kask",
    productDescription: "MSA V-Gard endüstriyel güvenlik kaskı ile maksimum koruma. Ayarlanabilir askı sistemi, yüksek dayanıklılık ve konforlu kullanım. Profesyonel iş ortamları için ideal güvenlik çözümü.",
    category: "Baş Koruma", 
    brand: "MSA",
    price: 89
  },
  {
    productName: "Uvex Pheos B-WR Güvenlik Kaskı",
    productDescription: "Uvex Pheos B-WR güvenlik kaskı ile modern koruma. Havalandırma sistemi, ergonomik tasarım ve üstün koruma özellikleri. Uzun süreli konforlu kullanım için tasarlanmış.",
    category: "Baş Koruma",
    brand: "Uvex", 
    price: 125
  }
]

async function testMultipleProducts() {
  console.log('🧪 Testing Multiple Products...\n')
  
  for (let i = 0; i < testProducts.length; i++) {
    const product = testProducts[i]
    console.log(`\n📦 Test ${i + 1}/${testProducts.length}: ${product.productName}`)
    console.log('-'.repeat(60))
    
    try {
      const response = await fetch('http://localhost:3000/api/seo/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...product,
          storeName: "İş Güvenliği Mağazası"
        })
      })

      const result = await response.json()
      
      if (result.success) {
        const seo = result.data
        console.log(`✅ SEO Title: ${seo.seoTitle}`)
        console.log(`📝 SEO Description: ${seo.seoDescription}`)
        console.log(`🎯 Focus Keyword: ${seo.focusKeyword}`)
        console.log(`🔗 Canonical URL: ${seo.canonicalUrl}`)
      } else {
        console.log(`❌ Failed: ${result.error}`)
      }
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}`)
    }
    
    // Wait between requests
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  
  console.log('\n🎉 Multiple product test completed!')
}

// Run tests
if (process.argv.includes('--multiple')) {
  testMultipleProducts()
} else {
  testSEOGeneration()
}

console.log('\n📖 Usage:')
console.log('  node test-seo-generation.js          # Test single product')
console.log('  node test-seo-generation.js --multiple # Test multiple products')
console.log('\n🔧 Requirements:')
console.log('  - Development server running (npm run dev)')
console.log('  - OPENAI_API_KEY environment variable set')
console.log('  - Internet connection for LLM API calls')
