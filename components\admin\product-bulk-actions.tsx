"use client"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { useUpdateProductBulk, useDeleteProductBulk } from "@/lib/hooks/use-products"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { Input } from "@/components/ui/input"
import { useMainCategories } from "@/lib/hooks/use-categories"
import type { Category } from "@/types"

interface ProductBulkActionsProps {
  open: boolean
  onClose: () => void
  selectedProducts: string[] // Array of product IDs
  onSuccess: (message: string) => void
  onError: (message: string) => void
}

export function ProductBulkActions({ open, onClose, selectedProducts, onSuccess, onError }: ProductBulkActionsProps) {
  const [actionType, setActionType] = useState<string>("")
  const [statusValue, setStatusValue] = useState<boolean | null>(null)
  const [categoryValue, setCategoryValue] = useState<string | null>(null)
  const [priceAdjustmentType, setPriceAdjustmentType] = useState<"increase" | "decrease" | null>(null)
  const [priceAdjustmentValue, setPriceAdjustmentValue] = useState<number>(0)
  const [priceAdjustmentMethod, setPriceAdjustmentMethod] = useState<"percentage" | "fixed">("percentage")

  const { mutate: updateBulk, loading: updateLoading } = useUpdateProductBulk()
  const { mutate: deleteBulk, loading: deleteLoading } = useDeleteProductBulk()
  const { data: categoriesData, loading: categoriesLoading } = useMainCategories()
  const categories = categoriesData?.data || []

  const loading = updateLoading || deleteLoading

  const resetForm = () => {
    setActionType("")
    setStatusValue(null)
    setCategoryValue(null)
    setPriceAdjustmentType(null)
    setPriceAdjustmentValue(0)
    setPriceAdjustmentMethod("percentage")
    onClose()
  }

  const handleSubmit = async () => {
    if (selectedProducts.length === 0) {
      onError("Lütfen işlem yapmak için ürün seçin.")
      return
    }

    try {
      if (actionType === "status") {
        if (statusValue === null) {
          onError("Lütfen bir durum seçin (Aktif/Pasif).")
          return
        }
        await updateBulk({
          productIds: selectedProducts,
          data: { isActive: statusValue },
        })
        onSuccess(`${selectedProducts.length} ürünün durumu başarıyla güncellendi.`)
      } else if (actionType === "category") {
        if (!categoryValue) {
          onError("Lütfen bir kategori seçin.")
          return
        }
        await updateBulk({
          productIds: selectedProducts,
          data: { categoryId: categoryValue },
        })
        onSuccess(`${selectedProducts.length} ürünün kategorisi başarıyla güncellendi.`)
      } else if (actionType === "price") {
        if (priceAdjustmentValue === 0 || priceAdjustmentType === null) {
          onError("Lütfen fiyat ayarlaması için geçerli bir değer ve tür girin.")
          return
        }
        await updateBulk({
          productIds: selectedProducts,
          data: {
            priceAdjustment: {
              type: priceAdjustmentType,
              value: priceAdjustmentValue,
              method: priceAdjustmentMethod,
            },
          },
        })
        onSuccess(`${selectedProducts.length} ürünün fiyatı başarıyla güncellendi.`)
      } else if (actionType === "delete") {
        await deleteBulk(selectedProducts)
        onSuccess(`${selectedProducts.length} ürün başarıyla silindi.`)
      } else {
        onError("Lütfen bir toplu işlem türü seçin.")
        return
      }
      resetForm()
    } catch (err: any) {
      onError(err.message || "Toplu işlem sırasında bir hata oluştu.")
    }
  }

  return (
    <Dialog open={open} onOpenChange={resetForm}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Toplu İşlemler ({selectedProducts.length} ürün)</DialogTitle>
          <DialogDescription>Seçili ürünler üzerinde toplu işlem yapın.</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="actionType" className="text-right">
              İşlem Türü
            </Label>
            <Select value={actionType} onValueChange={setActionType}>
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="İşlem Seçin" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="status">Durum Güncelle</SelectItem>
                <SelectItem value="category">Kategori Değiştir</SelectItem>
                <SelectItem value="price">Fiyat Ayarla</SelectItem>
                <SelectItem value="delete" className="text-red-600">
                  Sil
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {actionType === "status" && (
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="statusValue" className="text-right">
                Durum
              </Label>
              <Select
                value={statusValue === null ? "" : String(statusValue)}
                onValueChange={(val) => setStatusValue(val === "true")}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Durum Seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="true">Aktif</SelectItem>
                  <SelectItem value="false">Pasif</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {actionType === "category" && (
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="categoryValue" className="text-right">
                Kategori
              </Label>
              <Select value={categoryValue || ""} onValueChange={setCategoryValue} disabled={categoriesLoading}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Kategori Seçin" />
                </SelectTrigger>
                <SelectContent>
                  {categoriesLoading ? (
                    <SelectItem value="loading" disabled>
                      Yükleniyor...
                    </SelectItem>
                  ) : categories.length === 0 ? (
                    <SelectItem value="no-categories" disabled>
                      Kategori bulunamadı
                    </SelectItem>
                  ) : (
                    categories.map((category: Category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
            </div>
          )}

          {actionType === "price" && (
            <>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="priceAdjustmentType" className="text-right">
                  Ayarlama Türü
                </Label>
                <Select
                  value={priceAdjustmentType || ""}
                  onValueChange={(val) => setPriceAdjustmentType(val as "increase" | "decrease")}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Seçiniz" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="increase">Artır</SelectItem>
                    <SelectItem value="decrease">Azalt</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="priceAdjustmentMethod" className="text-right">
                  Yöntem
                </Label>
                <Select
                  value={priceAdjustmentMethod}
                  onValueChange={(val) => setPriceAdjustmentMethod(val as "percentage" | "fixed")}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Seçiniz" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="percentage">Yüzde (%)</SelectItem>
                    <SelectItem value="fixed">Sabit Değer (TL)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="priceAdjustmentValue" className="text-right">
                  Değer
                </Label>
                <Input
                  id="priceAdjustmentValue"
                  type="number"
                  step="0.01"
                  value={priceAdjustmentValue}
                  onChange={(e) => setPriceAdjustmentValue(Number(e.target.value))}
                  className="col-span-3"
                />
              </div>
            </>
          )}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={resetForm} disabled={loading}>
            İptal
          </Button>
          <Button onClick={handleSubmit} disabled={loading || !actionType}>
            {loading && <LoadingSpinner size="sm" className="mr-2" />}
            Uygula
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
