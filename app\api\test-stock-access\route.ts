import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Testing stock field access...')

    // Test 1: Raw SQL query (should work)
    console.log('Test 1: Raw SQL query')
    const rawResult = await prisma.$queryRaw`
      SELECT 
        id,
        name,
        "stockQuantity",
        "minStockThreshold", 
        "maxStockCapacity",
        "basePrice"
      FROM "products" 
      LIMIT 1
    `
    console.log('✅ Raw SQL successful:', rawResult)

    // Test 2: Try Prisma client query (might fail)
    console.log('Test 2: Prisma client query')
    let prismaResult = null
    let prismaError = null
    
    try {
      prismaResult = await prisma.product.findFirst({
        select: {
          id: true,
          name: true,
          basePrice: true,
          trackStock: true
          // Intentionally not including stock fields to avoid error
        }
      })
      console.log('✅ Basic Prisma query successful:', prismaResult)
    } catch (error: any) {
      prismaError = error.message
      console.log('❌ Prisma query failed:', error.message)
    }

    // Test 3: Try to access stock fields via Prisma (will likely fail)
    console.log('Test 3: Prisma stock fields query')
    let stockResult = null
    let stockError = null
    
    try {
      stockResult = await prisma.product.findFirst({
        select: {
          id: true,
          name: true,
          stockQuantity: true,
          minStockThreshold: true,
          maxStockCapacity: true
        }
      })
      console.log('✅ Stock fields query successful:', stockResult)
    } catch (error: any) {
      stockError = error.message
      console.log('❌ Stock fields query failed:', error.message)
    }

    return NextResponse.json({
      success: true,
      tests: {
        rawSQL: {
          success: true,
          data: rawResult
        },
        basicPrisma: {
          success: !prismaError,
          data: prismaResult,
          error: prismaError
        },
        stockFields: {
          success: !stockError,
          data: stockResult,
          error: stockError
        }
      },
      diagnosis: stockError ? 
        'Database has stock fields but Prisma client needs regeneration' :
        'All tests passed - stock fields are accessible',
      nextSteps: stockError ? [
        'Run: npx prisma generate',
        'Restart development server',
        'Test admin panel'
      ] : [
        'Stock fields are working',
        'Test admin panel functionality'
      ]
    })

  } catch (error: any) {
    console.error('❌ Test failed:', error)
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 })
  }
}
