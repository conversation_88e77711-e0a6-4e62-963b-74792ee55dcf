import { prisma } from '@/lib/prisma'

// Types
export interface StockInfo {
  id: string
  stockQuantity: number
  minStockThreshold: number
  maxStockCapacity: number
  stockStatus: 'IN_STOCK' | 'LOW_STOCK' | 'OUT_OF_STOCK'
  trackStock: boolean
  reservedStock: number
  availableStock: number
  stockIndicators: {
    isLowStock: boolean
    isOutOfStock: boolean
    isOverCapacity: boolean
    stockPercentage: number | null
    daysUntilReorder: number | null
  }
}

export interface StockUpdateData {
  stockQuantity?: number
  minStockThreshold?: number
  maxStockCapacity?: number
  trackStock?: boolean
  stockLocation?: string
  reorderPoint?: number
  reorderQuantity?: number
}

/**
 * Unified Stock Service - Single source of truth for all stock operations
 */
export class UnifiedStockService {
  
  /**
   * Calculate stock status based on current stock and thresholds
   */
  static calculateStockStatus(
    stockQuantity: number, 
    minThreshold: number = 0,
    reservedStock: number = 0
  ): 'IN_STOCK' | 'LOW_STOCK' | 'OUT_OF_STOCK' {
    const availableStock = stockQuantity - reservedStock
    
    if (availableStock <= 0) {
      return 'OUT_OF_STOCK'
    } else if (availableStock <= minThreshold && minThreshold > 0) {
      return 'LOW_STOCK'
    } else {
      return 'IN_STOCK'
    }
  }

  /**
   * Calculate stock indicators for UI display
   */
  static calculateStockIndicators(
    stockQuantity: number,
    minThreshold: number = 0,
    maxCapacity: number = 0,
    reservedStock: number = 0
  ) {
    const availableStock = stockQuantity - reservedStock
    
    return {
      isLowStock: availableStock <= minThreshold && minThreshold > 0,
      isOutOfStock: availableStock <= 0,
      isOverCapacity: maxCapacity > 0 && stockQuantity > maxCapacity,
      stockPercentage: maxCapacity > 0 ? Math.round((stockQuantity / maxCapacity) * 100) : null,
      daysUntilReorder: null as number | null // TODO: Calculate based on usage history
    }
  }

  /**
   * Get comprehensive stock information for a single product
   */
  static async getProductStock(productId: string): Promise<StockInfo | null> {
    const product = await prisma.product.findUnique({
      where: { id: productId },
      select: {
        id: true,
        stockQuantity: true,
        minStockThreshold: true,
        maxStockCapacity: true,
        stockStatus: true,
        trackStock: true,
        reservedStock: true
      }
    })

    if (!product) return null

    const availableStock = product.stockQuantity - product.reservedStock
    const calculatedStatus = this.calculateStockStatus(
      product.stockQuantity, 
      product.minStockThreshold,
      product.reservedStock
    )
    
    const stockIndicators = this.calculateStockIndicators(
      product.stockQuantity,
      product.minStockThreshold,
      product.maxStockCapacity,
      product.reservedStock
    )

    return {
      id: product.id,
      stockQuantity: product.stockQuantity,
      minStockThreshold: product.minStockThreshold,
      maxStockCapacity: product.maxStockCapacity,
      stockStatus: calculatedStatus,
      trackStock: product.trackStock,
      reservedStock: product.reservedStock,
      availableStock,
      stockIndicators
    }
  }

  /**
   * Get stock information for multiple products
   */
  static async getMultipleProductsStock(productIds: string[]): Promise<Record<string, StockInfo>> {
    const products = await prisma.product.findMany({
      where: { id: { in: productIds } },
      select: {
        id: true,
        stockQuantity: true,
        minStockThreshold: true,
        maxStockCapacity: true,
        stockStatus: true,
        trackStock: true,
        reservedStock: true
      }
    })

    const stockInfoMap: Record<string, StockInfo> = {}

    for (const product of products) {
      const availableStock = product.stockQuantity - product.reservedStock
      const calculatedStatus = this.calculateStockStatus(
        product.stockQuantity, 
        product.minStockThreshold,
        product.reservedStock
      )
      
      const stockIndicators = this.calculateStockIndicators(
        product.stockQuantity,
        product.minStockThreshold,
        product.maxStockCapacity,
        product.reservedStock
      )

      stockInfoMap[product.id] = {
        id: product.id,
        stockQuantity: product.stockQuantity,
        minStockThreshold: product.minStockThreshold,
        maxStockCapacity: product.maxStockCapacity,
        stockStatus: calculatedStatus,
        trackStock: product.trackStock,
        reservedStock: product.reservedStock,
        availableStock,
        stockIndicators
      }
    }

    return stockInfoMap
  }

  /**
   * Update stock settings (not quantity - use stock movements for that)
   */
  static async updateStockSettings(
    productId: string, 
    updateData: StockUpdateData
  ): Promise<StockInfo | null> {
    // Remove stockQuantity from updateData if present (should use stock movements)
    const { stockQuantity, ...settingsData } = updateData

    const updatedProduct = await prisma.product.update({
      where: { id: productId },
      data: settingsData,
      select: {
        id: true,
        stockQuantity: true,
        minStockThreshold: true,
        maxStockCapacity: true,
        stockStatus: true,
        trackStock: true,
        reservedStock: true
      }
    })

    // Recalculate stock status after settings update
    const newStatus = this.calculateStockStatus(
      updatedProduct.stockQuantity,
      updatedProduct.minStockThreshold,
      updatedProduct.reservedStock
    )

    // Update stock status if it changed
    if (newStatus !== updatedProduct.stockStatus) {
      await prisma.product.update({
        where: { id: productId },
        data: { stockStatus: newStatus }
      })
    }

    return this.getProductStock(productId)
  }

  /**
   * Get stock status text for UI display
   */
  static getStockStatusText(status: string): string {
    switch (status) {
      case 'IN_STOCK':
        return 'Stokta Var'
      case 'LOW_STOCK':
        return 'Düşük Stok'
      case 'OUT_OF_STOCK':
        return 'Stokta Yok'
      default:
        return 'Bilinmiyor'
    }
  }

  /**
   * Get stock status color for UI display
   */
  static getStockStatusColor(status: string): string {
    switch (status) {
      case 'IN_STOCK':
        return 'text-green-600 bg-green-100'
      case 'LOW_STOCK':
        return 'text-orange-600 bg-orange-100'
      case 'OUT_OF_STOCK':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  /**
   * Validate stock operation with capacity checks
   */
  static validateStockOperation(
    currentStock: number,
    requestedQuantity: number,
    operation: 'add' | 'subtract' | 'set',
    maxCapacity?: number
  ): { isValid: boolean; error?: string; newStock?: number; availableCapacity?: number } {
    let newStock: number

    switch (operation) {
      case 'add':
        newStock = currentStock + requestedQuantity

        // Check capacity for add operations
        if (maxCapacity && maxCapacity > 0 && newStock > maxCapacity) {
          const availableCapacity = maxCapacity - currentStock
          return {
            isValid: false,
            error: `Kapasite aşımı. Mevcut: ${currentStock}, Eklenecek: ${requestedQuantity}, Maksimum: ${maxCapacity}. Eklenebilir maksimum: ${availableCapacity}`,
            availableCapacity
          }
        }
        break
      case 'subtract':
        newStock = currentStock - requestedQuantity
        if (newStock < 0) {
          return {
            isValid: false,
            error: `Yetersiz stok. Mevcut: ${currentStock}, İstenen: ${requestedQuantity}`
          }
        }
        break
      case 'set':
        newStock = requestedQuantity
        if (newStock < 0) {
          return {
            isValid: false,
            error: 'Stok miktarı negatif olamaz'
          }
        }

        // Check capacity for set operations
        if (maxCapacity && maxCapacity > 0 && newStock > maxCapacity) {
          return {
            isValid: false,
            error: `Kapasite aşımı. Ayarlanacak: ${newStock}, Maksimum: ${maxCapacity}`
          }
        }
        break
      default:
        return {
          isValid: false,
          error: 'Geçersiz işlem tipi'
        }
    }

    return {
      isValid: true,
      newStock,
      availableCapacity: maxCapacity && maxCapacity > 0 ? maxCapacity - newStock : undefined
    }
  }

  /**
   * Get available capacity for stock additions
   */
  static getAvailableCapacity(currentStock: number, maxCapacity?: number): number {
    if (!maxCapacity || maxCapacity <= 0) {
      return Infinity // No capacity limit
    }
    return Math.max(0, maxCapacity - currentStock)
  }
}
