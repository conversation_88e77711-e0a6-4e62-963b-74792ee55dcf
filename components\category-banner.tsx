"use client"

import { CategoryBannerCarousel } from "@/components/category-banner-carousel"
import type { Category } from "@/types"

interface CategoryBannerProps {
  category: Category
  height?: "sm" | "md" | "lg" | "xl"
  showPlayPause?: boolean
  autoplay?: boolean
  className?: string
}

export function CategoryBanner({
  category,
  height = "lg",
  showPlayPause = false,
  autoplay = true,
  className
}: CategoryBannerProps) {
  return (
    <CategoryBannerCarousel
      category={category}
      height={height}
      showPlayPause={showPlayPause}
      autoplay={autoplay}
      className={className}
    />
  )
}

// Kompakt banner versiyonu (daha küçük sayfalar için)
export function CategoryBannerCompact({ category, className }: CategoryBannerProps) {
  return (
    <CategoryBannerCarousel
      category={category}
      height="sm"
      showPlayPause={false}
      autoplay={true}
      className={className}
    />
  )
}
