"use client"

import { useSortable } from "@dnd-kit/sortable"
import { CSS } from "@dnd-kit/utilities"
import { GripVertical, Edit, Trash2, Eye, EyeOff } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { formatDate } from "@/lib/utils"
import type { Category } from "@/types"

interface SortableCategoryItemProps {
  category: Category
  index: number
  onEdit: (category: Category) => void
  onDelete: (category: Category) => void
}

export function SortableCategoryItem({ category, index, onEdit, onDelete }: SortableCategoryItemProps) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: category.id })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  }

  return (
    <Card
      ref={setNodeRef}
      style={style}
      className={`transition-all duration-200 ${
        isDragging ? "shadow-lg scale-105 rotate-2 z-50 bg-orange-50 border-orange-200" : "hover:shadow-md"
      }`}
    >
      <CardContent className="p-4">
        <div className="flex items-center gap-4">
          {/* Drag Handle */}
          <div
            {...attributes}
            {...listeners}
            className={`cursor-grab active:cursor-grabbing p-2 rounded-lg transition-colors ${
              isDragging ? "bg-orange-100" : "hover:bg-gray-100"
            }`}
          >
            <GripVertical className="h-5 w-5 text-gray-400" />
          </div>

          {/* Order Number */}
          <div className="flex-shrink-0">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                isDragging ? "bg-orange-500 text-white" : "bg-gray-100 text-gray-600"
              }`}
            >
              {index + 1}
            </div>
          </div>

          {/* Category Icon */}
          <div className="flex-shrink-0">
            <div className="bg-orange-50 p-3 rounded-lg">
              <div className="w-6 h-6 bg-orange-500 rounded"></div>
            </div>
          </div>

          {/* Category Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold text-gray-900 truncate">{category.name}</h3>
              <Badge variant={category.isActive ? "success" : "secondary"}>
                {category.isActive ? <Eye className="h-3 w-3 mr-1" /> : <EyeOff className="h-3 w-3 mr-1" />}
                {category.isActive ? "Aktif" : "Pasif"}
              </Badge>
            </div>
            <p className="text-sm text-gray-600 truncate mb-1">{category.description}</p>
            <div className="flex items-center gap-4 text-xs text-gray-500">
              <span>{category.productCount} ürün</span>
              <span>Sıra: {category.sortOrder}</span>
              <span>{formatDate(category.createdAt)}</span>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onEdit(category)}
              className="hover:bg-blue-50 hover:text-blue-600"
            >
              <Edit className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => onDelete(category)}
              className="hover:bg-red-50 hover:text-red-600"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
