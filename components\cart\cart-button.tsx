"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useCartStore } from "@/lib/stores/cart-store"
import { ShoppingCart } from "lucide-react"

export function CartButton() {
  const { getTotalItems, setIsOpen } = useCartStore()
  const totalItems = getTotalItems()

  return (
    <Button variant="ghost" size="icon" onClick={() => setIsOpen(true)} className="relative">
      <ShoppingCart className="w-5 h-5" />
      {totalItems > 0 && (
        <Badge className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 bg-blue-600 hover:bg-blue-700">
          {totalItems > 99 ? "99+" : totalItems}
        </Badge>
      )}
    </Button>
  )
}
