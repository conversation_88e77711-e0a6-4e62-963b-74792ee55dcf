# SEO Generation Service Guide

## Overview

The SEO Generation Service uses Large Language Models (LLM) to automatically generate comprehensive, professional SEO content for Turkish e-commerce products. This service creates optimized content for search engines, social media platforms, and structured data markup.

## Features

### 🤖 AI-Powered SEO Generation
- **LLM Integration**: Uses OpenAI GPT-4 or compatible models
- **Turkish Optimization**: Specialized for Turkish market and language
- **Comprehensive Coverage**: Generates all SEO fields in one request
- **Fallback System**: Local generation if LLM service fails

### 📊 Generated SEO Fields

#### Basic SEO
- **SEO Title** (50-60 characters): Optimized for search results
- **SEO Description** (120-160 characters): Compelling meta descriptions
- **Meta Keywords**: Relevant Turkish keywords (max 10)
- **Focus Keyword**: Primary target keyword
- **Canonical URL**: SEO-friendly URL slug

#### Social Media Optimization
- **Open Graph**: Facebook sharing optimization
- **Twitter Card**: Twitter sharing optimization
- **Social Titles/Descriptions**: Platform-specific content

#### Technical SEO
- **Robots Directive**: Search engine crawling instructions
- **Alternative Text**: Image accessibility descriptions
- **Breadcrumbs**: Navigation path structure

#### Advanced SEO
- **Structured Data**: Schema.org Product markup
- **JSON-LD Schema**: Rich snippets for search engines
- **Internal/External Links**: SEO link strategy

## Setup

### 1. Environment Configuration

Add to your `.env` file:

```env
# LLM API Configuration
OPENAI_API_KEY="your-openai-api-key-here"
LLM_ENDPOINT="https://api.openai.com/v1/chat/completions"

# SEO Generation Settings
SEO_GENERATION_ENABLED="true"
SEO_FALLBACK_TO_LOCAL="true"
SEO_CACHE_DURATION="3600"
```

### 2. API Key Setup

#### OpenAI (Recommended)
1. Visit [OpenAI API](https://platform.openai.com/api-keys)
2. Create a new API key
3. Add to environment variables

#### Alternative Providers
- **Anthropic Claude**: Set `ANTHROPIC_API_KEY`
- **Google AI**: Set `GOOGLE_AI_API_KEY`
- **Local LLM**: Use Ollama or similar

## Usage

### 1. Admin Panel Integration

The SEO generation is integrated into the Product Form Modal:

1. **Navigate**: Go to Admin Panel → Products → Add Product
2. **Fill Basic Info**: Enter product name and description
3. **Go to SEO Tab**: Click on the "SEO" tab
4. **Auto Generate**: Click "Otomatik SEO Değerleri Oluştur"
5. **Review & Edit**: Review generated content and make adjustments

### 2. API Usage

#### Generate SEO Data

```javascript
const response = await fetch('/api/seo/generate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    productName: "3M H-700 Güvenlik Bareti",
    productDescription: "3M H-700 serisi güvenlik bareti...",
    category: "Baş Koruma",
    brand: "3M",
    price: 65,
    storeName: "İş Güvenliği Mağazası"
  })
})

const result = await response.json()
```

#### Response Format

```json
{
  "success": true,
  "data": {
    "seoTitle": "3M H-700 Güvenlik Bareti - UV Korumalı - 65 TL",
    "seoDescription": "3M H-700 güvenlik bareti ile başınızı koruyun. Yüksek darbe dayanımı, UV koruması ve konforlu kullanım.",
    "metaKeywords": ["3m güvenlik bareti", "h-700 kask", "iş güvenliği"],
    "focusKeyword": "3m h-700 güvenlik bareti",
    "canonicalUrl": "/urun/3m-h700-guvenlik-bareti",
    "robotsDirective": "index,follow",
    "ogTitle": "3M H-700 Güvenlik Bareti - UV Korumalı",
    "ogDescription": "3M H-700 güvenlik bareti ile başınızı koruyun...",
    "ogType": "product",
    "twitterCard": "summary_large_image",
    "twitterTitle": "3M H-700 Güvenlik Bareti",
    "twitterDescription": "3M kalitesi ile güvenlik bareti...",
    "alternativeText": "3M H-700 Güvenlik Bareti - Beyaz renk",
    "breadcrumbs": ["Ana Sayfa", "Baş Koruma", "3M H-700 Güvenlik Bareti"],
    "structuredData": {
      "@context": "https://schema.org/",
      "@type": "Product",
      "name": "3M H-700 Güvenlik Bareti",
      "brand": {
        "@type": "Brand",
        "name": "3M"
      },
      "offers": {
        "@type": "Offer",
        "price": "65.00",
        "priceCurrency": "TRY",
        "availability": "https://schema.org/InStock"
      }
    },
    "schemaMarkup": "{\"@context\":\"https://schema.org/\"...}"
  }
}
```

### 3. Programmatic Usage

```typescript
import { createSEOGenerationService } from '@/lib/services/seo-generation-service'

const seoService = createSEOGenerationService({
  llmApiKey: process.env.OPENAI_API_KEY,
  llmEndpoint: 'https://api.openai.com/v1/chat/completions'
})

const seoData = await seoService.generateSEO({
  productName: "Product Name",
  productDescription: "Product description...",
  category: "Category",
  brand: "Brand",
  price: 100
})
```

## Testing

### 1. Test Script

Run the included test script:

```bash
# Test single product
node test-seo-generation.js

# Test multiple products
node test-seo-generation.js --multiple
```

### 2. API Testing

Test the API endpoint:

```bash
curl -X POST http://localhost:3000/api/seo/generate \
  -H "Content-Type: application/json" \
  -d '{
    "productName": "Test Product",
    "productDescription": "Test description...",
    "category": "Test Category",
    "brand": "Test Brand",
    "price": 100
  }'
```

## Turkish SEO Optimization

### Language Features
- **Turkish Characters**: Proper handling of ç, ğ, ı, ö, ş, ü
- **Grammar Rules**: Turkish word order and grammar
- **Local Terms**: Turkish-specific search terms
- **Cultural Context**: Turkish shopping behaviors

### Market Optimization
- **Search Engines**: Google.com.tr, Yandex.com.tr optimization
- **Keywords**: Turkish safety equipment terminology
- **Pricing**: Turkish Lira (TRY) currency
- **Trust Signals**: Turkish market trust factors

### Content Strategy
- **Natural Language**: Conversational Turkish
- **Local SEO**: Turkey-specific optimization
- **Mobile-First**: High mobile usage in Turkey
- **Value Proposition**: Price sensitivity considerations

## Best Practices

### 1. Content Quality
- Review generated content for accuracy
- Ensure brand consistency
- Verify technical specifications
- Check pricing information

### 2. SEO Optimization
- Monitor character limits (title: 60, description: 160)
- Ensure keyword density is natural (1-3%)
- Verify structured data validity
- Test social media previews

### 3. Performance
- Cache generated content when possible
- Use fallback for critical operations
- Monitor API usage and costs
- Implement rate limiting

## Troubleshooting

### Common Issues

#### 1. API Key Issues
```
Error: LLM API error: 401 Unauthorized
```
**Solution**: Check your API key in environment variables

#### 2. Rate Limiting
```
Error: LLM API error: 429 Too Many Requests
```
**Solution**: Implement request throttling or upgrade API plan

#### 3. Invalid JSON Response
```
Error: Invalid JSON response from LLM
```
**Solution**: Check prompt template and model configuration

#### 4. Fallback Activation
```
Warning: LLM SEO generation failed, falling back to local generation
```
**Solution**: Check network connectivity and API status

### Debug Mode

Enable debug logging:

```env
NODE_ENV="development"
DEBUG="seo-generation:*"
```

## Cost Optimization

### 1. Caching Strategy
- Cache generated SEO data for 1 hour
- Use Redis for distributed caching
- Implement cache invalidation

### 2. Request Optimization
- Batch multiple products when possible
- Use shorter prompts for simple products
- Implement smart fallbacks

### 3. Model Selection
- Use GPT-3.5-turbo for cost efficiency
- Use GPT-4 for complex products
- Consider local models for high volume

## Security

### 1. API Key Protection
- Never commit API keys to version control
- Use environment variables
- Rotate keys regularly

### 2. Input Validation
- Validate all input parameters
- Sanitize user-provided content
- Implement rate limiting

### 3. Output Validation
- Validate generated JSON structure
- Check content for inappropriate material
- Verify URL safety

## Support

For issues and questions:
- Check the troubleshooting section
- Review API documentation
- Contact development team
- Submit GitHub issues

## License

This SEO Generation Service is part of the İş Güvenliği E-ticaret project and follows the same license terms.
