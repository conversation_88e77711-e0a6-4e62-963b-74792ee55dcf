import { NextRequest, NextResponse } from 'next/server'
import { exec } from 'child_process'
import { promisify } from 'util'

const execAsync = promisify(exec)

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 Force generating Prisma client...')

    // Try multiple approaches to generate Prisma client
    let success = false
    let result = null

    // Approach 1: Standard generate
    try {
      console.log('Trying standard generate...')
      const { stdout, stderr } = await execAsync('npx prisma generate --force-reset', {
        cwd: process.cwd(),
        timeout: 60000,
        env: {
          ...process.env,
          PRISMA_GENERATE_SKIP_AUTOINSTALL: 'true'
        }
      })
      
      console.log('✅ Standard generate successful!')
      console.log('STDOUT:', stdout)
      if (stderr) console.log('STDERR:', stderr)
      success = true
      result = { stdout, stderr }
    } catch (error1) {
      console.log('❌ Standard generate failed:', error1.message)
      
      // Approach 2: Try with different flags
      try {
        console.log('Trying with different flags...')
        const { stdout, stderr } = await execAsync('npx prisma generate', {
          cwd: process.cwd(),
          timeout: 30000
        })
        
        console.log('✅ Alternative generate successful!')
        success = true
        result = { stdout, stderr }
      } catch (error2) {
        console.log('❌ Alternative generate failed:', error2.message)
      }
    }

    if (success) {
      // Test the new fields
      console.log('🔍 Testing stock field access...')
      
      try {
        // Clear require cache and import fresh Prisma client
        const prismaPath = require.resolve('@/lib/prisma')
        delete require.cache[prismaPath]
        
        const { prisma } = require('@/lib/prisma')
        
        const testProduct = await prisma.product.findFirst({
          select: {
            id: true,
            name: true,
            stockQuantity: true,
            minStockThreshold: true,
            maxStockCapacity: true,
            trackStock: true,
            basePrice: true
          }
        })

        console.log('✅ Stock fields are now accessible!')
        console.log('Test product:', testProduct)

        return NextResponse.json({
          success: true,
          message: 'Prisma client regenerated and stock fields are working!',
          testProduct: testProduct,
          generateResult: result,
          nextSteps: [
            'Stock fields are now accessible via Prisma client',
            'Admin panel should work correctly',
            'You can now edit products and see stock fields'
          ]
        })
        
      } catch (testError) {
        console.log('❌ Stock field test failed:', testError.message)
        
        return NextResponse.json({
          success: true,
          message: 'Prisma client regenerated but stock fields still not accessible',
          generateResult: result,
          testError: testError.message,
          solution: 'Please restart the development server manually'
        })
      }
    } else {
      return NextResponse.json({
        success: false,
        error: 'All Prisma generate attempts failed',
        solution: {
          message: 'Manual intervention required',
          steps: [
            'Stop the development server (Ctrl+C)',
            'Run: npx prisma generate',
            'Run: npm run dev',
            'The database migration was successful, only client generation is needed'
          ]
        }
      }, { status: 500 })
    }

  } catch (error: any) {
    console.error('❌ Force generate failed:', error)
    return NextResponse.json({
      success: false,
      error: error.message,
      solution: 'Please restart the development server manually'
    }, { status: 500 })
  }
}
