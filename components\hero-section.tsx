import { Button } from "@/components/ui/button"
import { ArrowRight, Shield, Truck, Award } from "lucide-react"

export function HeroSection() {
  return (
    <section className="bg-gradient-to-r from-slate-900 via-slate-800 to-orange-900 text-white">
      <div className="container mx-auto px-4 py-16 md:py-24">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div>
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              İş Güvenliği
              <span className="text-orange-400 block">Malzemelerinde</span>
              Türkiye'nin Lideri
            </h1>
            <p className="text-xl mb-8 text-slate-300 leading-relaxed">
              25 yıllık deneyimimizle, iş güvenliği malzemelerinin en kaliteli ve uygun fiyatlı çözümlerini sunuyoruz.
              <PERSON><PERSON><PERSON> ürün, hızlı teslimat.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 mb-12">
              <Button size="lg" className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-4 text-lg">
                Ürünleri İncele
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-slate-900 px-8 py-4 text-lg bg-transparent"
              >
                Kurumsal Teklif Al
              </Button>
            </div>

            <div className="grid grid-cols-3 gap-6">
              <div className="text-center">
                <Shield className="h-8 w-8 text-orange-400 mx-auto mb-2" />
                <div className="text-sm text-slate-300">Sertifikalı</div>
                <div className="font-semibold">Ürünler</div>
              </div>
              <div className="text-center">
                <Truck className="h-8 w-8 text-orange-400 mx-auto mb-2" />
                <div className="text-sm text-slate-300">Hızlı</div>
                <div className="font-semibold">Teslimat</div>
              </div>
              <div className="text-center">
                <Award className="h-8 w-8 text-orange-400 mx-auto mb-2" />
                <div className="text-sm text-slate-300">25 Yıl</div>
                <div className="font-semibold">Deneyim</div>
              </div>
            </div>
          </div>

          <div className="relative">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
              <h3 className="text-2xl font-bold mb-6 text-center">Özel Kampanya</h3>
              <div className="text-center mb-6">
                <div className="text-4xl font-bold text-orange-400 mb-2">%30 İndirim</div>
                <div className="text-lg">Tüm güvenlik ayakkabılarında</div>
              </div>
              <div className="space-y-3 mb-6">
                <div className="flex justify-between">
                  <span>Minimum Sipariş:</span>
                  <span className="font-semibold">500₺</span>
                </div>
                <div className="flex justify-between">
                  <span>Ücretsiz Kargo:</span>
                  <span className="font-semibold">✓</span>
                </div>
                <div className="flex justify-between">
                  <span>Geçerlilik:</span>
                  <span className="font-semibold">31 Ocak'a kadar</span>
                </div>
              </div>
              <Button className="w-full bg-orange-500 hover:bg-orange-600">Kampanyayı Gör</Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
