import { withAuth } from 'next-auth/middleware'
import { NextResponse } from 'next/server'

// ⚠️ DEVELOPMENT ONLY: Skip authentication in development mode
// This is for development convenience only and should NEVER be used in production
const SKIP_AUTH_IN_DEV = process.env.NODE_ENV === 'development' && process.env.SKIP_AUTH === 'true'

export default withAuth(
  function middleware(req) {
    // 🚨 DEVELOPMENT BYPASS: Skip all authentication checks in development mode
    if (SKIP_AUTH_IN_DEV) {
      console.log('🔓 [DEV ONLY] Authentication bypassed for:', req.nextUrl.pathname)
      return NextResponse.next()
    }

    // Check if user is trying to access admin routes
    if (req.nextUrl.pathname.startsWith('/admin')) {
      // Allow access to login page
      if (req.nextUrl.pathname === '/admin/login') {
        return NextResponse.next()
      }

      // Check if user is authenticated
      if (!req.nextauth.token) {
        return NextResponse.redirect(new URL('/admin/login', req.url))
      }

      // Check if user has admin role
      const userRole = req.nextauth.token.role
      if (userRole !== 'ADMIN' && userRole !== 'SUPER_ADMIN') {
        return NextResponse.redirect(new URL('/admin/login', req.url))
      }
    }

    // Check API routes that require admin access
    if (req.nextUrl.pathname.startsWith('/api/')) {
      // Skip auth routes
      if (req.nextUrl.pathname.startsWith('/api/auth/')) {
        return NextResponse.next()
      }

      // Skip public API routes (if any)
      const publicRoutes = ['/api/products', '/api/categories']
      const isPublicRoute = publicRoutes.some(route =>
        req.nextUrl.pathname.startsWith(route) && req.method === 'GET'
      )

      if (isPublicRoute) {
        return NextResponse.next()
      }

      // 🚨 DEVELOPMENT BYPASS: Skip API authentication in development mode
      if (SKIP_AUTH_IN_DEV) {
        return NextResponse.next()
      }

      // For other API routes, require authentication
      if (!req.nextauth.token) {
        return NextResponse.json(
          { error: 'Unauthorized' },
          { status: 401 }
        )
      }

      // Check admin role for write operations
      const writeOperations = ['POST', 'PUT', 'DELETE', 'PATCH']
      if (writeOperations.includes(req.method)) {
        const userRole = req.nextauth.token.role
        if (userRole !== 'ADMIN' && userRole !== 'SUPER_ADMIN') {
          return NextResponse.json(
            { error: 'Forbidden: Admin access required' },
            { status: 403 }
          )
        }
      }
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // 🚨 DEVELOPMENT BYPASS: Always allow access in development mode when auth is skipped
        if (SKIP_AUTH_IN_DEV) {
          return true
        }

        // Allow access to public routes
        if (!req.nextUrl.pathname.startsWith('/admin') &&
            !req.nextUrl.pathname.startsWith('/api/')) {
          return true
        }

        // For admin routes and API routes, check in the main middleware function
        return true
      },
    },
  }
)

export const config = {
  matcher: [
    '/admin/:path*',
    '/api/:path*'
  ]
}
