import { Suspense } from "react"
import { getCategories } from "@/lib/api/categories"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Skeleton } from "@/components/ui/skeleton"
import { formatCurrency } from "@/lib/utils"

interface ProductFiltersProps {
  searchParams: {
    search?: string
    category?: string
    brand?: string
    minPrice?: string
    maxPrice?: string
    inStock?: string
    sort?: string
    page?: string
  }
}

async function FilterContent({ searchParams }: ProductFiltersProps) {
  try {
    // Kategorileri ve markaları paralel olarak çek
    const [categoriesResponse, brandsResponse] = await Promise.all([
      getCategories({ isActive: true, parentId: "", limit: 50 }),
      fetch("/api/products/brands").then((res) => res.json()),
    ])

    const categories = categoriesResponse.data || []
    const brands = brandsResponse.data || []

    // Fiyat aralığını belirle
    const minPrice = Number.parseFloat(searchParams.minPrice || "0")
    const maxPrice = Number.parseFloat(searchParams.maxPrice || "10000")

    return (
      <div className="space-y-6">
        {/* Kategoriler */}
        <div>
          <h3 className="font-medium text-gray-900 mb-3">Kategoriler</h3>
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {categories.map((category) => (
              <div key={category.id} className="flex items-center space-x-2">
                <Checkbox id={`category-${category.id}`} checked={searchParams.category === category.id} />
                <Label htmlFor={`category-${category.id}`} className="text-sm text-gray-700 cursor-pointer">
                  {category.name}
                  {category.productCount > 0 && <span className="text-gray-500 ml-1">({category.productCount})</span>}
                </Label>
              </div>
            ))}
          </div>
        </div>

        {/* Markalar */}
        {brands.length > 0 && (
          <div>
            <h3 className="font-medium text-gray-900 mb-3">Markalar</h3>
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {brands.map((brand: string) => (
                <div key={brand} className="flex items-center space-x-2">
                  <Checkbox id={`brand-${brand}`} checked={searchParams.brand === brand} />
                  <Label htmlFor={`brand-${brand}`} className="text-sm text-gray-700 cursor-pointer">
                    {brand}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Fiyat Aralığı */}
        <div>
          <h3 className="font-medium text-gray-900 mb-3">Fiyat Aralığı</h3>
          <div className="space-y-4">
            <Slider value={[minPrice, maxPrice]} max={10000} min={0} step={50} className="w-full" />
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>{formatCurrency(minPrice)}</span>
              <span>{formatCurrency(maxPrice)}</span>
            </div>
          </div>
        </div>

        {/* Stok Durumu */}
        <div>
          <h3 className="font-medium text-gray-900 mb-3">Stok Durumu</h3>
          <div className="flex items-center space-x-2">
            <Checkbox id="inStock" checked={searchParams.inStock === "true"} />
            <Label htmlFor="inStock" className="text-sm text-gray-700 cursor-pointer">
              Sadece stokta olanlar
            </Label>
          </div>
        </div>
      </div>
    )
  } catch (error) {
    console.error("Filter content error:", error)
    return (
      <div className="text-center py-4">
        <p className="text-sm text-gray-500">Filtreler yüklenemedi</p>
      </div>
    )
  }
}

export default function ProductFilters({ searchParams }: ProductFiltersProps) {
  return (
    <Suspense
      fallback={
        <div className="space-y-6">
          <div>
            <Skeleton className="h-5 w-20 mb-3" />
            <div className="space-y-2">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex items-center space-x-2">
                  <Skeleton className="h-4 w-4" />
                  <Skeleton className="h-4 w-24" />
                </div>
              ))}
            </div>
          </div>
          <div>
            <Skeleton className="h-5 w-16 mb-3" />
            <div className="space-y-2">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="flex items-center space-x-2">
                  <Skeleton className="h-4 w-4" />
                  <Skeleton className="h-4 w-20" />
                </div>
              ))}
            </div>
          </div>
          <div>
            <Skeleton className="h-5 w-24 mb-3" />
            <Skeleton className="h-6 w-full" />
          </div>
        </div>
      }
    >
      <FilterContent searchParams={searchParams} />
    </Suspense>
  )
}
