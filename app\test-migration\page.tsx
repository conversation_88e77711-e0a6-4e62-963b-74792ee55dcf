'use client'

import { useState } from 'react'

export default function TestMigrationPage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const applyMigration = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/fix-stock-fields', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({ success: false, error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const testDebugStock = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/debug-stock')
      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({ success: false, error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const generatePrisma = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/generate-prisma', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({ success: false, error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const formatPrisma = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/format-prisma', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({ success: false, error: error.message })
    } finally {
      setLoading(false)
    }
  }

  const restartServer = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/restart-server', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({ success: false, error: error.message })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Stock Management Migration Test</h1>
      
      <div className="space-y-4 mb-8">
        <button
          onClick={applyMigration}
          disabled={loading}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loading ? 'Adding Fields...' : 'Add Stock Fields'}
        </button>
        
        <button
          onClick={testDebugStock}
          disabled={loading}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50 ml-4"
        >
          {loading ? 'Testing...' : 'Test Stock Fields'}
        </button>

        <button
          onClick={generatePrisma}
          disabled={loading}
          className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 disabled:opacity-50 ml-4"
        >
          {loading ? 'Generating...' : 'Generate Prisma Client'}
        </button>

        <button
          onClick={formatPrisma}
          disabled={loading}
          className="bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600 disabled:opacity-50 ml-4"
        >
          {loading ? 'Formatting...' : 'Format & Generate Prisma'}
        </button>

        <button
          onClick={restartServer}
          disabled={loading}
          className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 disabled:opacity-50 ml-4"
        >
          {loading ? 'Restarting...' : 'Fix Prisma Client'}
        </button>
      </div>

      {result && (
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Result:</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  )
}
