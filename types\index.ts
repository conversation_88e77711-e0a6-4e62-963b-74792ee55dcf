// Enum'ları import et
import {
  ApprovalStatus,
  IconType,
  MobileTemplate,
  TransitionType,
  DeviceType
} from '../lib/enums'

// Temel kategori interface'i
export interface Category {
  id: string
  name: string
  slug: string
  description: string
  icon: string
  parentId?: string | null
  children?: Category[]
  productCount: number
  isActive: boolean
  sortOrder: number
  seoTitle?: string
  seoDescription?: string

  // 1. Performans optimizasyonu
  cacheKey?: string
  viewCount: number
  popularityScore: number

  // 2. Indexing & Search
  searchKeywords?: string
  isSearchable: boolean

  // 4. Görsel & UI/UX
  categoryImage?: string
  colorCode?: string
  iconType?: IconType

  // 5. Advanced SEO
  metaKeywords?: string
  ogTitle?: string
  ogDescription?: string
  ogImage?: string

  // 6. Analytics & Tracking
  conversionRate?: number
  avgOrderValue?: number

  // 7. Business Rules
  minOrderAmount?: number
  commissionRate?: number
  taxRate?: number

  // 8. Inventory & Stock alanları kaldırıldı - stok yönetimi Product seviyesinde yapılacak

  // 9. Admin Features
  isPromoted: boolean
  isFeatured: boolean
  adminNotes?: string
  approvalStatus: ApprovalStatus

  // 10. Audit & History
  version: number
  changeLog?: any // JSON
  createdBy?: string

  // 11. Mobile Optimization
  mobileIcon?: string
  mobileTemplate?: MobileTemplate

  createdAt: Date
  updatedAt: Date
}

// CategoryBanner interface'i - Çoklu banner sistemi
export interface CategoryBanner {
  id: string
  categoryId: string
  category?: Category

  // Görsel ve İçerik
  imageUrl: string
  imageAlt?: string
  mobileImageUrl?: string
  title?: string
  subtitle?: string
  description?: string

  // Call-to-Action
  ctaText?: string
  ctaUrl?: string

  // Görüntüleme Ayarları
  displayOrder: number
  isActive: boolean
  startDate?: Date
  endDate?: Date
  displayDuration: number // saniye cinsinden

  // Stil ve Animasyon
  transitionType: TransitionType
  backgroundColor?: string
  textColor?: string

  // Analytics ve Tracking
  clickCount: number
  impressionCount: number
  ctr: number
  priority: number

  // Hedefleme
  targetAudience?: string // JSON format
  deviceType: DeviceType[]
  geoLocation?: string
  seasonalTags?: string

  // Business
  conversionGoal?: string
  budgetAllocation?: number

  // Audit
  createdAt: Date
  updatedAt: Date
  createdBy?: string
}

// Ürün interface'i - Updated for variant support
export interface Product {
  id: string
  name: string
  slug: string
  description: string
  shortDescription: string
  categoryId: string
  category: Category
  brand: string
  model?: string

  // Simplified pricing structure (can be overridden by variants)
  baseCostPrice?: number // Alış fiyatı (purchase/cost price)
  basePrice: number // Normal satış fiyatı (base selling price)
  zakatAmount?: number // Zakat amount (zekat tutarı)
  taxRate: number
  currency: string

  // Calculated pricing (computed from basePrice + active discounts)
  currentPrice?: number // Güncel satış fiyatı (with discounts applied)
  activeDiscountAmount?: number // Aktif indirim tutarı
  activeDiscountPercentage?: number // Aktif indirim yüzdesi

  // Product-level settings
  hasVariants: boolean
  trackStock: boolean

  // Enhanced Stock Management
  stockQuantity?: number // Current stock quantity
  minStockThreshold?: number // Minimum stock threshold for low stock alerts
  maxStockCapacity?: number // Maximum stock capacity
  stockLocation?: string // Warehouse/location information
  reorderPoint?: number // Reorder point for automatic restocking
  reorderQuantity?: number // Quantity to reorder when stock is low
  stockStatus?: "IN_STOCK" | "OUT_OF_STOCK" | "LOW_STOCK" // Calculated stock status

  // Resim ve medya
  images: ProductImage[]
  videos?: ProductVideo[]

  // Teknik özellikler
  specifications: ProductSpecification[]

  // Sertifikalar
  certificates: ProductCertificate[]

  // Variants and attributes
  variants?: ProductVariant[]
  attributes?: ProductAttribute[]

  // SEO ve meta
  seoTitle?: string
  seoDescription?: string
  metaKeywords?: string[]

  // Durum ve görünürlük
  isActive: boolean
  isFeatured: boolean
  isNew: boolean
  isOnSale: boolean

  // Ölçüler ve ağırlık (base values)
  weight?: number
  dimensions?: ProductDimensions

  // Tarihler
  createdAt: Date
  updatedAt: Date
  publishedAt?: Date
}

// Product Variant interface
export interface ProductVariant {
  id: string
  productId: string

  // Variant identification
  sku: string
  barcode?: string

  // Variant-specific pricing
  price: number
  originalPrice?: number
  costPrice?: number

  // Variant-specific stock
  stock: number
  minStock: number
  stockStatus: "IN_STOCK" | "OUT_OF_STOCK" | "LOW_STOCK"

  // Variant attributes
  attributes: Record<string, any> // e.g., {"color": "red", "size": "XL"}

  // Variant-specific properties
  weight?: number
  dimensions?: ProductDimensions

  // Status
  isActive: boolean
  isDefault: boolean

  // Relations
  images?: ProductVariantImage[]

  // Timestamps
  createdAt: Date
  updatedAt: Date
}

// Product Attribute interface
export interface ProductAttribute {
  id: string
  productId: string
  name: string
  type: ProductAttributeType
  isRequired: boolean
  sortOrder: number
  options?: ProductAttributeOption[]
  createdAt: Date
  updatedAt: Date
}

// Product Attribute Option interface
export interface ProductAttributeOption {
  id: string
  attributeId: string
  value: string
  displayName: string
  colorCode?: string
  sortOrder: number
  isActive: boolean
  createdAt: Date
  updatedAt: Date
}

// Product Variant Image interface
export interface ProductVariantImage {
  id: string
  variantId: string
  url: string
  alt: string
  title?: string
  sortOrder: number
  isMain: boolean
  size: number
  width: number
  height: number
  format: string
}

// Product Attribute Type enum
export enum ProductAttributeType {
  COLOR = "COLOR",
  SIZE = "SIZE",
  TEXT = "TEXT",
  NUMBER = "NUMBER",
  BOOLEAN = "BOOLEAN"
}

// Discount System Types
export interface ProductDiscount {
  id: string
  productId?: string
  variantId?: string
  categoryId?: string

  // Discount identification
  name: string
  code?: string
  description?: string

  // Discount type and value
  type: DiscountType
  value: number
  maxDiscount?: number
  minOrderAmount?: number

  // Buy X Get Y specific fields
  buyQuantity?: number
  getQuantity?: number
  getDiscountPercent?: number

  // Validity period
  startDate: Date
  endDate: Date

  // Usage limits
  usageLimit?: number
  usageCount: number
  userLimit?: number

  // Conditions
  conditions?: any

  // Targeting
  customerGroups?: string[]
  regions?: string[]

  // Status and priority
  isActive: boolean
  priority: number
  stackable: boolean

  // Analytics
  clickCount: number
  conversionCount: number
  totalSavings: number

  // Relations
  usages?: DiscountUsage[]

  // Audit
  createdBy?: string
  createdAt: Date
  updatedAt: Date
}

export interface DiscountUsage {
  id: string
  discountId: string

  // Usage details
  userId?: string
  orderId?: string
  productId?: string
  variantId?: string

  // Usage amounts
  originalAmount: number
  discountAmount: number
  finalAmount: number

  // Usage context
  ipAddress?: string
  userAgent?: string
  sessionId?: string

  usedAt: Date
}

export interface ProductPriceHistory {
  id: string
  productId: string
  variantId?: string

  oldPrice: number
  newPrice: number
  changeType: PriceChangeType
  reason?: string
  discountId?: string

  // Price components
  basePrice?: number
  costPrice?: number
  margin?: number

  effectiveDate: Date
  createdBy?: string
  createdAt: Date
}

// Enums
export enum DiscountType {
  PERCENTAGE = "PERCENTAGE",
  FIXED_AMOUNT = "FIXED_AMOUNT",
  BUY_X_GET_Y = "BUY_X_GET_Y",
  FREE_SHIPPING = "FREE_SHIPPING",
  BULK_DISCOUNT = "BULK_DISCOUNT",
  TIERED_DISCOUNT = "TIERED_DISCOUNT"
}

export enum PriceChangeType {
  INCREASE = "INCREASE",
  DECREASE = "DECREASE",
  PROMOTION = "PROMOTION",
  COST_CHANGE = "COST_CHANGE",
  MARKET_ADJUSTMENT = "MARKET_ADJUSTMENT",
  SEASONAL_CHANGE = "SEASONAL_CHANGE",
  COMPETITOR_MATCH = "COMPETITOR_MATCH",
  CLEARANCE = "CLEARANCE"
}

// Ürün resmi interface'i
export interface ProductImage {
  id: string
  productId?: string // Yeni görseller için opsiyonel yapıldı
  url: string
  alt: string
  title?: string
  sortOrder: number
  isMain: boolean
  size: number
  width: number
  height: number
  format: string
}

// Ürün videosu interface'i
export interface ProductVideo {
  id: string
  productId: string
  url: string
  title: string
  description?: string
  thumbnail?: string
  duration?: number
  sortOrder: number
}

// Ürün spesifikasyonu interface'i
export interface ProductSpecification {
  id: string
  productId: string
  name: string
  value: string
  unit?: string
  group: string
  sortOrder: number
}

// Ürün sertifikası interface'i
export interface ProductCertificate {
  id: string
  productId: string
  name: string
  issuer: string
  certificateNumber: string
  issueDate: Date
  expiryDate?: Date
  documentUrl?: string
  isActive: boolean
}

// Ürün boyutları interface'i
export interface ProductDimensions {
  length?: number
  width?: number
  height?: number
  unit?: "cm" | "mm" | "m"
}

// Filtreleme için interface'ler
export interface CategoryFilter {
  search?: string
  isActive?: boolean
  parentId?: string | null
  sortBy?: "name" | "productCount" | "createdAt" | "sortOrder" | "viewCount" | "popularityScore"
  sortOrder?: "asc" | "desc"
  page?: number
  limit?: number

  // Yeni filtreleme seçenekleri
  approvalStatus?: ApprovalStatus
  isPromoted?: boolean
  isFeatured?: boolean
  isSearchable?: boolean
  minViewCount?: number
  minPopularityScore?: number
}

// Ürün filtreleme parametreleri
export interface ProductFilterParams {
  search?: string
  categoryId?: string
  brand?: string
  minPrice?: number
  maxPrice?: number
  inStock?: boolean
  isActive?: boolean
  isFeatured?: boolean
  isNew?: boolean
  isOnSale?: boolean
  sortBy?: "name" | "price" | "createdAt" | "stock" | "popularity" | "updatedAt"
  sortOrder?: "asc" | "desc"
  page?: number
  limit?: number
  stockStatus?: "in_stock" | "out_of_stock" | "low_stock"
}

// API Response interface'leri
export interface ApiResponse<T> {
  success: boolean
  data: T
  message?: string
  errors?: string[]
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// Form interface'leri
export interface CategoryFormData {
  name: string
  slug?: string
  description: string
  icon?: string
  parentId?: string | null
  isActive?: boolean
  sortOrder?: number
  seoTitle?: string
  seoDescription?: string
}

export interface ProductFormData {
  name: string
  description: string
  shortDescription: string
  categoryId: string
  brand: string
  model?: string

  // Simplified Pricing Structure
  costPrice?: number // Alış fiyatı (purchase/cost price)
  basePrice: number // Normal satış fiyatı (base selling price)
  zakatAmount?: number // Zakat amount (zekat tutarı)
  taxRate: number

  // Stock Management
  trackStock: boolean
  stockQuantity?: number // Current stock quantity
  minStockThreshold?: number // Minimum stock threshold for low stock alerts
  maxStockCapacity?: number // Maximum stock capacity
  stockLocation?: string // Warehouse/location information
  reorderPoint?: number // Reorder point for automatic restocking
  reorderQuantity?: number // Quantity to reorder when stock is low

  weight?: number
  dimensions?: ProductDimensions
  specifications: Omit<ProductSpecification, "id" | "productId">[]
  certificates: Omit<ProductCertificate, "id" | "productId">[]
  isActive: boolean
  isFeatured: boolean
  isNew: boolean
  isOnSale: boolean
  // SEO Fields - Comprehensive SEO optimization
  seoTitle?: string
  seoDescription?: string
  metaKeywords?: string[]
  canonicalUrl?: string
  robotsDirective?: string
  focusKeyword?: string
  seoScore?: number

  // Open Graph Fields
  ogTitle?: string
  ogDescription?: string
  ogImage?: string
  ogType?: string

  // Twitter Card Fields
  twitterCard?: string
  twitterTitle?: string
  twitterDescription?: string
  twitterImage?: string

  // Structured Data
  structuredData?: Record<string, any>

  // Additional SEO Fields
  alternativeText?: string
  breadcrumbs?: string[]
  schemaMarkup?: string
  hreflangTags?: Record<string, string>

  // Content SEO
  readabilityScore?: number
  keywordDensity?: number
  internalLinks?: string[]
  externalLinks?: string[]
  images?: ProductImage[] // Formda görsel yönetimi için eklendi
  discounts?: Omit<ProductDiscount, "id" | "productId" | "usageCount" | "clickCount" | "conversionCount" | "totalSavings" | "createdAt" | "updatedAt">[]
}

// Ürün oluşturma için giriş tipi
export type ProductCreateInput = Omit<ProductFormData, "images"> & {
  images?: Omit<ProductImage, "id" | "productId">[]
}

// Ürün güncelleme için giriş tipi
export type ProductUpdateInput = Partial<Omit<ProductFormData, "images">> & {
  images?: Partial<Omit<ProductImage, "id" | "productId">>[]
  priceAdjustment?: {
    // Toplu fiyat ayarlaması için eklendi
    type: "increase" | "decrease"
    value: number
    method: "percentage" | "fixed"
  }
}

// Enum'lar
export enum StockStatus {
  IN_STOCK = "in_stock",
  OUT_OF_STOCK = "out_of_stock",
  LOW_STOCK = "low_stock",
}

export enum Currency {
  TRY = "TRY",
  USD = "USD",
  EUR = "EUR",
}

export enum DimensionUnit {
  CM = "cm",
  MM = "mm",
  M = "m",
}

// Stock Management Utilities
export interface StockInfo {
  quantity: number
  status: "IN_STOCK" | "OUT_OF_STOCK" | "LOW_STOCK"
  isLowStock: boolean
  isOutOfStock: boolean
}

export interface ProfitInfo {
  costPrice: number
  sellingPrice: number
  profitAmount: number
  profitMargin: number // percentage
}

// Utility functions for stock and profit calculations
export const calculateStockStatus = (
  quantity: number = 0,
  minThreshold: number = 0
): "IN_STOCK" | "OUT_OF_STOCK" | "LOW_STOCK" => {
  if (quantity <= 0) return "OUT_OF_STOCK"
  if (quantity <= minThreshold) return "LOW_STOCK"
  return "IN_STOCK"
}

export const calculateProfitMargin = (
  costPrice: number = 0,
  sellingPrice: number = 0
): number => {
  if (costPrice <= 0 || sellingPrice <= 0) return 0
  return ((sellingPrice - costPrice) / sellingPrice) * 100
}

export const getStockStatusColor = (status: string): string => {
  switch (status) {
    case "IN_STOCK": return "text-green-600"
    case "LOW_STOCK": return "text-yellow-600"
    case "OUT_OF_STOCK": return "text-red-600"
    default: return "text-gray-600"
  }
}

export const getStockStatusText = (status: string): string => {
  switch (status) {
    case "IN_STOCK": return "Stokta"
    case "LOW_STOCK": return "Az Stok"
    case "OUT_OF_STOCK": return "Stok Yok"
    default: return "Bilinmiyor"
  }
}

// Enhanced pricing calculation utilities
export interface ActiveDiscountInfo {
  discount: ProductDiscount | null
  discountAmount: number
  discountPercentage: number
  currentPrice: number
}

export const calculateCurrentPrice = (
  basePrice: number,
  discounts: ProductDiscount[] = []
): ActiveDiscountInfo => {
  // Safety check for basePrice
  if (!basePrice || basePrice <= 0) {
    return {
      discount: null,
      discountAmount: 0,
      discountPercentage: 0,
      currentPrice: 0
    }
  }

  const now = new Date()

  // Find active discount
  const activeDiscount = discounts.find(discount =>
    discount &&
    discount.isActive &&
    discount.startDate &&
    discount.endDate &&
    new Date(discount.startDate) <= now &&
    new Date(discount.endDate) >= now
  )

  if (!activeDiscount) {
    return {
      discount: null,
      discountAmount: 0,
      discountPercentage: 0,
      currentPrice: basePrice
    }
  }

  let currentPrice = basePrice
  let discountAmount = 0
  let discountPercentage = 0

  switch (activeDiscount.type) {
    case 'PERCENTAGE':
      discountPercentage = activeDiscount.value
      discountAmount = basePrice * (activeDiscount.value / 100)
      currentPrice = basePrice - discountAmount
      break

    case 'FIXED_AMOUNT':
      discountAmount = activeDiscount.value
      discountPercentage = (discountAmount / basePrice) * 100
      currentPrice = Math.max(0, basePrice - discountAmount)
      break

    default:
      // For other discount types, return base price for now
      break
  }

  return {
    discount: activeDiscount,
    discountAmount,
    discountPercentage,
    currentPrice
  }
}

export const calculateProfitFromCurrentPrice = (
  costPrice: number = 0,
  currentPrice: number = 0
): number => {
  if (costPrice <= 0 || currentPrice <= 0) return 0
  return ((currentPrice - costPrice) / currentPrice) * 100
}
