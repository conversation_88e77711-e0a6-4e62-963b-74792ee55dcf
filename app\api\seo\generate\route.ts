import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { createSEOGenerationService, createSimpleLLMApiCall, type SEOGenerationInput, type LLMApiCall } from '@/lib/services/seo-generation-service'

// Request validation schema
const generateSEOSchema = z.object({
  productName: z.string().min(1, 'Product name is required').max(200, 'Product name too long'),
  productDescription: z.string().min(10, 'Product description must be at least 10 characters').max(2000, 'Product description too long'),
  category: z.string().optional(),
  brand: z.string().optional(),
  price: z.number().min(0).optional(),
  storeName: z.string().optional()
})

// OpenRouter AI API call function
async function callLLMApi(prompt: string): Promise<string> {
  console.log('🤖 OpenRouter API Call - Prompt length:', prompt.length)
  console.log('📝 Prompt preview:', prompt.substring(0, 200) + '...')

  // Validate environment variables
  const apiKey = process.env.OPENROUTER_API_KEY
  if (!apiKey) {
    throw new Error('OPENROUTER_API_KEY environment variable is not set')
  }

  const model = process.env.OPENROUTER_MODEL || 'deepseek/deepseek-chat-v3-0324:free'
  const siteUrl = process.env.OPENROUTER_SITE_URL || 'http://localhost:3000'
  const siteName = process.env.OPENROUTER_SITE_NAME || 'İş Güvenliği E-ticaret'

  console.log('🔧 OpenRouter Config:', { model, siteUrl, siteName })

  try {
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': siteUrl,
        'X-Title': siteName,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: model,
        messages: [
          {
            role: 'system',
            content: 'Sen Türkiye e-ticaret pazarı için uzman bir SEO specialist ve içerik stratejistisin. Her zaman geçerli JSON formatında yanıt döndür.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 3000,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0
      })
    })

    console.log('📡 OpenRouter Response Status:', response.status, response.statusText)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ OpenRouter API Error Response:', errorText)

      // Handle specific OpenRouter error codes
      if (response.status === 401) {
        throw new Error('OpenRouter API authentication failed. Check your API key.')
      } else if (response.status === 429) {
        throw new Error('OpenRouter API rate limit exceeded. Please try again later.')
      } else if (response.status === 402) {
        throw new Error('OpenRouter API insufficient credits. Please check your account balance.')
      } else {
        throw new Error(`OpenRouter API error: ${response.status} ${response.statusText} - ${errorText}`)
      }
    }

    const data = await response.json()
    console.log('📊 OpenRouter Response Data Keys:', Object.keys(data))

    // Validate response structure
    if (!data.choices || !Array.isArray(data.choices) || data.choices.length === 0) {
      console.error('❌ Invalid OpenRouter response structure:', data)
      throw new Error('Invalid response structure from OpenRouter API')
    }

    const choice = data.choices[0]
    if (!choice.message || !choice.message.content) {
      console.error('❌ No content in OpenRouter response:', choice)
      throw new Error('No content received from OpenRouter API')
    }

    const content = choice.message.content.trim()
    console.log('✅ OpenRouter Content Length:', content.length)
    console.log('📝 Content Preview:', content.substring(0, 200) + '...')

    // Log usage information if available
    if (data.usage) {
      console.log('📈 Token Usage:', {
        prompt_tokens: data.usage.prompt_tokens,
        completion_tokens: data.usage.completion_tokens,
        total_tokens: data.usage.total_tokens
      })
    }

    return content

  } catch (error) {
    console.error('❌ OpenRouter API call failed:', error)

    // Re-throw with more context
    if (error instanceof Error) {
      throw new Error(`OpenRouter API call failed: ${error.message}`)
    } else {
      throw new Error('OpenRouter API call failed with unknown error')
    }
  }
}

export async function POST(request: NextRequest) {
  const startTime = Date.now()

  try {
    console.log('🚀 SEO Generation API called')

    // Parse and validate request body
    const body = await request.json()
    console.log('📦 Request data:', {
      productName: body.productName,
      category: body.category,
      brand: body.brand,
      price: body.price
    })

    const validatedData = generateSEOSchema.parse(body)
    console.log('✅ Request validation successful')

    // Create LLM API call function
    const llmApiCall: LLMApiCall = createSimpleLLMApiCall(callLLMApi)

    // Create SEO generation service
    const seoService = createSEOGenerationService({
      llmApiCall,
      enableFallback: process.env.SEO_FALLBACK_TO_LOCAL !== 'false'
    })

    // Generate SEO data
    const seoData = await seoService.generateSEO(validatedData as SEOGenerationInput)

    const duration = Date.now() - startTime
    console.log(`🎉 SEO generation completed in ${duration}ms`)

    return NextResponse.json({
      success: true,
      data: seoData,
      message: 'SEO data generated successfully',
      meta: {
        processingTime: duration,
        generatedFields: Object.keys(seoData).length,
        fallbackUsed: false // This would be set by the service if fallback was used
      }
    })

  } catch (error) {
    const duration = Date.now() - startTime
    console.error(`❌ SEO generation failed after ${duration}ms:`, error)

    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Validation error',
        details: error.errors.map(e => ({
          field: e.path.join('.'),
          message: e.message,
          code: e.code
        })),
        meta: { processingTime: duration }
      }, { status: 400 })
    }

    // Check if it's a known error type
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    const isLLMError = errorMessage.includes('LLM') || errorMessage.includes('API')

    return NextResponse.json({
      success: false,
      error: isLLMError ? 'LLM service error' : 'SEO generation failed',
      message: errorMessage,
      meta: {
        processingTime: duration,
        errorType: isLLMError ? 'llm_error' : 'processing_error'
      }
    }, { status: 500 })
  }
}

// GET endpoint for testing and status
export async function GET() {
  const isOpenRouterConfigured = !!process.env.OPENROUTER_API_KEY
  const openRouterModel = process.env.OPENROUTER_MODEL || 'deepseek/deepseek-chat-v3-0324:free'

  return NextResponse.json({
    success: true,
    message: 'SEO Generation API is running with OpenRouter AI',
    status: {
      llmProvider: 'OpenRouter AI',
      llmConfigured: isOpenRouterConfigured,
      model: openRouterModel,
      fallbackEnabled: process.env.SEO_FALLBACK_TO_LOCAL !== 'false',
      environment: process.env.NODE_ENV,
      siteUrl: process.env.OPENROUTER_SITE_URL,
      siteName: process.env.OPENROUTER_SITE_NAME
    },
    endpoints: {
      generate: 'POST /api/seo/generate',
      test: 'GET /api/seo/generate'
    },
    example: {
      productName: 'Onvec Smart Tag Akıllı Takip Cihazı 4 adet (Apple uyumlu)',
      productDescription: 'Onvec Smart Tag Akıllı Takip Cihazı, anahtarlarınızdan değerli eşyalarınıza ve hatta evcil hayvanlarınıza kadar birçok farklı şeyi takip etmek için kullanabileceğiniz kullanışlı bir cihazdır.',
      category: 'Telefonlar & Aksesuarlar',
      brand: 'Onvec',
      price: 899,
      storeName: 'Teknoloji Mağazası'
    },
    openRouter: {
      endpoint: 'https://openrouter.ai/api/v1/chat/completions',
      model: openRouterModel,
      configured: isOpenRouterConfigured,
      features: ['Turkish SEO Generation', 'E-commerce Optimization', 'Schema.org Markup']
    },
    instructions: {
      setup: 'OpenRouter AI is now integrated and ready to use',
      requiredEnvVars: ['OPENROUTER_API_KEY'],
      optionalEnvVars: ['OPENROUTER_MODEL', 'OPENROUTER_SITE_URL', 'OPENROUTER_SITE_NAME']
    }
  })
}
