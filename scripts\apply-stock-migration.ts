#!/usr/bin/env tsx

/**
 * Stock Management Migration Script
 * 
 * This script applies the stock management fields migration to ensure
 * the database has the required fields before seeding products.
 * 
 * Usage:
 * npx tsx scripts/apply-stock-migration.ts
 */

import { PrismaClient } from '@prisma/client'
import { readFileSync } from 'fs'
import { join } from 'path'

const prisma = new PrismaClient()

async function applyStockMigration() {
  console.log('🔧 Applying stock management migration...')
  
  try {
    // Check if migration is needed
    console.log('🔍 Checking current database schema...')
    
    try {
      await prisma.product.findFirst({
        select: {
          stockQuantity: true,
          minStockThreshold: true,
          maxStockCapacity: true,
          stockStatus: true
        }
      })
      console.log('✅ Stock management fields already exist')
      return true
    } catch (error) {
      console.log('⚠️  Stock management fields missing, applying migration...')
    }

    // Read and execute migration SQL
    const migrationPath = join(process.cwd(), 'prisma', 'migrations', 'add_stock_management_fields.sql')
    
    try {
      const migrationSQL = readFileSync(migrationPath, 'utf8')
      console.log('📄 Migration file loaded')
      
      // Execute migration using raw SQL
      await prisma.$executeRawUnsafe(migrationSQL)
      console.log('✅ Migration executed successfully')
      
      // Verify migration
      await prisma.product.findFirst({
        select: {
          stockQuantity: true,
          minStockThreshold: true,
          maxStockCapacity: true,
          stockStatus: true
        }
      })
      console.log('✅ Migration verified - all fields accessible')
      
      return true
      
    } catch (fileError) {
      console.log('⚠️  Migration file not found, using Prisma db push...')
      
      // Fallback to Prisma db push
      const { spawn } = require('child_process')
      
      return new Promise((resolve, reject) => {
        const dbPush = spawn('npx', ['prisma', 'db', 'push'], {
          stdio: 'inherit',
          shell: true
        })
        
        dbPush.on('close', (code) => {
          if (code === 0) {
            console.log('✅ Database schema updated via Prisma db push')
            resolve(true)
          } else {
            console.error('❌ Prisma db push failed')
            reject(new Error('Database migration failed'))
          }
        })
        
        dbPush.on('error', (error) => {
          console.error('❌ Failed to run Prisma db push:', error)
          reject(error)
        })
      })
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Export for use in other scripts
export { applyStockMigration }

// Run if called directly
if (require.main === module) {
  applyStockMigration()
    .then(() => {
      console.log('')
      console.log('🎉 Stock management migration completed!')
      console.log('💡 You can now run the comprehensive seeding script:')
      console.log('   npx tsx scripts/comprehensive-seed.ts')
    })
    .catch((error) => {
      console.error(error)
      process.exit(1)
    })
}
