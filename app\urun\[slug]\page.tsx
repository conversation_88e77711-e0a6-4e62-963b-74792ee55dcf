import type { Metadata } from "next"
import { notFound } from "next/navigation"
import { ProductDetailView } from "@/components/products/product-detail-view"
import ProductService from "@/lib/services/ProductService"

interface ProductPageProps {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  try {
    const product = await ProductService.getBySlug(params.slug)

    if (!product) {
      return {
        title: "Ürün Bulunamadı - İş Güvenliği Mağazası",
        description: "Aradığınız ürün bulunamadı.",
      }
    }

    return {
      title: product.seoTitle || `${product.name} - İş Güvenliği Mağazası`,
      description: product.seoDescription || product.shortDescription,
      keywords: product.metaKeywords?.join(", "),
      openGraph: {
        title: product.name,
        description: product.shortDescription,
        images:
          product.images.length > 0
            ? [
                {
                  url: product.images[0].url,
                  width: product.images[0].width,
                  height: product.images[0].height,
                  alt: product.images[0].alt,
                },
              ]
            : [],
        type: "website",
        url: `/urun/${product.slug}`,
      },
      twitter: {
        card: "summary_large_image",
        title: product.name,
        description: product.shortDescription,
        images: product.images.length > 0 ? [product.images[0].url] : [],
      },
      alternates: {
        canonical: `/urun/${product.slug}`,
      },
    }
  } catch (error) {
    return {
      title: "Ürün Bulunamadı - İş Güvenliği Mağazası",
      description: "Aradığınız ürün bulunamadı.",
    }
  }
}

export default async function ProductPage({ params }: ProductPageProps) {
  try {
    const product = await ProductService.getBySlug(params.slug)

    if (!product) {
      notFound()
    }

    // JSON-LD structured data for SEO
    const jsonLd = {
      "@context": "https://schema.org",
      "@type": "Product",
      name: product.name,
      description: product.description,
      image: product.images.map((img) => img.url),
      brand: {
        "@type": "Brand",
        name: product.brand,
      },
      category: product.category.name,
      sku: product.sku,
      offers: {
        "@type": "Offer",
        price: product.price,
        priceCurrency: product.currency,
        availability:
          product.stockStatus === "in_stock" ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
        seller: {
          "@type": "Organization",
          name: "İş Güvenliği Mağazası",
        },
      },
      aggregateRating: product.rating
        ? {
            "@type": "AggregateRating",
            ratingValue: product.rating,
            reviewCount: product.reviewCount || 0,
          }
        : undefined,
    }

    return (
      <>
        <script type="application/ld+json" dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }} />
        <ProductDetailView product={product} />
      </>
    )
  } catch (error) {
    console.error("Product page error:", error)
    notFound()
  }
}
