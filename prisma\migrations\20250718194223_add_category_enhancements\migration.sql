-- Create<PERSON><PERSON>
CREATE TYPE "StockStatus" AS ENUM ('STOK<PERSON>', 'DUSUK_STOK', 'STOK_YOK', 'URETIM_DURDURULDU');

-- CreateEnum
CREATE TYPE "ApprovalStatus" AS ENUM ('TASLAK', '<PERSON><PERSON><PERSON><PERSON>ME<PERSON>', 'ONAYLAND<PERSON>', 'REDDEDILDI');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "SyncStatus" AS ENUM ('SENKRONIZE', 'BE<PERSON><PERSON>MEDE', 'BASARISIZ', 'DEVRE_DISI');

-- CreateEnum
CREATE TYPE "IconType" AS ENUM ('SVG', 'PNG', 'FONT_ICON', 'EMOJI');

-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "MobileTemplate" AS ENUM ('VARSAYILAN', 'GRID', 'LISTE', 'KART', 'BANNER');

-- AlterTable
ALTER TABLE "categories" ADD COLUMN     "adminNotes" TEXT,
ADD COLUMN     "approvalStatus" "ApprovalStatus" NOT NULL DEFAULT 'ONAYLANDI',
ADD COLUMN     "avgOrderValue" DOUBLE PRECISION,
ADD COLUMN     "bannerImage" TEXT,
ADD COLUMN     "cacheKey" TEXT,
ADD COLUMN     "changeLog" JSONB,
ADD COLUMN     "colorCode" TEXT,
ADD COLUMN     "commissionRate" DOUBLE PRECISION,
ADD COLUMN     "conversionRate" DOUBLE PRECISION,
ADD COLUMN     "createdBy" TEXT,
ADD COLUMN     "iconType" "IconType" DEFAULT 'SVG',
ADD COLUMN     "isFeatured" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "isPromoted" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "isSearchable" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "lowStockThreshold" INTEGER,
ADD COLUMN     "metaKeywords" TEXT,
ADD COLUMN     "minOrderAmount" DOUBLE PRECISION,
ADD COLUMN     "mobileIcon" TEXT,
ADD COLUMN     "mobileTemplate" "MobileTemplate" DEFAULT 'VARSAYILAN',
ADD COLUMN     "ogDescription" TEXT,
ADD COLUMN     "ogImage" TEXT,
ADD COLUMN     "ogTitle" TEXT,
ADD COLUMN     "popularityScore" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
ADD COLUMN     "searchKeywords" TEXT,
ADD COLUMN     "stockStatus" "StockStatus" NOT NULL DEFAULT 'STOKTA',
ADD COLUMN     "taxRate" DOUBLE PRECISION,
ADD COLUMN     "totalStock" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "version" INTEGER NOT NULL DEFAULT 1,
ADD COLUMN     "viewCount" INTEGER NOT NULL DEFAULT 0;
