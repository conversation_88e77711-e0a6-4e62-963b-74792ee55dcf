import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  const testResults = []
  let allTestsPassed = true

  try {
    console.log('🧪 Starting Stock Management API Tests...')

    // Test 1: Database Schema Verification
    console.log('\n📋 Test 1: Database Schema Verification')
    try {
      // Check if stock_movements table exists
      const stockMovementCount = await prisma.stockMovement.count()
      console.log('✅ StockMovement table accessible, count:', stockMovementCount)
      
      // Check if Product has stock fields
      const testProduct = await prisma.product.findFirst({
        select: {
          id: true,
          name: true,
          stockQuantity: true,
          minStockThreshold: true,
          maxStockCapacity: true,
          stockStatus: true,
          trackStock: true
        }
      })
      
      if (testProduct) {
        console.log('✅ Product stock fields accessible:', {
          stockQuantity: testProduct.stockQuantity,
          minStockThreshold: testProduct.minStockThreshold,
          maxStockCapacity: testProduct.maxStockCapacity,
          stockStatus: testProduct.stockStatus
        })
      }

      testResults.push({
        test: 'Database Schema Verification',
        status: 'PASSED',
        details: {
          stockMovementTable: 'Accessible',
          productStockFields: 'Accessible',
          sampleProduct: testProduct
        }
      })
    } catch (error: any) {
      console.log('❌ Schema verification failed:', error.message)
      testResults.push({
        test: 'Database Schema Verification',
        status: 'FAILED',
        error: error.message
      })
      allTestsPassed = false
    }

    // Test 2: Create Test Product with Stock
    console.log('\n📋 Test 2: Create Test Product with Stock')
    let testProductId = null
    try {
      // First, get or create a category
      let testCategory = await prisma.category.findFirst({
        where: { name: 'Test Category' }
      })
      
      if (!testCategory) {
        testCategory = await prisma.category.create({
          data: {
            name: 'Test Category',
            slug: 'test-category',
            description: 'Test category for stock management',
            icon: '📦',
            isActive: true,
            sortOrder: 999
          }
        })
      }

      // Create test product
      const testProduct = await prisma.product.create({
        data: {
          name: 'Test Stock Product',
          slug: 'test-stock-product-' + Date.now(),
          description: 'Test product for stock management testing',
          shortDescription: 'Test product',
          categoryId: testCategory.id,
          brand: 'Test Brand',
          basePrice: 100.0,
          baseCostPrice: 50.0,
          stockQuantity: 100,
          minStockThreshold: 10,
          maxStockCapacity: 500,
          trackStock: true,
          stockStatus: 'IN_STOCK',
          isActive: true,
          publishedAt: new Date()
        }
      })

      testProductId = testProduct.id
      console.log('✅ Test product created:', testProduct.name, 'ID:', testProductId)

      testResults.push({
        test: 'Create Test Product with Stock',
        status: 'PASSED',
        details: {
          productId: testProductId,
          initialStock: 100,
          minThreshold: 10,
          maxCapacity: 500
        }
      })
    } catch (error: any) {
      console.log('❌ Test product creation failed:', error.message)
      testResults.push({
        test: 'Create Test Product with Stock',
        status: 'FAILED',
        error: error.message
      })
      allTestsPassed = false
    }

    // Test 3: Stock Movement API - Stock In
    console.log('\n📋 Test 3: Stock Movement API - Stock In')
    if (testProductId) {
      try {
        const stockInData = {
          productId: testProductId,
          movementType: 'in',
          quantity: 50,
          reason: 'purchase',
          reference: 'PO-001',
          unitCost: 45.0,
          supplier: 'Test Supplier',
          notes: 'Test stock in movement',
          createdBy: 'test-user'
        }

        // Simulate API call
        const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/stock-movements`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(stockInData)
        })

        if (response.ok) {
          const result = await response.json()
          console.log('✅ Stock in movement created:', result.data.id)
          
          // Verify stock was updated
          const updatedProduct = await prisma.product.findUnique({
            where: { id: testProductId },
            select: { stockQuantity: true, stockStatus: true }
          })
          
          console.log('✅ Product stock updated to:', updatedProduct?.stockQuantity)

          testResults.push({
            test: 'Stock Movement API - Stock In',
            status: 'PASSED',
            details: {
              movementId: result.data.id,
              newStock: updatedProduct?.stockQuantity,
              stockStatus: updatedProduct?.stockStatus
            }
          })
        } else {
          throw new Error(`API returned ${response.status}`)
        }
      } catch (error: any) {
        console.log('❌ Stock in test failed:', error.message)
        testResults.push({
          test: 'Stock Movement API - Stock In',
          status: 'FAILED',
          error: error.message
        })
        allTestsPassed = false
      }
    }

    // Test 4: Stock Movement API - Stock Out
    console.log('\n📋 Test 4: Stock Movement API - Stock Out')
    if (testProductId) {
      try {
        const stockOutData = {
          productId: testProductId,
          movementType: 'out',
          quantity: 25,
          reason: 'sale',
          reference: 'ORDER-001',
          notes: 'Test stock out movement'
        }

        const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/stock-movements`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(stockOutData)
        })

        if (response.ok) {
          const result = await response.json()
          console.log('✅ Stock out movement created:', result.data.id)

          testResults.push({
            test: 'Stock Movement API - Stock Out',
            status: 'PASSED',
            details: {
              movementId: result.data.id
            }
          })
        } else {
          throw new Error(`API returned ${response.status}`)
        }
      } catch (error: any) {
        console.log('❌ Stock out test failed:', error.message)
        testResults.push({
          test: 'Stock Movement API - Stock Out',
          status: 'FAILED',
          error: error.message
        })
        allTestsPassed = false
      }
    }

    // Test 5: Product Stock API
    console.log('\n📋 Test 5: Product Stock API')
    if (testProductId) {
      try {
        const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/products/${testProductId}/stock`)
        
        if (response.ok) {
          const result = await response.json()
          console.log('✅ Product stock API working:', result.data.product.stockQuantity)

          testResults.push({
            test: 'Product Stock API',
            status: 'PASSED',
            details: {
              currentStock: result.data.product.stockQuantity,
              recentMovements: result.data.recentMovements.length,
              statistics: result.data.statistics
            }
          })
        } else {
          throw new Error(`API returned ${response.status}`)
        }
      } catch (error: any) {
        console.log('❌ Product stock API test failed:', error.message)
        testResults.push({
          test: 'Product Stock API',
          status: 'FAILED',
          error: error.message
        })
        allTestsPassed = false
      }
    }

    // Test 6: Stock Movements List API
    console.log('\n📋 Test 6: Stock Movements List API')
    try {
      const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/stock-movements?limit=10`)
      
      if (response.ok) {
        const result = await response.json()
        console.log('✅ Stock movements list API working, found:', result.data.length, 'movements')

        testResults.push({
          test: 'Stock Movements List API',
          status: 'PASSED',
          details: {
            movementsCount: result.data.length,
            totalCount: result.pagination.total
          }
        })
      } else {
        throw new Error(`API returned ${response.status}`)
      }
    } catch (error: any) {
      console.log('❌ Stock movements list test failed:', error.message)
      testResults.push({
        test: 'Stock Movements List API',
        status: 'FAILED',
        error: error.message
      })
      allTestsPassed = false
    }

    // Cleanup: Remove test product
    if (testProductId) {
      try {
        await prisma.product.delete({
          where: { id: testProductId }
        })
        console.log('🧹 Test product cleaned up')
      } catch (error) {
        console.log('⚠️  Failed to cleanup test product:', error)
      }
    }

    console.log('\n🎯 Test Summary:')
    console.log(`Total Tests: ${testResults.length}`)
    console.log(`Passed: ${testResults.filter(t => t.status === 'PASSED').length}`)
    console.log(`Failed: ${testResults.filter(t => t.status === 'FAILED').length}`)

    return NextResponse.json({
      success: allTestsPassed,
      message: allTestsPassed ? 'All tests passed!' : 'Some tests failed',
      summary: {
        total: testResults.length,
        passed: testResults.filter(t => t.status === 'PASSED').length,
        failed: testResults.filter(t => t.status === 'FAILED').length
      },
      results: testResults
    })

  } catch (error: any) {
    console.error('❌ Test suite failed:', error)
    return NextResponse.json({
      success: false,
      error: 'Test suite execution failed',
      details: error.message,
      results: testResults
    }, { status: 500 })
  }
}
