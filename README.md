# İş Güvenliği E-Ticaret Platformu

Modern ve güvenli bir iş güvenliği ekipmanları e-ticaret platformu. Next.js, TypeScript, Prisma ve PostgreSQL ile geliştirilmiştir.

## 🚀 Özellikler

- **Modern UI/UX**: Tailwind CSS ve Shadcn/ui ile responsive tasarım
- **Güçlü Backend**: Next.js API Routes ile RESTful API
- **Tip Güvenliği**: TypeScript ile tam tip desteği
- **Veritabanı**: PostgreSQL ile güvenilir veri yönetimi
- **ORM**: Prisma ile type-safe veritabanı işlemleri
- **Görsel Yönetimi**: Sharp ile otomatik görsel işleme
- **Drag & Drop**: Modern dosya yükleme deneyimi

## 📋 Gereksinimler

- Node.js 18+ 
- Docker ve Docker Compose
- npm veya yarn

## 🛠️ Kurulum

### 1. <PERSON><PERSON><PERSON>

```bash
git clone <repository-url>
cd is-guvenligi-eticaret
```

### 2. Bağımlılıkları Yükleyin

```bash
npm install
```

### 3. Environment Variables

`.env.example` dosyasını `.env` olarak kopyalayın:

```bash
cp .env.example .env
```

### 4. PostgreSQL Veritabanını Başlatın

Docker Compose ile PostgreSQL ve pgAdmin'i başlatın:

```bash
docker-compose up -d
```

Bu komut şunları başlatır:
- **PostgreSQL**: `localhost:5432`
- **pgAdmin**: `localhost:5050` (<EMAIL> / admin123)

### 5. Veritabanı Migration ve Seed

```bash
# Prisma client oluştur
npx prisma generate

# Migration çalıştır
npx prisma migrate dev --name init

# Seed data yükle
npm run db:seed
```

### 6. Development Server'ı Başlatın

```bash
npm run dev
```

Uygulama `http://localhost:3000` adresinde çalışacaktır.

## 🗄️ Veritabanı Yönetimi

### pgAdmin Erişimi

1. `http://localhost:5050` adresine gidin
2. Email: `<EMAIL>`
3. Password: `admin123`
4. Yeni server ekleyin:
   - Name: `Local PostgreSQL`
   - Host: `postgres` (Docker network içinde)
   - Port: `5432`
   - Database: `is_guvenligi_eticaret`
   - Username: `postgres`
   - Password: `postgres123`

### Prisma Studio

Veritabanını görsel olarak yönetmek için:

```bash
npx prisma studio
```

### Yararlı Komutlar

```bash
# Veritabanını sıfırla
npm run db:reset

# Yeni migration oluştur
npx prisma migrate dev --name migration-name

# Production için migration
npx prisma migrate deploy

# Seed data yükle
npm run db:seed
```

## 📁 Proje Yapısı

```
├── app/                    # Next.js App Router
│   ├── api/               # API Routes
│   ├── admin/             # Admin paneli
│   └── (public)/          # Public sayfalar
├── components/            # React bileşenleri
│   ├── ui/               # Temel UI bileşenleri
│   └── admin/            # Admin bileşenleri
├── lib/                  # Utility fonksiyonlar
│   ├── hooks/            # Custom React hooks
│   ├── services/         # API servisleri
│   └── utils.ts          # Yardımcı fonksiyonlar
├── prisma/               # Prisma schema ve migrations
├── public/               # Statik dosyalar
├── types/                # TypeScript tip tanımları
└── docker/               # Docker konfigürasyonları
```

## 🔧 Geliştirme

### ⚠️ Development Mode Authentication Bypass

Geliştirme sürecini kolaylaştırmak için authentication'ı geçici olarak bypass edebilirsiniz:

#### Authentication'ı Devre Dışı Bırakma (Development Only)

1. `.env` dosyasında şu değişkenleri ayarlayın:
```bash
SKIP_AUTH="true"
NEXT_PUBLIC_SKIP_AUTH="true"
```

2. Uygulamayı yeniden başlatın:
```bash
npm run dev
```

3. Artık `/admin` route'larına giriş yapmadan erişebilirsiniz

#### Authentication'ı Yeniden Etkinleştirme

1. `.env` dosyasında değişkenleri `false` yapın:
```bash
SKIP_AUTH="false"
NEXT_PUBLIC_SKIP_AUTH="false"
```

2. Uygulamayı yeniden başlatın

#### 🚨 Güvenlik Uyarıları

- **Bu özellik sadece development ortamında çalışır**
- **Production'da otomatik olarak devre dışı kalır**
- **NODE_ENV=production olduğunda bypass çalışmaz**
- **Production deployment'ında bu değişkenleri kaldırın**

### Yeni Özellik Ekleme

1. TypeScript tiplerini `types/index.ts` dosyasında tanımlayın
2. Prisma schema'sını güncelleyin (`prisma/schema.prisma`)
3. Migration oluşturun: `npx prisma migrate dev`
4. API endpoint'lerini `app/api/` altında oluşturun
5. Frontend bileşenlerini `components/` altında geliştirin

### Code Style

- ESLint ve Prettier kullanılır
- TypeScript strict mode aktif
- Tailwind CSS için class sorting

## 🚀 Production Deployment

### Environment Variables

Production için gerekli environment variables:

```bash
DATABASE_URL="postgresql://user:password@host:port/database"
NODE_ENV="production"
NEXTAUTH_URL="https://yourdomain.com"
NEXTAUTH_SECRET="your-production-secret"

# 🚨 IMPORTANT: Remove or set to false in production
SKIP_AUTH="false"
NEXT_PUBLIC_SKIP_AUTH="false"
```

### 🔒 Production Security Checklist

Production'a deploy etmeden önce:

1. ✅ `NODE_ENV="production"` olarak ayarlandığından emin olun
2. ✅ `SKIP_AUTH` ve `NEXT_PUBLIC_SKIP_AUTH` değişkenlerini kaldırın veya `false` yapın
3. ✅ `NEXTAUTH_SECRET` için güçlü bir secret key kullanın
4. ✅ Database connection string'ini production database'e yönlendirin
5. ✅ Environment variables'ları güvenli bir şekilde saklayın
6. ✅ HTTPS kullandığınızdan emin olun

### Build

```bash
npm run build
npm start
```

## 📝 API Endpoints

### Products
- `GET /api/products` - Ürün listesi
- `GET /api/products/[id]` - Ürün detayı
- `POST /api/products` - Yeni ürün
- `PUT /api/products/[id]` - Ürün güncelle
- `DELETE /api/products/[id]` - Ürün sil

### Categories
- `GET /api/categories` - Kategori listesi
- `GET /api/categories/[id]` - Kategori detayı
- `POST /api/categories` - Yeni kategori
- `PUT /api/categories/[id]` - Kategori güncelle
- `DELETE /api/categories/[id]` - Kategori sil

### Upload
- `POST /api/upload` - Dosya yükleme
- `DELETE /api/upload` - Dosya silme

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit edin (`git commit -m 'Add amazing feature'`)
4. Push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.
