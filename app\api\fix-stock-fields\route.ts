import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 Adding missing stock fields to products table...')

    // Step 1: Add missing stock management fields to products table
    console.log('Adding stockQuantity column...')
    await prisma.$executeRaw`
      ALTER TABLE "products" ADD COLUMN IF NOT EXISTS "stockQuantity" INTEGER DEFAULT 0;
    `
    
    console.log('Adding minStockThreshold column...')
    await prisma.$executeRaw`
      ALTER TABLE "products" ADD COLUMN IF NOT EXISTS "minStockThreshold" INTEGER DEFAULT 0;
    `
    
    console.log('Adding maxStockCapacity column...')
    await prisma.$executeRaw`
      ALTER TABLE "products" ADD COLUMN IF NOT EXISTS "maxStockCapacity" INTEGER DEFAULT 100;
    `

    console.log('✅ Stock fields added successfully')

    // Step 2: Set reasonable defaults for existing products
    console.log('Setting default stock values...')
    await prisma.$executeRaw`
      UPDATE "products" 
      SET "stockQuantity" = 50
      WHERE "stockQuantity" = 0;
    `

    await prisma.$executeRaw`
      UPDATE "products" 
      SET "minStockThreshold" = 10
      WHERE "minStockThreshold" = 0;
    `

    console.log('✅ Default values set')

    // Step 3: Test that the fields now exist
    console.log('Testing field access...')
    const testProduct = await prisma.product.findFirst({
      select: {
        id: true,
        name: true,
        stockQuantity: true,
        minStockThreshold: true,
        maxStockCapacity: true,
        stockStatus: true,
        trackStock: true,
        basePrice: true
      }
    })

    if (testProduct) {
      console.log('✅ Migration successful! Stock fields are now accessible')
      console.log('Sample product:', {
        id: testProduct.id,
        name: testProduct.name,
        stockQuantity: testProduct.stockQuantity,
        minStockThreshold: testProduct.minStockThreshold,
        maxStockCapacity: testProduct.maxStockCapacity,
        stockStatus: testProduct.stockStatus
      })
    }

    return NextResponse.json({
      success: true,
      message: 'Stock management fields added successfully',
      testProduct: testProduct,
      nextSteps: [
        'Test the debug stock API',
        'Check the individual product API endpoints',
        'Verify the admin panel edit form'
      ]
    })

  } catch (error: any) {
    console.error('❌ Migration failed:', error)
    return NextResponse.json({
      success: false,
      error: error.message,
      details: 'Failed to add stock management fields'
    }, { status: 500 })
  }
}
