import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import type { ProductSpecification } from "@/types"

interface ProductSpecificationsProps {
  specifications: ProductSpecification[]
}

export function ProductSpecifications({ specifications }: ProductSpecificationsProps) {
  if (!specifications || specifications.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Bu ürün için teknik özellik bilgisi bulunmamaktadır.</p>
      </div>
    )
  }

  // Group specifications by group
  const groupedSpecs = specifications.reduce(
    (acc, spec) => {
      const group = spec.group || "Genel"
      if (!acc[group]) {
        acc[group] = []
      }
      acc[group].push(spec)
      return acc
    },
    {} as Record<string, ProductSpecification[]>,
  )

  return (
    <div className="space-y-6">
      {Object.entries(groupedSpecs).map(([group, specs]) => (
        <Card key={group}>
          <CardHeader>
            <CardTitle className="text-lg">{group}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {specs
                .sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0))
                .map((spec, index) => (
                  <div
                    key={spec.id || index}
                    className="flex justify-between items-center py-2 border-b last:border-b-0"
                  >
                    <span className="font-medium text-gray-700">
                      {typeof spec.name === 'string' ? spec.name : JSON.stringify(spec.name)}
                    </span>
                    <span className="text-gray-900">
                      {typeof spec.value === 'string' ? spec.value : JSON.stringify(spec.value)}
                      {spec.unit && typeof spec.unit === 'string' && (
                        <span className="text-gray-500 ml-1">{spec.unit}</span>
                      )}
                    </span>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
