import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(amount: number, currency = "₺"): string {
  return new Intl.NumberFormat("tr-TR", {
    style: "currency",
    currency: "TRY",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount)
}

export function calculateDiscountPercentage(originalPrice: number, currentPrice: number): number {
  if (originalPrice <= 0 || currentPrice >= originalPrice) {
    return 0
  }
  return Math.round(((originalPrice - currentPrice) / originalPrice) * 100)
}

export async function fetcher<T>(url: string, options?: RequestInit): Promise<T> {
  // Server-side için tam URL oluştur
  const baseUrl = typeof window === 'undefined'
    ? process.env.NEXTAUTH_URL || 'http://localhost:3000'
    : ''

  const fullUrl = url.startsWith('http') ? url : `${baseUrl}${url}`

  const response = await fetch(fullUrl, options)
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: "Bilinmeyen hata" }))
    throw new Error(errorData.message || "Veri çekilirken hata oluştu")
  }
  return response.json()
}

export function slugify(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .trim()
}

export function calculatePagination(page: number, limit: number, total: number) {
  const totalPages = Math.ceil(total / limit)
  const startIndex = (page - 1) * limit
  const endIndex = startIndex + limit

  return {
    page,
    limit,
    total,
    totalPages,
    startIndex,
    endIndex,
    hasNext: page < totalPages,
    hasPrev: page > 1,
  }
}

// Debounce function for search and input handling
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout | null = null

  return (...args: Parameters<T>) => {
    if (timeoutId) {
      clearTimeout(timeoutId)
    }

    timeoutId = setTimeout(() => {
      func(...args)
    }, delay)
  }
}

// Date formatting utility
export function formatDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date

  return new Intl.DateTimeFormat('tr-TR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(dateObj)
}

// Stock status utilities
export function getStockStatusText(status: string): string {
  switch (status.toLowerCase()) {
    case 'in_stock':
      return 'Stokta'
    case 'out_of_stock':
      return 'Stok Yok'
    case 'low_stock':
      return 'Az Stok'
    default:
      return 'Bilinmiyor'
  }
}

export function getStockStatusColor(status: string): string {
  switch (status.toLowerCase()) {
    case 'in_stock':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'out_of_stock':
      return 'bg-red-100 text-red-800 border-red-200'
    case 'low_stock':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

// Number formatting utility
export function formatNumber(num: number): string {
  return new Intl.NumberFormat('tr-TR').format(num)
}
