"use client"
import { useEffect, useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"

import { DialogFooter } from "@/components/ui/dialog"

import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import ImageIcon from "@/components/ui/image-icon"
import type { Product, ProductImage } from "@/types"
import {
  formatCurrency,
  formatDate,
  formatNumber,
  getStockStatusText,
  getStockStatusColor,
  calculateDiscountPercentage,
} from "@/lib/utils"

interface ProductDetailModalProps {
  open: boolean
  onClose: () => void
  product: Product | null
  onEdit: () => void // Added for edit button
  onDelete: () => void // Added for delete button
}

export function ProductDetailModal({ open, onClose, product, onEdit, onDelete }: ProductDetailModalProps) {
  const [mainImage, setMainImage] = useState<ProductImage | null>(null)

  useEffect(() => {
    if (product && product.images && product.images.length > 0) {
      setMainImage(product.images.find((img) => img.isMain) || product.images[0])
    } else {
      setMainImage(null)
    }
  }, [product])

  if (!product) return null

  const stockStatus = getStockStatusText(
    product.stockQuantity <= 0 ? "out_of_stock" : product.stockQuantity <= (product.minStockThreshold || 0) ? "low_stock" : "in_stock",
  )
  const stockStatusColor = getStockStatusColor(
    product.stockQuantity <= 0 ? "out_of_stock" : product.stockQuantity <= (product.minStockThreshold || 0) ? "low_stock" : "in_stock",
  )
  const discountPercentage = product.originalPrice
    ? calculateDiscountPercentage(product.originalPrice, product.price)
    : 0

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{product.name}</DialogTitle>
          <DialogDescription>Ürün Detayları</DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-4">
          {/* Product Images */}
          <div className="flex flex-col items-center">
            <div className="w-full max-w-md aspect-square rounded-lg overflow-hidden border mb-4">
              {mainImage ? (
                <ImageIcon src={mainImage.url || "/placeholder.svg"} alt={mainImage.alt} className="w-full h-full" />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-gray-100 text-gray-400">
                  Görsel Yok
                </div>
              )}
            </div>
            {product.images && product.images.length > 1 && (
              <div className="flex gap-2 overflow-x-auto pb-2">
                {product.images.map((image) => (
                  <div
                    key={image.id}
                    className={`w-20 h-20 flex-shrink-0 rounded-md overflow-hidden border cursor-pointer ${
                      mainImage?.id === image.id ? "border-orange-500 ring-2 ring-orange-500" : ""
                    }`}
                    onClick={() => setMainImage(image)}
                  >
                    <ImageIcon src={image.url || "/placeholder.svg"} alt={image.alt} className="w-full h-full" />
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Product Details */}
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold">Kısa Açıklama</h4>
              <p className="text-gray-600">{product.shortDescription}</p>
            </div>
            {product.description && (
              <div>
                <h4 className="font-semibold">Detaylı Açıklama</h4>
                <p className="text-gray-600">{product.description}</p>
              </div>
            )}

            <Separator />

            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="font-semibold">SKU:</span> {product.sku}
              </div>
              {product.barcode && (
                <div>
                  <span className="font-semibold">Barkod:</span> {product.barcode}
                </div>
              )}
              <div>
                <span className="font-semibold">Kategori:</span> {product.category?.name || "N/A"}
              </div>
              <div>
                <span className="font-semibold">Marka:</span> {product.brand}
              </div>
              {product.model && (
                <div>
                  <span className="font-semibold">Model:</span> {product.model}
                </div>
              )}
            </div>

            <Separator />

            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="font-semibold">Satış Fiyatı:</span> {formatCurrency(product.price)}
              </div>
              {product.originalPrice && product.originalPrice > product.price && (
                <div>
                  <span className="font-semibold">Orijinal Fiyat:</span>{" "}
                  <span className="line-through text-gray-500">{formatCurrency(product.originalPrice)}</span>
                  {discountPercentage > 0 && (
                    <Badge variant="secondary" className="ml-2 bg-red-100 text-red-700">
                      %{discountPercentage} İndirim
                    </Badge>
                  )}
                </div>
              )}
              {product.costPrice && (
                <div>
                  <span className="font-semibold">Maliyet Fiyatı:</span> {formatCurrency(product.costPrice)}
                </div>
              )}
              <div>
                <span className="font-semibold">KDV Oranı:</span> %{product.taxRate}
              </div>
              <div>
                <span className="font-semibold">Stok Miktarı:</span> {formatNumber(product.stockQuantity || 0)}
              </div>
              <div>
                <span className="font-semibold">Minimum Stok:</span> {formatNumber(product.minStockThreshold || 0)}
              </div>
              <div>
                <span className="font-semibold">Stok Takibi:</span> {product.trackStock ? "Evet" : "Hayır"}
              </div>
              <div>
                <span className="font-semibold">Stok Durumu:</span>{" "}
                <Badge className={stockStatusColor}>{stockStatus}</Badge>
              </div>
            </div>

            <Separator />

            {(product.weight || product.dimensions) && (
              <>
                <h4 className="font-semibold">Boyut ve Ağırlık</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  {product.weight && (
                    <div>
                      <span className="font-semibold">Ağırlık:</span> {formatNumber(product.weight)} gr
                    </div>
                  )}
                  {product.dimensions?.length && (
                    <div>
                      <span className="font-semibold">Uzunluk:</span> {formatNumber(product.dimensions.length)} cm
                    </div>
                  )}
                  {product.dimensions?.width && (
                    <div>
                      <span className="font-semibold">Genişlik:</span> {formatNumber(product.dimensions.width)} cm
                    </div>
                  )}
                  {product.dimensions?.height && (
                    <div>
                      <span className="font-semibold">Yükseklik:</span> {formatNumber(product.dimensions.height)} cm
                    </div>
                  )}
                </div>
                <Separator />
              </>
            )}

            {product.specifications && product.specifications.length > 0 && (
              <>
                <h4 className="font-semibold">Teknik Özellikler</h4>
                <div className="space-y-2 text-sm">
                  {product.specifications.map((spec, index) => (
                    <div key={index} className="grid grid-cols-2">
                      <span className="font-medium">{spec.name}:</span>
                      <span>
                        {spec.value} {spec.unit}
                      </span>
                    </div>
                  ))}
                </div>
                <Separator />
              </>
            )}

            {product.certificates && product.certificates.length > 0 && (
              <>
                <h4 className="font-semibold">Sertifikalar</h4>
                <div className="space-y-2 text-sm">
                  {product.certificates.map((cert, index) => (
                    <div key={index} className="border p-3 rounded-md">
                      <p>
                        <span className="font-semibold">Adı:</span> {cert.name}
                      </p>
                      <p>
                        <span className="font-semibold">Veren Kurum:</span> {cert.issuer}
                      </p>
                      <p>
                        <span className="font-semibold">Sertifika No:</span> {cert.certificateNumber}
                      </p>
                      <p>
                        <span className="font-semibold">Veriliş Tarihi:</span> {formatDate(cert.issueDate)}
                      </p>
                      {cert.expiryDate && (
                        <p>
                          <span className="font-semibold">Bitiş Tarihi:</span> {formatDate(cert.expiryDate)}
                        </p>
                      )}
                      {cert.documentUrl && (
                        <p>
                          <span className="font-semibold">Belge:</span>{" "}
                          <a
                            href={cert.documentUrl}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline"
                          >
                            Görüntüle
                          </a>
                        </p>
                      )}
                      <p>
                        <span className="font-semibold">Aktif:</span> {cert.isActive ? "Evet" : "Hayır"}
                      </p>
                    </div>
                  ))}
                </div>
                <Separator />
              </>
            )}

            <h4 className="font-semibold">SEO Bilgileri</h4>
            <div className="space-y-2 text-sm">
              <div>
                <span className="font-semibold">SEO Başlık:</span> {product.seoTitle || "Belirtilmemiş"}
              </div>
              <div>
                <span className="font-semibold">SEO Açıklama:</span> {product.seoDescription || "Belirtilmemiş"}
              </div>
              <div>
                <span className="font-semibold">Meta Anahtar Kelimeler:</span>{" "}
                {product.metaKeywords && product.metaKeywords.length > 0
                  ? product.metaKeywords.join(", ")
                  : "Belirtilmemiş"}
              </div>
            </div>

            <Separator />

            <h4 className="font-semibold">Durum Bayrakları</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span className="font-semibold">Aktif:</span> {product.isActive ? "Evet" : "Hayır"}
              </div>
              <div>
                <span className="font-semibold">Öne Çıkan:</span> {product.isFeatured ? "Evet" : "Hayır"}
              </div>
              <div>
                <span className="font-semibold">Yeni Ürün:</span> {product.isNew ? "Evet" : "Hayır"}
              </div>
              <div>
                <span className="font-semibold">İndirimde:</span> {product.isOnSale ? "Evet" : "Hayır"}
              </div>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Kapat
          </Button>
          <Button onClick={onEdit}>Düzenle</Button>
          <Button variant="destructive" onClick={onDelete}>
            Sil
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
