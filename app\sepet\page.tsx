"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { CartItem } from "@/components/cart/cart-item"
import { useCartStore } from "@/lib/stores/cart-store"
import { formatCurrency } from "@/lib/utils"
import { ShoppingCart, ArrowLeft, ArrowRight } from "lucide-react"
import Link from "next/link"

export default function CartPage() {
  const { items, getTotalItems, getTotalPrice, clearCart } = useCartStore()

  const totalItems = getTotalItems()
  const totalPrice = getTotalPrice()
  const shippingThreshold = 500
  const shippingCost = totalPrice >= shippingThreshold ? 0 : 29.99
  const finalTotal = totalPrice + shippingCost

  if (items.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto text-center">
            <ShoppingCart className="w-24 h-24 text-gray-300 mx-auto mb-6" />
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Sepetiniz Boş</h1>
            <p className="text-gray-600 mb-8">Alışverişe başlamak için ürünlerimizi inceleyin</p>
            <Button asChild size="lg">
              <Link href="/urunler">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Ürünleri İncele
              </Link>
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Sepetim</h1>
          <p className="text-gray-600">{totalItems} ürün</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Ürünler ({totalItems})</span>
                  <Button variant="outline" size="sm" onClick={clearCart}>
                    Sepeti Temizle
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {items.map((item) => (
                  <div key={item.id}>
                    <CartItem item={item} />
                    <Separator className="mt-4" />
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle>Sipariş Özeti</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span>Ara Toplam:</span>
                  <span>{formatCurrency(totalPrice)}</span>
                </div>

                <div className="flex justify-between">
                  <span>Kargo:</span>
                  <span className={shippingCost === 0 ? "text-green-600" : ""}>
                    {shippingCost === 0 ? "Ücretsiz" : formatCurrency(shippingCost)}
                  </span>
                </div>

                {totalPrice < shippingThreshold && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <p className="text-sm text-blue-800">
                      {formatCurrency(shippingThreshold - totalPrice)} daha alışveriş yapın, kargo ücretsiz olsun!
                    </p>
                  </div>
                )}

                <Separator />

                <div className="flex justify-between text-lg font-bold">
                  <span>Toplam:</span>
                  <span>{formatCurrency(finalTotal)}</span>
                </div>

                <Button className="w-full" size="lg">
                  Ödemeye Geç
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>

                <Button variant="outline" className="w-full bg-transparent" asChild>
                  <Link href="/urunler">
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Alışverişe Devam Et
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
