import { getProducts } from "@/lib/api/products"
import ProductCard from "./product-card"
import ProductPagination from "./product-pagination"
import { AlertCircle } from "lucide-react"

interface ProductGridProps {
  searchParams: {
    search?: string
    category?: string
    brand?: string
    minPrice?: string
    maxPrice?: string
    inStock?: string
    sort?: string
    page?: string
  }
}

export default async function ProductGrid({ searchParams }: ProductGridProps) {
  try {
    // Parse sort parameter
    const sortParts = searchParams.sort?.split("-") || ["createdAt", "desc"]
    const sortBy = sortParts[0] || "createdAt"
    const sortOrder = (sortParts[1] as "asc" | "desc") || "desc"

    const filters = {
      search: searchParams.search,
      categoryId: searchParams.category,
      brand: searchParams.brand,
      minPrice: searchParams.minPrice ? Number.parseFloat(searchParams.minPrice) : undefined,
      maxPrice: searchParams.maxPrice ? Number.parseFloat(searchParams.maxPrice) : undefined,
      inStock: searchParams.inStock === "true" ? true : searchParams.inStock === "false" ? false : undefined,
      sortBy,
      sortOrder,
      page: Number.parseInt(searchParams.page || "1"),
      limit: 12,
      isActive: true, // Only show active products on frontend
    }

    const result = await getProducts(filters)

    // API response format: { success: true, data: { data: [...], pagination: {...} } }
    const { data: products, pagination } = result.data || result

    if (!products || products.length === 0) {
      return (
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Ürün bulunamadı</h3>
          <p className="text-gray-600">
            Arama kriterlerinize uygun ürün bulunamadı. Filtrelerinizi değiştirmeyi deneyin.
          </p>
        </div>
      )
    }

    return (
      <div>
        {/* Ürün Sayısı */}
        <div className="mb-6">
          <p className="text-sm text-gray-600">
            {pagination?.total || products.length} ürün bulundu
            {searchParams.search && <span> - "{searchParams.search}" için sonuçlar</span>}
          </p>
        </div>

        {/* Ürün Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
          {products.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>

        {/* Pagination */}
        {pagination && pagination.totalPages > 1 && (
          <ProductPagination
            currentPage={pagination.page}
            totalPages={pagination.totalPages}
            hasNext={pagination.hasNext}
            hasPrev={pagination.hasPrev}
          />
        )}
      </div>
    )
  } catch (error) {
    console.error("Product grid error:", error)
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-12 w-12 text-red-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Hata oluştu</h3>
        <p className="text-gray-600">Ürünler yüklenirken bir hata oluştu. Lütfen sayfayı yenileyin.</p>
        <details className="mt-4 text-left max-w-md mx-auto">
          <summary className="cursor-pointer text-sm text-gray-500">Hata detayları</summary>
          <pre className="mt-2 text-xs text-gray-400 bg-gray-50 p-2 rounded overflow-auto">
            {error instanceof Error ? error.message : String(error)}
          </pre>
        </details>
      </div>
    )
  }
}
