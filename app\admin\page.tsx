import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Package, FolderTree, ShoppingCart, Users, TrendingUp, AlertTriangle } from "lucide-react"
import { DevModeAlert } from "@/components/dev-mode-indicator"

const stats = [
  {
    title: "Toplam Ürün",
    value: "1,234",
    change: "+12%",
    changeType: "positive" as const,
    icon: Package,
  },
  {
    title: "Kategoriler",
    value: "45",
    change: "+3",
    changeType: "positive" as const,
    icon: FolderTree,
  },
  {
    title: "Bekleyen Siparişler",
    value: "23",
    change: "-5%",
    changeType: "negative" as const,
    icon: ShoppingCart,
  },
  {
    title: "Aktif <PERSON>üş<PERSON>iler",
    value: "892",
    change: "+18%",
    changeType: "positive" as const,
    icon: Users,
  },
]

const recentActivities = [
  {
    id: 1,
    type: "product",
    message: "<PERSON><PERSON>r<PERSON>n eklendi: 3M Peltor X5A Kulaklık",
    time: "2 dakika önce",
  },
  {
    id: 2,
    type: "order",
    message: "Yeni sipariş alındı: #12345",
    time: "15 dakika önce",
  },
  {
    id: 3,
    type: "category",
    message: "Kategori güncellendi: Baş Koruma",
    time: "1 saat önce",
  },
  {
    id: 4,
    type: "stock",
    message: "Stok uyarısı: Uvex Pheos CX2 Gözlük",
    time: "2 saat önce",
  },
]

const lowStockProducts = [
  {
    id: 1,
    name: "Uvex Pheos CX2 Güvenlik Gözlüğü",
    stock: 5,
    minStock: 20,
  },
  {
    id: 2,
    name: "Ansell HyFlex 11-801 Eldiven",
    stock: 12,
    minStock: 50,
  },
  {
    id: 3,
    name: "MSA V-Gard Baret",
    stock: 8,
    minStock: 30,
  },
]

export default function AdminDashboard() {
  return (
    <div className="space-y-6">
      {/* Development Mode Alert */}
      <DevModeAlert />

      {/* Page header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Gösterge Paneli</h1>
        <p className="text-gray-600 mt-2">İş güvenliği malzemeleri e-ticaret yönetim paneli</p>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => {
          const Icon = stat.icon
          return (
            <Card key={stat.title}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-3xl font-bold text-gray-900 mt-2">{stat.value}</p>
                    <p className={`text-sm mt-1 ${stat.changeType === "positive" ? "text-green-600" : "text-red-600"}`}>
                      {stat.change} geçen aydan
                    </p>
                  </div>
                  <div className="bg-orange-50 p-3 rounded-lg">
                    <Icon className="h-6 w-6 text-orange-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent activities */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Son Aktiviteler
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start gap-3 p-3 rounded-lg hover:bg-gray-50">
                  <div className="bg-orange-100 p-2 rounded-full">
                    {(() => {
                      switch (activity.type) {
                        case "product":
                          return <Package className="h-4 w-4 text-orange-600" />
                        case "order":
                          return <ShoppingCart className="h-4 w-4 text-orange-600" />
                        case "category":
                          return <FolderTree className="h-4 w-4 text-orange-600" />
                        case "stock":
                          return <AlertTriangle className="h-4 w-4 text-orange-600" />
                        default:
                          return null
                      }
                    })()}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{activity.message}</p>
                    <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Low stock alerts */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
              Düşük Stok Uyarıları
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {lowStockProducts.map((product) => (
                <div
                  key={product.id}
                  className="flex items-center justify-between p-3 rounded-lg border border-yellow-200 bg-yellow-50"
                >
                  <div>
                    <p className="text-sm font-medium text-gray-900">{product.name}</p>
                    <p className="text-xs text-gray-500 mt-1">
                      Mevcut: {product.stock} | Minimum: {product.minStock}
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-bold text-yellow-600">{product.stock}</div>
                    <div className="text-xs text-gray-500">adet</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
