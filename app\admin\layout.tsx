"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useSearchParams } from "next/navigation"
import { useSession, signOut } from "next-auth/react"
import { ErrorBoundary } from "@/components/error-boundary"
import { DevModeIndicator, DevModeAlert } from "@/components/dev-mode-indicator"
import {
  LayoutDashboard,
  Package,
  FolderTree,
  Users,
  ShoppingCart,
  BarChart3,
  Settings,
  Menu,
  X,
  Bell,
  Search,
  User,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"
import { Suspense } from "react"

const sidebarItems = [
  {
    title: "Gösterge Paneli",
    href: "/admin",
    icon: LayoutDashboard,
  },
  {
    title: "Kategoriler",
    href: "/admin/kategoriler",
    icon: FolderTree,
  },
  {
    title: "Ürünler",
    href: "/admin/urunler",
    icon: Package,
  },
  {
    title: "Siparişler",
    href: "/admin/siparisler",
    icon: ShoppingCart,
    badge: "12",
  },
  {
    title: "Müşteriler",
    href: "/admin/musteriler",
    icon: Users,
  },
  {
    title: "Raporlar",
    href: "/admin/raporlar",
    icon: BarChart3,
  },
  {
    title: "Ayarlar",
    href: "/admin/ayarlar",
    icon: Settings,
  },
]

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const { data: session, status } = useSession()

  // ⚠️ DEVELOPMENT ONLY: Skip authentication in development mode
  // This is for development convenience only and should NEVER be used in production
  const SKIP_AUTH_IN_DEV = process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_SKIP_AUTH === 'true'

  // 🚨 DEVELOPMENT BYPASS: Create mock session when auth is skipped
  const mockSession = SKIP_AUTH_IN_DEV ? {
    user: {
      id: 'dev-user-id',
      email: '<EMAIL>',
      name: 'Development User',
      role: 'ADMIN'
    }
  } : null

  const effectiveSession = SKIP_AUTH_IN_DEV ? mockSession : session
  const effectiveStatus = SKIP_AUTH_IN_DEV ? 'authenticated' : status

  // Show loading while checking authentication (skip in dev mode)
  if (!SKIP_AUTH_IN_DEV && effectiveStatus === "loading") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Yükleniyor...</p>
        </div>
      </div>
    )
  }

  // Redirect to login if not authenticated (skip in dev mode)
  if (!SKIP_AUTH_IN_DEV && effectiveStatus === "unauthenticated") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Giriş yapmanız gerekiyor...</p>
        </div>
      </div>
    )
  }

  const handleSignOut = async () => {
    // 🚨 DEVELOPMENT BYPASS: Show alert instead of actual signout in dev mode
    if (SKIP_AUTH_IN_DEV) {
      alert('🔓 Development Mode: Authentication is bypassed. Signout disabled.')
      return
    }
    await signOut({ callbackUrl: '/admin/login' })
  }

  return (
    <Suspense fallback={<div>Loading...</div>}>
      {/* Development Mode Indicator */}
      <DevModeIndicator />

      <div className="min-h-screen bg-gray-50" style={{ paddingTop: SKIP_AUTH_IN_DEV ? '40px' : '0' }}>
        {/* Mobile sidebar overlay */}
        {sidebarOpen && (
          <div className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden" onClick={() => setSidebarOpen(false)} />
        )}

        {/* Sidebar */}
        <div
          className={cn(
            "fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0",
            sidebarOpen ? "translate-x-0" : "-translate-x-full",
          )}
        >
          <div className="flex items-center justify-between h-16 px-6 border-b">
            <div className="flex items-center gap-2">
              <div className="bg-orange-500 text-white p-2 rounded-lg">
                <div className="w-6 h-6 flex items-center justify-center font-bold text-sm">İG</div>
              </div>
              <div>
                <div className="font-bold text-lg text-slate-900">Admin Panel</div>
              </div>
            </div>
            <Button variant="ghost" size="icon" className="lg:hidden" onClick={() => setSidebarOpen(false)}>
              <X className="h-5 w-5" />
            </Button>
          </div>

          <nav className="mt-6 px-3">
            <div className="space-y-1">
              {sidebarItems.map((item) => {
                const Icon = item.icon
                const isActive = pathname === item.href

                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      "flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors",
                      isActive
                        ? "bg-orange-50 text-orange-600 border-r-2 border-orange-600"
                        : "text-gray-600 hover:bg-gray-50 hover:text-gray-900",
                    )}
                    onClick={() => setSidebarOpen(false)}
                  >
                    <Icon className="h-5 w-5" />
                    <span className="flex-1">{item.title}</span>
                    {item.badge && (
                      <Badge variant="secondary" className="bg-orange-100 text-orange-600">
                        {item.badge}
                      </Badge>
                    )}
                  </Link>
                )
              })}
            </div>
          </nav>

          {/* User info at bottom */}
          <div className="absolute bottom-0 left-0 right-0 p-4 border-t bg-gray-50">
            <div className="flex items-center gap-3">
              <Avatar className="h-8 w-8">
                <AvatarImage src="/placeholder.svg" />
                <AvatarFallback>
                  {session?.user?.name?.charAt(0).toUpperCase() || 'A'}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-gray-900 truncate">
                  {effectiveSession?.user?.name || 'Admin User'}
                  {SKIP_AUTH_IN_DEV && <span className="text-orange-500 ml-1">(DEV)</span>}
                </div>
                <div className="text-xs text-gray-500 truncate">
                  {effectiveSession?.user?.email}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="lg:pl-64">
          {/* Top header */}
          <header className="bg-white shadow-sm border-b h-16 flex items-center justify-between px-6">
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="icon" className="lg:hidden" onClick={() => setSidebarOpen(true)}>
                <Menu className="h-5 w-5" />
              </Button>

              <div className="hidden md:block">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input placeholder="Ara..." className="pl-10 w-80" />
                </div>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <Button variant="ghost" size="icon" className="relative">
                <Bell className="h-5 w-5" />
                <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 bg-red-500">
                  3
                </Badge>
              </Button>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src="/placeholder.svg" />
                      <AvatarFallback>
                        {effectiveSession?.user?.name?.charAt(0).toUpperCase() || 'A'}
                      </AvatarFallback>
                    </Avatar>
                    <span className="hidden md:block">
                      {effectiveSession?.user?.name || 'Admin User'}
                      {SKIP_AUTH_IN_DEV && <span className="text-orange-500 ml-1">(DEV)</span>}
                    </span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem>
                    <User className="h-4 w-4 mr-2" />
                    Profil
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Settings className="h-4 w-4 mr-2" />
                    Ayarlar
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className="text-red-600 cursor-pointer"
                    onClick={handleSignOut}
                  >
                    Çıkış Yap
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </header>

          {/* Page content */}
          <main className="p-6">
            <ErrorBoundary>
              {children}
            </ErrorBoundary>
          </main>
        </div>
      </div>
    </Suspense>
  )
}
