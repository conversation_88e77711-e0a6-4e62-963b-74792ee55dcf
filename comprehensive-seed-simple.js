const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

// Stock scenarios for realistic distribution
const stockScenarios = [
  { name: "High Stock", stockQuantity: 85, minStockThreshold: 15, maxStockCapacity: 120, status: "IN_STOCK" },
  { name: "Normal Stock", stockQuantity: 45, minStockThreshold: 10, maxStockCapacity: 80, status: "IN_STOCK" },
  { name: "Low Stock", stockQuantity: 8, minStockThreshold: 10, maxStockCapacity: 60, status: "LOW_STOCK" },
  { name: "Critical Stock", stockQuantity: 3, minStockThreshold: 5, maxStockCapacity: 40, status: "LOW_STOCK" },
  { name: "Out of Stock", stockQuantity: 0, minStockThreshold: 5, maxStockCapacity: 50, status: "OUT_OF_STOCK" }
]

// Turkish brands for realistic product names
const turkishBrands = [
  "3M", "MSA", "Uvex", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>Clark", "<PERSON>lde<PERSON>",
  "Delta Plus", "J<PERSON>", "Portwest", "Co<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Hansen", "<PERSON>hartt"
]

// Product templates for each category
const productTemplates = {
  "Baş Koruma": [
    {
      name: "Profesyonel Güvenlik Bareti",
      description: "Yüksek darbe dayanımı ve UV koruması olan güvenlik bareti. İnşaat ve endüstriyel kullanım için ideal.",
      shortDescription: "Darbe dayanımlı güvenlik bareti",
      models: ["H-700", "V-Gard", "EVO3", "Pheos", "JSP MK8"],
      priceRange: { min: 45, max: 120 }
    },
    {
      name: "Elektrikçi Güvenlik Kaskı",
      description: "Elektriksel izolasyon özellikli güvenlik kaskı. 1000V'a kadar koruma sağlar.",
      shortDescription: "Elektriksel izolasyonlu kask",
      models: ["E-Man", "Electrical", "Volt-Guard", "Iso-Safe"],
      priceRange: { min: 85, max: 180 }
    }
  ],
  "Göz Koruma": [
    {
      name: "Güvenlik Gözlüğü",
      description: "Çarpma ve kimyasal sıçramaya karşı koruma sağlayan güvenlik gözlüğü.",
      shortDescription: "Çok amaçlı güvenlik gözlüğü",
      models: ["CX2", "Sportstyle", "Pheos", "I-Vo", "Genesis"],
      priceRange: { min: 25, max: 85 }
    }
  ],
  "Kulak Koruma": [
    {
      name: "Gürültü Önleyici Kulaklık",
      description: "Yüksek gürültü seviyelerine karşı etkili koruma sağlayan kulaklık. Ergonomik tasarım.",
      shortDescription: "Profesyonel gürültü koruması",
      models: ["X5A", "Optime III", "H540A", "Clarity", "Thunder"],
      priceRange: { min: 35, max: 150 }
    }
  ],
  "Solunum Koruma": [
    {
      name: "Toz Maskesi",
      description: "Zararlı toz ve partiküllere karşı koruma sağlayan filtreli maske.",
      shortDescription: "Partikül filtreli maske",
      models: ["FFP2", "FFP3", "N95", "P2", "P3"],
      priceRange: { min: 8, max: 45 }
    }
  ],
  "El Koruma": [
    {
      name: "İş Eldiveni",
      description: "Genel amaçlı iş eldiveni. Kesik ve aşınma koruması sağlar.",
      shortDescription: "Çok amaçlı iş eldiveni",
      models: ["HyFlex", "PowerFlex", "Sensilite", "Activarmr", "Grip"],
      priceRange: { min: 12, max: 65 }
    }
  ],
  "Ayak Koruma": [
    {
      name: "Güvenlik Ayakkabısı",
      description: "Çelik burunlu güvenlik ayakkabısı. Su geçirmez ve anti-slip taban.",
      shortDescription: "Çelik burunlu iş ayakkabısı",
      models: ["S3", "S1P", "S2", "Safety", "Work"],
      priceRange: { min: 120, max: 350 }
    }
  ],
  "Vücut Koruma": [
    {
      name: "Koruyucu Tulum",
      description: "Kimyasal ve partikül koruması sağlayan tek kullanımlık tulum.",
      shortDescription: "Kimyasal koruyucu tulum",
      models: ["Tyvek", "Tychem", "Microgard", "ProShield", "Barrier"],
      priceRange: { min: 15, max: 85 }
    }
  ],
  "Yüksekte Çalışma": [
    {
      name: "Emniyet Kemeri",
      description: "Yüksekte çalışma için tam vücut emniyet kemeri. Çoklu bağlantı noktası.",
      shortDescription: "Tam vücut emniyet kemeri",
      models: ["Miller", "Titan", "ExoFit", "Form", "Revolution"],
      priceRange: { min: 150, max: 400 }
    }
  ]
}

// Utility functions
function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

function getRandomPrice(min, max) {
  return Math.round((Math.random() * (max - min) + min) * 100) / 100
}

function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)]
}

async function main() {
  console.log('🌱 Starting comprehensive database seeding...')
  console.log('')

  try {
    // Step 1: Check database schema
    console.log('🔍 Step 1: Checking database schema...')
    try {
      await prisma.product.findFirst({
        select: {
          stockQuantity: true,
          minStockThreshold: true,
          maxStockCapacity: true,
          stockStatus: true
        }
      })
      console.log('✅ Stock management fields exist')
    } catch (error) {
      console.log('⚠️  Stock management fields missing')
      console.log('💡 Please run: npx prisma db push')
      console.log('   Or execute the migration SQL manually')
      return
    }

    // Step 2: Get categories
    console.log('📂 Step 2: Loading categories...')
    const categories = await prisma.category.findMany({
      where: { isActive: true },
      orderBy: { sortOrder: 'asc' }
    })

    if (categories.length === 0) {
      console.log('❌ No categories found. Please seed categories first.')
      return
    }

    console.log(`✅ Found ${categories.length} categories`)
    categories.forEach(cat => {
      console.log(`   - ${cat.name}`)
    })

    // Step 3: Clear existing products
    console.log('\n🧹 Step 3: Clearing existing products...')
    const existingCount = await prisma.product.count()
    
    if (existingCount > 0) {
      console.log(`⚠️  Found ${existingCount} existing products, clearing...`)
      await prisma.productImage.deleteMany()
      await prisma.productSpecification.deleteMany()
      await prisma.productCertificate.deleteMany()
      await prisma.product.deleteMany()
      console.log('✅ Existing products cleared')
    } else {
      console.log('✅ No existing products found')
    }

    // Step 4: Create products
    console.log('\n🛍️  Step 4: Creating products...')
    let totalProducts = 0
    const stockStats = { IN_STOCK: 0, LOW_STOCK: 0, OUT_OF_STOCK: 0 }

    for (const category of categories) {
      console.log(`\n📦 Creating products for: ${category.name}`)
      
      const templates = productTemplates[category.name]
      if (!templates) {
        console.log(`⚠️  No templates found for category: ${category.name}`)
        continue
      }

      // Create 10-12 products per category
      const productsToCreate = getRandomInt(10, 12)
      let categoryProductCount = 0

      for (let i = 0; i < productsToCreate; i++) {
        try {
          const template = getRandomElement(templates)
          const brand = getRandomElement(turkishBrands)
          const model = getRandomElement(template.models)
          const stockScenario = getRandomElement(stockScenarios)
          
          // Generate product data
          const basePrice = getRandomPrice(template.priceRange.min, template.priceRange.max)
          const costPrice = basePrice * 0.6 // 40% margin
          const productName = `${brand} ${template.name} ${model}`
          const slug = productName.toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim()

          // Create product
          const product = await prisma.product.create({
            data: {
              name: productName,
              slug: `${slug}-${getRandomInt(100, 999)}`,
              description: template.description,
              shortDescription: template.shortDescription,
              categoryId: category.id,
              brand: brand,
              model: model,

              // Pricing
              basePrice: basePrice,
              baseCostPrice: costPrice,
              taxRate: 20,
              currency: "TRY",

              // Stock Management - Using new fields
              trackStock: true,
              stockQuantity: stockScenario.stockQuantity,
              minStockThreshold: stockScenario.minStockThreshold,
              maxStockCapacity: stockScenario.maxStockCapacity,
              stockStatus: stockScenario.status,
              reservedStock: 0,

              // Product status
              isActive: true,
              isFeatured: Math.random() < 0.3, // 30% featured
              isNew: Math.random() < 0.2, // 20% new
              isOnSale: Math.random() < 0.25, // 25% on sale

              // SEO
              seoTitle: `${productName} - ${brand} | İş Güvenliği`,
              seoDescription: `${template.shortDescription}. ${brand} kalitesi ile güvenilir koruma. Hızlı teslimat ve uygun fiyat.`,

              // Physical properties
              weight: getRandomPrice(0.1, 3.0),

              publishedAt: new Date()
            }
          })

          // Create product image
          await prisma.productImage.create({
            data: {
              productId: product.id,
              url: `/placeholder.svg?height=400&width=400&text=${encodeURIComponent(productName)}`,
              alt: `${productName}`,
              title: `${productName}`,
              sortOrder: 0,
              isMain: true,
              width: 400,
              height: 400,
              format: "SVG"
            }
          })

          // Update statistics
          totalProducts++
          categoryProductCount++
          stockStats[stockScenario.status]++

          console.log(`  ✅ ${categoryProductCount}. ${productName} (${stockScenario.name})`)

        } catch (error) {
          console.error(`  ❌ Failed to create product ${i + 1}:`, error.message)
        }
      }

      console.log(`✅ Created ${categoryProductCount} products for ${category.name}`)
    }

    // Step 5: Update category product counts
    console.log('\n📊 Step 5: Updating category counts...')
    for (const category of categories) {
      const productCount = await prisma.product.count({
        where: { categoryId: category.id }
      })
      
      await prisma.category.update({
        where: { id: category.id },
        data: { productCount }
      })
    }

    // Final summary
    console.log('\n' + '='.repeat(50))
    console.log('✅ Seeding completed successfully!')
    console.log('')
    console.log('📊 Summary:')
    console.log(`  Categories: ${categories.length}`)
    console.log(`  Products created: ${totalProducts}`)
    console.log(`  Products per category: ${Math.round(totalProducts / categories.length)}`)
    console.log('')
    console.log('🎯 Stock Distribution:')
    console.log(`  In Stock: ${stockStats.IN_STOCK} products`)
    console.log(`  Low Stock: ${stockStats.LOW_STOCK} products`)
    console.log(`  Out of Stock: ${stockStats.OUT_OF_STOCK} products`)
    console.log('')
    console.log('🎉 Stock data inconsistency issue should now be resolved!')
    console.log('💡 Both product detail page and edit form will show consistent stock data.')

  } catch (error) {
    console.error('❌ Seeding failed:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

main()
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
