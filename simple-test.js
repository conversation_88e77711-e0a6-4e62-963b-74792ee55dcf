console.log('🧪 Simple test script running...')
console.log('✅ Node.js is working')
console.log('📅 Current time:', new Date().toISOString())

const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function quickTest() {
  try {
    console.log('🔍 Testing database connection...')
    
    // Test basic connection
    await prisma.$connect()
    console.log('✅ Database connected')
    
    // Count categories
    const categoryCount = await prisma.category.count()
    console.log(`📂 Categories: ${categoryCount}`)
    
    // Count products
    const productCount = await prisma.product.count()
    console.log(`📦 Products: ${productCount}`)
    
    // Test stock fields
    try {
      const testProduct = await prisma.product.findFirst({
        select: {
          id: true,
          name: true,
          stockQuantity: true,
          minStockThreshold: true,
          stockStatus: true
        }
      })
      
      if (testProduct) {
        console.log('✅ Stock fields accessible')
        console.log(`📊 Sample: ${testProduct.name} - Stock: ${testProduct.stockQuantity}`)
      } else {
        console.log('⚠️  No products found to test')
      }
    } catch (error) {
      console.log('❌ Stock fields not accessible:', error.message)
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
  } finally {
    await prisma.$disconnect()
    console.log('🔌 Database disconnected')
  }
}

quickTest()
