# LLM Integration Guide

## Overview

This guide shows how to integrate your LLM service with the SEO Generation system. The system is designed to work with any LLM provider - you just need to implement the `callLLMApi` function in the API route.

## Integration Steps

### 1. Locate the Integration Point

Open `app/api/seo/generate/route.ts` and find the `callLLMApi` function:

```typescript
// LLM API call function - TO BE IMPLEMENTED BY USER
async function callLLMApi(prompt: string): Promise<string> {
  // TODO: Replace this with your actual LLM API call
  throw new Error('LLM API not implemented. Please provide your LLM API call implementation.')
}
```

### 2. Replace with Your LLM Implementation

Choose your LLM provider and replace the function:

#### Option A: OpenAI GPT-4

```typescript
async function callLLMApi(prompt: string): Promise<string> {
  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
    },
    body: JSON.stringify({
      model: 'gpt-4',
      messages: [
        {
          role: 'system',
          content: 'You are an expert SEO specialist for Turkish e-commerce. Always return valid JSON responses.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2000,
    }),
  })

  if (!response.ok) {
    throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`)
  }

  const data = await response.json()
  
  if (!data.choices?.[0]?.message?.content) {
    throw new Error('No content received from OpenAI API')
  }

  return data.choices[0].message.content
}
```

#### Option B: Anthropic Claude

```typescript
async function callLLMApi(prompt: string): Promise<string> {
  const response = await fetch('https://api.anthropic.com/v1/messages', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': process.env.ANTHROPIC_API_KEY!,
      'anthropic-version': '2023-06-01'
    },
    body: JSON.stringify({
      model: 'claude-3-sonnet-20240229',
      max_tokens: 2000,
      messages: [
        {
          role: 'user',
          content: prompt
        }
      ]
    }),
  })

  if (!response.ok) {
    throw new Error(`Anthropic API error: ${response.status} ${response.statusText}`)
  }

  const data = await response.json()
  
  if (!data.content?.[0]?.text) {
    throw new Error('No content received from Anthropic API')
  }

  return data.content[0].text
}
```

#### Option C: Google Gemini

```typescript
async function callLLMApi(prompt: string): Promise<string> {
  const response = await fetch(
    `https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${process.env.GOOGLE_AI_API_KEY}`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.7,
          maxOutputTokens: 2000,
        }
      }),
    }
  )

  if (!response.ok) {
    throw new Error(`Google AI API error: ${response.status} ${response.statusText}`)
  }

  const data = await response.json()
  
  if (!data.candidates?.[0]?.content?.parts?.[0]?.text) {
    throw new Error('No content received from Google AI API')
  }

  return data.candidates[0].content.parts[0].text
}
```

#### Option D: Local LLM (Ollama)

```typescript
async function callLLMApi(prompt: string): Promise<string> {
  const response = await fetch('http://localhost:11434/api/generate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: 'llama2', // or your preferred model
      prompt: prompt,
      stream: false,
      options: {
        temperature: 0.7,
        num_predict: 2000,
      }
    }),
  })

  if (!response.ok) {
    throw new Error(`Ollama API error: ${response.status} ${response.statusText}`)
  }

  const data = await response.json()
  
  if (!data.response) {
    throw new Error('No content received from Ollama API')
  }

  return data.response
}
```

### 3. Set Environment Variables

Add your API key to `.env`:

```env
# For OpenAI
OPENAI_API_KEY="your-openai-api-key-here"

# For Anthropic
ANTHROPIC_API_KEY="your-anthropic-api-key-here"

# For Google AI
GOOGLE_AI_API_KEY="your-google-ai-api-key-here"

# Optional: SEO Generation Settings
SEO_FALLBACK_TO_LOCAL="true"
```

### 4. Test Your Integration

Run the test script to verify everything works:

```bash
# Test the API
node test-seo-generation.js

# Or test in the admin panel
npm run dev
# Go to http://localhost:3000/admin/urunler
# Click "Ürün Ekle" → "SEO" tab → "Otomatik SEO Değerleri Oluştur"
```

## Advanced Configuration

### Error Handling

The system includes comprehensive error handling:

- **LLM API Errors**: Automatically falls back to local generation
- **JSON Parsing Errors**: Attempts to extract JSON from response
- **Validation Errors**: Uses Zod schema validation
- **Rate Limiting**: Handles API rate limits gracefully

### Custom LLM Provider

For custom LLM providers, implement the function following this pattern:

```typescript
async function callLLMApi(prompt: string): Promise<string> {
  try {
    // 1. Make API call to your LLM service
    const response = await fetch('YOUR_LLM_ENDPOINT', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.YOUR_API_KEY}`,
        // Add any other required headers
      },
      body: JSON.stringify({
        // Format according to your LLM's API
        prompt: prompt,
        // Add other parameters as needed
      }),
    })

    // 2. Check response status
    if (!response.ok) {
      throw new Error(`LLM API error: ${response.status} ${response.statusText}`)
    }

    // 3. Parse response
    const data = await response.json()
    
    // 4. Extract text content (adjust path according to your API)
    const content = data.result || data.text || data.content
    
    if (!content) {
      throw new Error('No content received from LLM API')
    }

    // 5. Return the text response
    return content

  } catch (error) {
    console.error('LLM API call failed:', error)
    throw error
  }
}
```

### Performance Optimization

1. **Caching**: Consider caching responses for identical prompts
2. **Rate Limiting**: Implement request throttling if needed
3. **Timeout**: Add request timeouts for reliability
4. **Retry Logic**: Implement retry logic for transient failures

Example with timeout and retry:

```typescript
async function callLLMApi(prompt: string): Promise<string> {
  const maxRetries = 3
  const timeout = 30000 // 30 seconds
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), timeout)
      
      const response = await fetch('YOUR_LLM_ENDPOINT', {
        method: 'POST',
        headers: { /* your headers */ },
        body: JSON.stringify({ /* your payload */ }),
        signal: controller.signal
      })
      
      clearTimeout(timeoutId)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }
      
      const data = await response.json()
      return data.content // Adjust according to your API
      
    } catch (error) {
      console.error(`LLM API attempt ${attempt} failed:`, error)
      
      if (attempt === maxRetries) {
        throw error
      }
      
      // Wait before retry (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000))
    }
  }
  
  throw new Error('All retry attempts failed')
}
```

## Testing

### 1. API Status Check

```bash
curl http://localhost:3000/api/seo/generate
```

### 2. Full SEO Generation Test

```bash
curl -X POST http://localhost:3000/api/seo/generate \
  -H "Content-Type: application/json" \
  -d '{
    "productName": "Test Product",
    "productDescription": "Test description for SEO generation",
    "category": "Test Category",
    "brand": "Test Brand",
    "price": 100
  }'
```

### 3. Admin Panel Test

1. Start development server: `npm run dev`
2. Go to: `http://localhost:3000/admin/urunler`
3. Click "Ürün Ekle"
4. Fill in product name and description
5. Go to "SEO" tab
6. Click "Otomatik SEO Değerleri Oluştur"

## Troubleshooting

### Common Issues

1. **"LLM API not implemented"**: Replace the placeholder function with your LLM implementation
2. **API Key errors**: Check your environment variables
3. **JSON parsing errors**: Ensure your LLM returns valid JSON
4. **Rate limiting**: Implement request throttling or upgrade your API plan
5. **Timeout errors**: Increase timeout or optimize your prompt

### Debug Mode

Enable detailed logging by setting:

```env
NODE_ENV="development"
```

This will show detailed logs in the console for debugging.

## Support

If you encounter issues:

1. Check the console logs for detailed error messages
2. Verify your API key is correctly set
3. Test your LLM API independently
4. Check the network connectivity
5. Review the prompt format and response structure

The system is designed to be robust with fallback mechanisms, so even if the LLM fails, users can still generate SEO content using the local fallback system.
