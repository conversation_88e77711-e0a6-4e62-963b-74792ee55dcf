"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { BannerCarousel } from "@/components/ui/banner-carousel"
import { Play, Pause, RotateCcw, Smartphone, Monitor, Tablet } from "lucide-react"
import type { CategoryBanner, DeviceType } from "@/types"
import { cn } from "@/lib/utils"

interface BannerPreviewModalProps {
  banners: CategoryBanner[]
  open: boolean
  onOpenChange: (open: boolean) => void
  initialBannerIndex?: number
}

export function BannerPreviewModal({
  banners,
  open,
  onOpenChange,
  initialBannerIndex = 0
}: BannerPreviewModalProps) {
  const [deviceType, setDeviceType] = useState<DeviceType>('DESKTOP')
  const [autoplay, setAutoplay] = useState(true)
  const [currentIndex, setCurrentIndex] = useState(initialBannerIndex)

  // Filter banners by device type
  const filteredBanners = banners.filter(banner => 
    banner.deviceType.includes(deviceType) && banner.isActive
  )

  // Create carousel items
  const carouselItems = filteredBanners.map(banner => ({
    id: banner.id,
    duration: banner.displayDuration,
    content: (
      <BannerPreviewItem
        banner={banner}
        deviceType={deviceType}
      />
    )
  }))

  // Get transition type from current banner
  const currentBanner = filteredBanners[currentIndex]
  const transitionType = currentBanner?.transitionType || 'FADE'

  // Device type options
  const deviceOptions = [
    { type: 'DESKTOP' as DeviceType, icon: Monitor, label: 'Desktop', width: 'w-full', height: 'h-80' },
    { type: 'TABLET' as DeviceType, icon: Tablet, label: 'Tablet', width: 'w-3/4', height: 'h-72' },
    { type: 'MOBILE' as DeviceType, icon: Smartphone, label: 'Mobile', width: 'w-80', height: 'h-64' }
  ]

  const currentDevice = deviceOptions.find(d => d.type === deviceType)!

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Banner Önizleme</span>
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                {filteredBanners.length} banner
              </Badge>
              <Badge variant="outline">
                {autoplay ? 'Otomatik' : 'Manuel'}
              </Badge>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Cihaz:</span>
              {deviceOptions.map(device => {
                const Icon = device.icon
                return (
                  <Button
                    key={device.type}
                    variant={deviceType === device.type ? "default" : "outline"}
                    size="sm"
                    onClick={() => setDeviceType(device.type)}
                    className="flex items-center gap-1"
                  >
                    <Icon className="w-3 h-3" />
                    {device.label}
                  </Button>
                )
              })}
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setAutoplay(!autoplay)}
                className="flex items-center gap-1"
              >
                {autoplay ? <Pause className="w-3 h-3" /> : <Play className="w-3 h-3" />}
                {autoplay ? 'Durdur' : 'Başlat'}
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentIndex(0)}
                className="flex items-center gap-1"
              >
                <RotateCcw className="w-3 h-3" />
                Sıfırla
              </Button>
            </div>
          </div>

          {/* Preview Container */}
          <div className="flex justify-center">
            <div className={cn(
              "mx-auto transition-all duration-300",
              currentDevice.width
            )}>
              {carouselItems.length > 0 ? (
                <BannerCarousel
                  items={carouselItems}
                  autoplay={autoplay}
                  showNavigation={true}
                  showPagination={true}
                  showPlayPause={true}
                  transitionType={transitionType}
                  onSlideChange={setCurrentIndex}
                  className={currentDevice.height}
                />
              ) : (
                <div className={cn(
                  "border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center",
                  currentDevice.height
                )}>
                  <div className="text-center text-gray-500">
                    <p className="font-medium">Bu cihaz için banner bulunamadı</p>
                    <p className="text-sm">
                      {deviceType} cihazı için aktif banner bulunmuyor
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Banner Info */}
          {filteredBanners.length > 0 && currentIndex < filteredBanners.length && (
            <BannerInfo banner={filteredBanners[currentIndex]} />
          )}

          {/* All Banners List */}
          <div>
            <h4 className="font-medium mb-3">Tüm Banner'lar ({deviceType})</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredBanners.map((banner, index) => (
                <div
                  key={banner.id}
                  className={cn(
                    "border rounded-lg p-3 cursor-pointer transition-colors",
                    index === currentIndex ? "border-blue-500 bg-blue-50" : "border-gray-200 hover:border-gray-300"
                  )}
                  onClick={() => setCurrentIndex(index)}
                >
                  <div className="flex items-center gap-3">
                    <img
                      src={banner.imageUrl}
                      alt={banner.imageAlt || banner.title || 'Banner'}
                      className="w-16 h-10 object-cover rounded"
                    />
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-sm truncate">
                        {banner.title || 'Başlıksız Banner'}
                      </p>
                      <p className="text-xs text-gray-500">
                        {banner.displayDuration}s • Sıra: {banner.displayOrder}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

// Banner preview item component
interface BannerPreviewItemProps {
  banner: CategoryBanner
  deviceType: DeviceType
}

function BannerPreviewItem({ banner, deviceType }: BannerPreviewItemProps) {
  const imageUrl = deviceType === 'MOBILE' && banner.mobileImageUrl 
    ? banner.mobileImageUrl 
    : banner.imageUrl

  return (
    <div 
      className="relative w-full h-full overflow-hidden rounded-lg"
      style={banner.backgroundColor ? { backgroundColor: banner.backgroundColor } : undefined}
    >
      {/* Background Image */}
      <img 
        src={imageUrl}
        alt={banner.imageAlt || banner.title || 'Banner'}
        className="w-full h-full object-cover"
      />
      
      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
      
      {/* Content */}
      <div className="absolute inset-0 flex items-end">
        <div className="p-4 md:p-6 text-white w-full" style={banner.textColor ? { color: banner.textColor } : undefined}>
          <div className="max-w-4xl">
            {banner.title && (
              <h2 className={cn(
                "font-bold mb-2 leading-tight",
                deviceType === 'MOBILE' ? "text-lg" : 
                deviceType === 'TABLET' ? "text-xl md:text-2xl" :
                "text-2xl md:text-3xl lg:text-4xl"
              )}>
                {banner.title}
              </h2>
            )}
            
            {banner.subtitle && (
              <h3 className={cn(
                "font-medium mb-2 opacity-90",
                deviceType === 'MOBILE' ? "text-sm" : "text-base md:text-lg"
              )}>
                {banner.subtitle}
              </h3>
            )}
            
            {banner.description && (
              <p className={cn(
                "opacity-80 mb-4 leading-relaxed",
                deviceType === 'MOBILE' ? "text-xs line-clamp-2" : "text-sm md:text-base"
              )}>
                {banner.description}
              </p>
            )}
            
            {/* CTA Button */}
            {banner.ctaText && banner.ctaUrl && (
              <button className={cn(
                "inline-flex items-center gap-2 bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white rounded-lg font-medium transition-colors duration-200",
                deviceType === 'MOBILE' ? "px-3 py-1 text-xs" : "px-4 py-2 md:px-6 md:py-3"
              )}>
                {banner.ctaText}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

// Banner info component
interface BannerInfoProps {
  banner: CategoryBanner
}

function BannerInfo({ banner }: BannerInfoProps) {
  return (
    <div className="bg-gray-50 rounded-lg p-4">
      <h4 className="font-medium mb-3">Banner Detayları</h4>
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div>
          <span className="text-gray-500">Başlık:</span>
          <p className="font-medium">{banner.title || 'Yok'}</p>
        </div>
        <div>
          <span className="text-gray-500">Süre:</span>
          <p className="font-medium">{banner.displayDuration}s</p>
        </div>
        <div>
          <span className="text-gray-500">Sıralama:</span>
          <p className="font-medium">{banner.displayOrder}</p>
        </div>
        <div>
          <span className="text-gray-500">Öncelik:</span>
          <p className="font-medium">{banner.priority}</p>
        </div>
        {banner.startDate && (
          <div>
            <span className="text-gray-500">Başlangıç:</span>
            <p className="font-medium">{new Date(banner.startDate).toLocaleDateString('tr-TR')}</p>
          </div>
        )}
        {banner.endDate && (
          <div>
            <span className="text-gray-500">Bitiş:</span>
            <p className="font-medium">{new Date(banner.endDate).toLocaleDateString('tr-TR')}</p>
          </div>
        )}
        <div>
          <span className="text-gray-500">Tıklama:</span>
          <p className="font-medium">{banner.clickCount}</p>
        </div>
        <div>
          <span className="text-gray-500">CTR:</span>
          <p className="font-medium">%{banner.ctr.toFixed(2)}</p>
        </div>
      </div>
    </div>
  )
}
