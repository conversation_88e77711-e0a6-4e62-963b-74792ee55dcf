export interface CategoryImageUploadResult {
  id: string
  originalName: string
  filename: string
  url: string
  alt: string
  title: string
  sortOrder: number
  isMain: boolean
  size: number
  width: number
  height: number
  format: string
  variants: CategoryImageVariant[]
}

export interface CategoryImageVariant {
  size: 'original' | 'thumbnail' | 'medium'
  filename: string
  url: string
  width: number
  height: number
  fileSize: number
}

// Upload category images to server
export async function uploadCategoryImages(files: File[]): Promise<CategoryImageUploadResult[]> {
  console.log('🔄 uploadCategoryImages: Starting upload', {
    filesCount: files.length,
    fileNames: files.map(f => f.name),
    fileSizes: files.map(f => f.size)
  })

  const formData = new FormData()

  files.forEach(file => {
    formData.append('files', file)
  })

  console.log('🔄 uploadCategoryImages: Sending request to /api/upload/categories')

  const response = await fetch('/api/upload/categories', {
    method: 'POST',
    body: formData,
  })

  console.log('🔄 uploadCategoryImages: Response received', {
    status: response.status,
    statusText: response.statusText,
    ok: response.ok
  })

  if (!response.ok) {
    const error = await response.json()
    console.error('❌ uploadCategoryImages: Upload failed', error)
    throw new Error(error.error || 'Upload failed')
  }

  const result = await response.json()
  console.log('✅ uploadCategoryImages: Upload successful', result)

  if (!result.success) {
    throw new Error(result.error || 'Upload failed')
  }

  return result.data
}

// Delete category image from server
export async function deleteCategoryImage(filename: string): Promise<void> {
  const response = await fetch(`/api/upload/categories?filename=${encodeURIComponent(filename)}`, {
    method: 'DELETE',
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.error || 'Delete failed')
  }
}

// Validate category image files before upload
export function validateCategoryImageFiles(files: File[]): { valid: boolean; errors: string[] } {
  const errors: string[] = []
  const maxSize = 5 * 1024 * 1024 // 5MB
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']

  if (files.length === 0) {
    errors.push('No files selected')
    return { valid: false, errors }
  }

  if (files.length > 1) {
    errors.push('Only one file allowed for category images')
  }

  for (const file of files) {
    if (!allowedTypes.includes(file.type)) {
      errors.push(`Invalid file type: ${file.type}. Allowed: JPG, PNG, WEBP`)
    }

    if (file.size > maxSize) {
      errors.push(`File too large: ${(file.size / 1024 / 1024).toFixed(2)}MB. Max: 5MB`)
    }

    if (file.name.length > 100) {
      errors.push('Filename too long (max 100 characters)')
    }
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

// Get category image URL with size variant
export function getCategoryImageUrl(imageUrl: string, size: 'original' | 'thumbnail' | 'medium' = 'original'): string {
  if (!imageUrl) return ''

  if (size === 'original') {
    return imageUrl
  }

  // Handle both /uploads/categories/ and /uploads/products/ paths
  let baseName = ''
  let basePath = ''

  if (imageUrl.includes('/uploads/categories/')) {
    baseName = imageUrl.replace(/\/uploads\/categories\//, '').replace(/-original\.jpg$/, '')
    basePath = '/uploads/categories/'
  } else if (imageUrl.includes('/uploads/products/')) {
    baseName = imageUrl.replace(/\/uploads\/products\//, '').replace(/-original\.jpg$/, '')
    basePath = '/uploads/products/' // Use products folder for now
  } else {
    // If no standard path, try to extract filename and use products path
    const filename = imageUrl.split('/').pop() || ''
    baseName = filename.replace(/-original\.jpg$/, '')
    basePath = '/uploads/products/'
  }

  // Map thumbnail to thumb for existing files
  const sizeMapping = {
    'thumbnail': 'thumb',
    'medium': 'medium',
    'original': 'original'
  }

  const mappedSize = sizeMapping[size] || size
  return `${basePath}${baseName}-${mappedSize}.jpg`
}

// Create category image placeholder
export function createCategoryImagePlaceholder(width: number = 800, height: number = 450): string {
  return `/placeholder.svg?height=${height}&width=${width}&text=Category+Image`
}

// Validate image dimensions for different category image types
export function validateImageDimensions(width: number, height: number, imageType: 'banner' | 'icon' | 'og'): { valid: boolean; message?: string } {
  switch (imageType) {
    case 'banner':
      // Banner images should be wide format
      if (width < 800 || height < 400) {
        return { valid: false, message: 'Banner resmi en az 800x400 piksel olmalıdır' }
      }
      if (width / height < 1.5) {
        return { valid: false, message: 'Banner resmi yatay format olmalıdır (en/boy oranı en az 1.5)' }
      }
      break
    
    case 'icon':
      // Icons should be square or close to square
      if (width < 100 || height < 100) {
        return { valid: false, message: 'İkon en az 100x100 piksel olmalıdır' }
      }
      if (Math.abs(width - height) > Math.min(width, height) * 0.2) {
        return { valid: false, message: 'İkon kare veya kareye yakın format olmalıdır' }
      }
      break
    
    case 'og':
      // OG images should be 1.91:1 ratio (1200x630 recommended)
      const idealRatio = 1200 / 630
      const actualRatio = width / height
      if (width < 600 || height < 315) {
        return { valid: false, message: 'OG resmi en az 600x315 piksel olmalıdır' }
      }
      if (Math.abs(actualRatio - idealRatio) > 0.2) {
        return { valid: false, message: 'OG resmi 1200x630 oranında olmalıdır (1.91:1)' }
      }
      break
  }

  return { valid: true }
}

// Extract filename from URL for deletion
export function extractFilenameFromUrl(url: string): string | null {
  const match = url.match(/\/uploads\/categories\/(.+)$/)
  return match ? match[1] : null
}

// Check if URL is a category image URL
export function isCategoryImageUrl(url: string): boolean {
  return url.includes('/uploads/categories/')
}

// Generate optimized image sizes for different use cases
export const CATEGORY_IMAGE_SIZES = {
  banner: {
    original: { width: 1920, height: 1080 },
    medium: { width: 800, height: 450 },
    thumbnail: { width: 300, height: 200 }
  },
  icon: {
    original: { width: 512, height: 512 },
    medium: { width: 256, height: 256 },
    thumbnail: { width: 64, height: 64 }
  },
  og: {
    original: { width: 1200, height: 630 },
    medium: { width: 800, height: 420 },
    thumbnail: { width: 400, height: 210 }
  }
} as const
