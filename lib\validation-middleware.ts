import { NextRequest, NextResponse } from 'next/server'
import { ZodSchema, ZodError } from 'zod'

export interface ValidationError {
  field: string
  message: string
}

export interface ApiError {
  success: false
  error: string
  errors?: ValidationError[]
  details?: string
}

export interface ApiSuccess<T = any> {
  success: true
  message?: string
  data?: T
}

export type ApiResponse<T = any> = ApiSuccess<T> | ApiError

// Format Zod errors into a more user-friendly format
export function formatZodErrors(error: ZodError): ValidationError[] {
  return error.errors.map((err) => ({
    field: err.path.join('.'),
    message: err.message
  }))
}

// Create standardized error response
export function createErrorResponse(
  error: string,
  status: number = 400,
  errors?: ValidationError[],
  details?: string
): NextResponse {
  const response: ApiError = {
    success: false,
    error,
    ...(errors && { errors }),
    ...(details && { details })
  }

  return NextResponse.json(response, { status })
}

// Create standardized success response
export function createSuccessResponse<T>(
  data?: T,
  message?: string,
  status: number = 200
): NextResponse {
  const response: ApiSuccess<T> = {
    success: true,
    ...(message && { message }),
    ...(data && { data })
  }

  return NextResponse.json(response, { status })
}

// Validate request body with Zod schema
export async function validateRequestBody<T>(
  request: NextRequest,
  schema: ZodSchema<T>
): Promise<{ success: true; data: T } | { success: false; response: NextResponse }> {
  try {
    const body = await request.json()
    const validatedData = schema.parse(body)
    
    return { success: true, data: validatedData }
  } catch (error) {
    if (error instanceof ZodError) {
      const validationErrors = formatZodErrors(error)
      return {
        success: false,
        response: createErrorResponse(
          'Validation failed',
          400,
          validationErrors
        )
      }
    }

    if (error instanceof SyntaxError) {
      return {
        success: false,
        response: createErrorResponse('Invalid JSON format', 400)
      }
    }

    return {
      success: false,
      response: createErrorResponse('Request validation failed', 400)
    }
  }
}

// Validate query parameters with Zod schema
export function validateQueryParams<T>(
  request: NextRequest,
  schema: ZodSchema<T>
): { success: true; data: T } | { success: false; response: NextResponse } {
  try {
    const { searchParams } = new URL(request.url)
    const params = Object.fromEntries(searchParams.entries())
    const validatedData = schema.parse(params)
    
    return { success: true, data: validatedData }
  } catch (error) {
    if (error instanceof ZodError) {
      const validationErrors = formatZodErrors(error)
      return {
        success: false,
        response: createErrorResponse(
          'Query parameter validation failed',
          400,
          validationErrors
        )
      }
    }

    return {
      success: false,
      response: createErrorResponse('Query parameter validation failed', 400)
    }
  }
}

// Validate form data with Zod schema
export async function validateFormData<T>(
  request: NextRequest,
  schema: ZodSchema<T>
): Promise<{ success: true; data: T } | { success: false; response: NextResponse }> {
  try {
    const formData = await request.formData()
    const data = Object.fromEntries(formData.entries())
    
    // Convert files to File objects if present
    const processedData: any = {}
    for (const [key, value] of Object.entries(data)) {
      if (value instanceof File) {
        processedData[key] = value
      } else if (key.endsWith('[]')) {
        // Handle array fields
        const arrayKey = key.slice(0, -2)
        if (!processedData[arrayKey]) {
          processedData[arrayKey] = []
        }
        processedData[arrayKey].push(value)
      } else {
        processedData[key] = value
      }
    }

    const validatedData = schema.parse(processedData)
    
    return { success: true, data: validatedData }
  } catch (error) {
    if (error instanceof ZodError) {
      const validationErrors = formatZodErrors(error)
      return {
        success: false,
        response: createErrorResponse(
          'Form data validation failed',
          400,
          validationErrors
        )
      }
    }

    return {
      success: false,
      response: createErrorResponse('Form data validation failed', 400)
    }
  }
}

// Higher-order function to wrap API handlers with validation
export function withValidation<TBody = any, TQuery = any>(
  handler: (
    request: NextRequest,
    context: any,
    validatedData: { body?: TBody; query?: TQuery }
  ) => Promise<NextResponse>,
  options: {
    bodySchema?: ZodSchema<TBody>
    querySchema?: ZodSchema<TQuery>
  } = {}
) {
  return async (request: NextRequest, context: any) => {
    try {
      const validatedData: { body?: TBody; query?: TQuery } = {}

      // Validate request body if schema provided
      if (options.bodySchema && (request.method === 'POST' || request.method === 'PUT' || request.method === 'PATCH')) {
        const bodyValidation = await validateRequestBody(request, options.bodySchema)
        if (!bodyValidation.success) {
          return bodyValidation.response
        }
        validatedData.body = bodyValidation.data
      }

      // Validate query parameters if schema provided
      if (options.querySchema) {
        const queryValidation = validateQueryParams(request, options.querySchema)
        if (!queryValidation.success) {
          return queryValidation.response
        }
        validatedData.query = queryValidation.data
      }

      return await handler(request, context, validatedData)
    } catch (error) {
      console.error('API Handler Error:', error)
      
      if (error instanceof Error) {
        return createErrorResponse(
          'Internal server error',
          500,
          undefined,
          process.env.NODE_ENV === 'development' ? error.message : undefined
        )
      }

      return createErrorResponse('Internal server error', 500)
    }
  }
}

// Utility function to handle async operations with error catching
export async function handleAsyncOperation<T>(
  operation: () => Promise<T>,
  errorMessage: string = 'Operation failed'
): Promise<{ success: true; data: T } | { success: false; error: string }> {
  try {
    const data = await operation()
    return { success: true, data }
  } catch (error) {
    console.error(`${errorMessage}:`, error)
    
    if (error instanceof Error) {
      return { success: false, error: error.message }
    }
    
    return { success: false, error: errorMessage }
  }
}
