'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  TrendingUp, 
  TrendingDown, 
  RotateCcw, 
  Search, 
  Filter,
  Calendar,
  User,
  FileText,
  ChevronLeft,
  ChevronRight,
  Loader2
} from 'lucide-react'
import { format } from 'date-fns'
import { tr } from 'date-fns/locale'

// Types
interface StockMovement {
  id: string
  movementType: 'in' | 'out' | 'adjustment'
  quantity: number
  previousStock: number
  newStock: number
  reason?: string
  reference?: string
  unitCost?: number
  supplier?: string
  notes?: string
  createdBy?: string
  createdAt: string
  product?: {
    id: string
    name: string
    slug: string
  }
}

interface StockHistoryTableProps {
  productId?: string
  className?: string
}

interface Filters {
  movementType: string
  reason: string
  startDate: string
  endDate: string
  search: string
}

const StockHistoryTable: React.FC<StockHistoryTableProps> = ({
  productId,
  className = ''
}) => {
  const [movements, setMovements] = useState<StockMovement[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1)
  const [totalCount, setTotalCount] = useState(0)
  const [hasMore, setHasMore] = useState(false)
  const itemsPerPage = 20

  // Filters
  const [filters, setFilters] = useState<Filters>({
    movementType: 'all',
    reason: 'all',
    startDate: '',
    endDate: '',
    search: ''
  })
  const [showFilters, setShowFilters] = useState(false)

  // Load stock movements
  const loadMovements = async (page: number = 1, resetData: boolean = true) => {
    setIsLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams({
        limit: itemsPerPage.toString(),
        offset: ((page - 1) * itemsPerPage).toString()
      })

      if (productId) params.append('productId', productId)
      if (filters.movementType && filters.movementType !== 'all') params.append('movementType', filters.movementType)
      if (filters.reason && filters.reason !== 'all') params.append('reason', filters.reason)
      if (filters.startDate) params.append('startDate', filters.startDate)
      if (filters.endDate) params.append('endDate', filters.endDate)

      const response = await fetch(`/api/stock-movements?${params}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Stok hareketleri yüklenemedi')
      }

      if (resetData) {
        setMovements(result.data)
      } else {
        setMovements(prev => [...prev, ...result.data])
      }

      setTotalCount(result.pagination.total)
      setHasMore(result.pagination.hasMore)
      setCurrentPage(page)

    } catch (error: any) {
      console.error('Load movements error:', error)
      setError(error.message)
    } finally {
      setIsLoading(false)
    }
  }

  // Initial load
  useEffect(() => {
    loadMovements(1, true)
  }, [productId, filters])

  // Get movement type info
  const getMovementTypeInfo = (type: string) => {
    switch (type) {
      case 'in':
        return { 
          icon: TrendingUp, 
          color: 'text-green-600', 
          bgColor: 'bg-green-100',
          label: 'Giriş',
          variant: 'success' as const
        }
      case 'out':
        return { 
          icon: TrendingDown, 
          color: 'text-red-600', 
          bgColor: 'bg-red-100',
          label: 'Çıkış',
          variant: 'destructive' as const
        }
      case 'adjustment':
        return { 
          icon: RotateCcw, 
          color: 'text-blue-600', 
          bgColor: 'bg-blue-100',
          label: 'Düzeltme',
          variant: 'secondary' as const
        }
      default:
        return { 
          icon: FileText, 
          color: 'text-gray-600', 
          bgColor: 'bg-gray-100',
          label: 'Bilinmiyor',
          variant: 'secondary' as const
        }
    }
  }

  // Get reason label
  const getReasonLabel = (reason?: string) => {
    const reasonLabels: Record<string, string> = {
      purchase: 'Satın Alma',
      sale: 'Satış',
      damage: 'Hasar/Fire',
      adjustment: 'Sayım Düzeltmesi',
      return: 'İade',
      transfer: 'Transfer'
    }
    return reason ? reasonLabels[reason] || reason : '-'
  }

  // Handle filter change
  const handleFilterChange = (key: keyof Filters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
    setCurrentPage(1)
  }

  // Clear filters
  const clearFilters = () => {
    setFilters({
      movementType: 'all',
      reason: 'all',
      startDate: '',
      endDate: '',
      search: ''
    })
    setCurrentPage(1)
  }

  // Load more (pagination)
  const loadMore = () => {
    if (!isLoading && hasMore) {
      loadMovements(currentPage + 1, false)
    }
  }

  // Go to specific page
  const goToPage = (page: number) => {
    if (page >= 1 && page <= Math.ceil(totalCount / itemsPerPage)) {
      loadMovements(page, true)
    }
  }

  const totalPages = Math.ceil(totalCount / itemsPerPage)

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Stok Hareketleri
            {totalCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {totalCount}
              </Badge>
            )}
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              Filtrele
            </Button>
          </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
            {/* Movement Type Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Hareket Tipi</label>
              <Select
                value={filters.movementType}
                onValueChange={(value) => handleFilterChange('movementType', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Tümü" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tümü</SelectItem>
                  <SelectItem value="in">Giriş</SelectItem>
                  <SelectItem value="out">Çıkış</SelectItem>
                  <SelectItem value="adjustment">Düzeltme</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Reason Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Sebep</label>
              <Select
                value={filters.reason}
                onValueChange={(value) => handleFilterChange('reason', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Tümü" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tümü</SelectItem>
                  <SelectItem value="purchase">Satın Alma</SelectItem>
                  <SelectItem value="sale">Satış</SelectItem>
                  <SelectItem value="damage">Hasar/Fire</SelectItem>
                  <SelectItem value="adjustment">Sayım Düzeltmesi</SelectItem>
                  <SelectItem value="return">İade</SelectItem>
                  <SelectItem value="transfer">Transfer</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Start Date */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Başlangıç Tarihi</label>
              <Input
                type="date"
                value={filters.startDate}
                onChange={(e) => handleFilterChange('startDate', e.target.value)}
              />
            </div>

            {/* End Date */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Bitiş Tarihi</label>
              <Input
                type="date"
                value={filters.endDate}
                onChange={(e) => handleFilterChange('endDate', e.target.value)}
              />
            </div>

            {/* Clear Filters */}
            <div className="md:col-span-2 lg:col-span-4 flex justify-end">
              <Button variant="ghost" size="sm" onClick={clearFilters}>
                Filtreleri Temizle
              </Button>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent>
        {error && (
          <div className="text-center py-8 text-red-600">
            <p>{error}</p>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => loadMovements(1, true)}
              className="mt-2"
            >
              Tekrar Dene
            </Button>
          </div>
        )}

        {!error && movements.length === 0 && !isLoading && (
          <div className="text-center py-8 text-gray-500">
            <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Henüz stok hareketi bulunmuyor</p>
          </div>
        )}

        {movements.length > 0 && (
          <>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Tarih</TableHead>
                    <TableHead>Tip</TableHead>
                    <TableHead>Miktar</TableHead>
                    <TableHead>Önceki</TableHead>
                    <TableHead>Yeni</TableHead>
                    <TableHead>Sebep</TableHead>
                    <TableHead>Referans</TableHead>
                    <TableHead>Notlar</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {movements.map((movement) => {
                    const typeInfo = getMovementTypeInfo(movement.movementType)
                    const TypeIcon = typeInfo.icon

                    return (
                      <TableRow key={movement.id}>
                        <TableCell className="font-medium">
                          <div className="flex flex-col">
                            <span className="text-sm">
                              {format(new Date(movement.createdAt), 'dd MMM yyyy', { locale: tr })}
                            </span>
                            <span className="text-xs text-gray-500">
                              {format(new Date(movement.createdAt), 'HH:mm')}
                            </span>
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <div className={`p-1 rounded ${typeInfo.bgColor}`}>
                              <TypeIcon className={`h-3 w-3 ${typeInfo.color}`} />
                            </div>
                            <Badge variant={typeInfo.variant} className="text-xs">
                              {typeInfo.label}
                            </Badge>
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          <span className={`font-medium ${
                            movement.quantity > 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {movement.quantity > 0 ? '+' : ''}{movement.quantity}
                          </span>
                        </TableCell>
                        
                        <TableCell className="text-gray-600">
                          {movement.previousStock}
                        </TableCell>
                        
                        <TableCell className="font-medium">
                          {movement.newStock}
                        </TableCell>
                        
                        <TableCell>
                          <span className="text-sm">
                            {getReasonLabel(movement.reason)}
                          </span>
                        </TableCell>
                        
                        <TableCell>
                          {movement.reference && (
                            <span className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                              {movement.reference}
                            </span>
                          )}
                        </TableCell>
                        
                        <TableCell>
                          {movement.notes && (
                            <span className="text-sm text-gray-600" title={movement.notes}>
                              {movement.notes.length > 30 
                                ? `${movement.notes.substring(0, 30)}...` 
                                : movement.notes}
                            </span>
                          )}
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-gray-500">
                  {totalCount} kayıttan {((currentPage - 1) * itemsPerPage) + 1}-{Math.min(currentPage * itemsPerPage, totalCount)} arası gösteriliyor
                </div>
                
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => goToPage(currentPage - 1)}
                    disabled={currentPage <= 1 || isLoading}
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  
                  <span className="text-sm">
                    Sayfa {currentPage} / {totalPages}
                  </span>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => goToPage(currentPage + 1)}
                    disabled={currentPage >= totalPages || isLoading}
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </>
        )}

        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>Yükleniyor...</span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default StockHistoryTable
